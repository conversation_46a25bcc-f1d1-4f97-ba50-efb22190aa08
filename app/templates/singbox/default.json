{"log": {"level": "warn", "timestamp": false}, "dns": {"servers": [{"tag": "dns-remote", "address": "*******", "detour": "proxy"}, {"tag": "dns-local", "address": "local", "detour": "direct"}], "rules": [{"outbound": "any", "server": "dns-local"}], "final": "dns-remote"}, "inbounds": [{"type": "tun", "tag": "tun-in", "interface_name": "sing-tun", "address": ["**********/30", "fdfe:dcba:9876::1/126"], "auto_route": true, "route_exclude_address": ["***********/16", "10.0.0.0/8", "***********/16", "**********/12", "fe80::/10", "fc00::/7"]}], "outbounds": [{"type": "selector", "tag": "proxy", "outbounds": null, "interrupt_exist_connections": true}, {"type": "urltest", "tag": "Best Latency", "outbounds": null}, {"type": "direct", "tag": "direct"}], "route": {"rules": [{"inbound": "tun-in", "action": "sniff"}, {"protocol": "dns", "action": "hijack-dns"}], "final": "proxy", "auto_detect_interface": true, "override_android_vpn": true}, "experimental": {"cache_file": {"enabled": true, "store_rdrc": true}}}
"""node api key

Revision ID: 53586547c10e
Revises: 58a5b64175a8
Create Date: 2025-04-13 22:03:43.772863

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '53586547c10e'
down_revision = '58a5b64175a8'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('nodes', sa.Column('api_key', sa.String(length=36), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('nodes', 'api_key')
    # ### end Alembic commands ###

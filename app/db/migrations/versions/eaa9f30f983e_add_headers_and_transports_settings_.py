"""add headers and transports_settings columns to hosts table

Revision ID: eaa9f30f983e
Revises: c5c734bd3da2
Create Date: 2025-02-26 14:20:14.371695

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'eaa9f30f983e'
down_revision = "c5c734bd3da2"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('hosts', sa.Column('http_headers', sa.JSON(none_as_null=True), nullable=True))
    op.add_column('hosts', sa.Column('transport_settings', sa.JSON(none_as_null=True), nullable=True))
    op.add_column('hosts', sa.Column('mux_settings', sa.JSO<PERSON>(none_as_null=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('hosts', 'mux_settings')
    op.drop_column('hosts', 'transport_settings')
    op.drop_column('hosts', 'http_headers')
    # ### end Alembic commands ###

"""add is_disabled to admins table

Revision ID: 931ed40d6eec
Revises: 7a93bcd44713
Create Date: 2025-01-12 08:00:38.731094

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision = '931ed40d6eec'
down_revision = '7a93bcd44713'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('admins', sa.Column('is_disabled', sa.<PERSON>(), server_default='0', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('admins', 'is_disabled')
    # ### end Alembic commands ###

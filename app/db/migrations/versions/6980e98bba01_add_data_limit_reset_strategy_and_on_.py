"""add data_limit_reset_strategy and on_hold_timeout to usertemplate table

Revision ID: 6980e98bba01
Revises: 9cfffef342cd
Create Date: 2025-04-14 21:38:13.835658

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '6980e98bba01'
down_revision = '9cfffef342cd'
branch_labels = None
depends_on = None

new_enum = sa.Enum('no_reset', 'day', 'week', 'month', 'year', name='userdatalimitresetstrategy')

def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_templates', sa.Column('on_hold_timeout', sa.Integer(), nullable=True))
    op.add_column('user_templates', sa.Column('data_limit_reset_strategy', new_enum, server_default='no_reset', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_templates', 'data_limit_reset_strategy')
    op.drop_column('user_templates', 'on_hold_timeout')
    # ### end Alembic commands ###
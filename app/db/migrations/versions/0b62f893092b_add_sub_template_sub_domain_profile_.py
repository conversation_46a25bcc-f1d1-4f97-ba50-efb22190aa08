"""add sub_template, sub_domain, profile_title and support_url to admin table

Revision ID: 0b62f893092b
Revises: 9aa6559916be
Create Date: 2025-03-08 17:12:10.205130

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0b62f893092b'
down_revision = '9aa6559916be'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('admins', sa.Column('sub_template', sa.String(length=1024), nullable=True))
    op.add_column('admins', sa.Column('sub_domain', sa.String(length=256), nullable=True))
    op.add_column('admins', sa.Column('profile_title', sa.String(length=512), nullable=True))
    op.add_column('admins', sa.Column('support_url', sa.String(length=1024), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('admins', 'support_url')
    op.drop_column('admins', 'profile_title')
    op.drop_column('admins', 'sub_domain')
    op.drop_column('admins', 'sub_template')
    # ### end Alembic commands ###

"""node stats table

Revision ID: 58a5b64175a8
Revises: 4e66033a2b9b
Create Date: 2025-04-10 13:36:20.215092

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '58a5b64175a8'
down_revision = '4e66033a2b9b'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('node_stats',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('node_id', sa.Integer(), nullable=False),
    sa.Column('mem_total', sa.BigInteger(), nullable=False),
    sa.Column('mem_used', sa.<PERSON>Integer(), nullable=False),
    sa.Column('cpu_cores', sa.Integer(), nullable=False),
    sa.Column('cpu_usage', sa.Float(), nullable=False),
    sa.Column('incoming_bandwidth_speed', sa.BigInteger(), nullable=False),
    sa.Column('outgoing_bandwidth_speed', sa.BigInteger(), nullable=False),
    sa.ForeignKeyConstraint(['node_id'], ['nodes.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('node_stats')
    # ### end Alembic commands ###

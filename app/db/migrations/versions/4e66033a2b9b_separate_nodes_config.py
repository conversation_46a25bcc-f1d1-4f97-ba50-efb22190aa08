"""separate nodes config

Revision ID: 4e66033a2b9b
Revises: 1ccf7b18b283
Create Date: 2025-04-08 17:39:03.984285

"""

from alembic import op
import sqlalchemy as sa
import comment<PERSON>son
from datetime import datetime as dt, timezone as tz
from decouple import config as decouple_config

from app.core.xray import XRayConfig


# revision identifiers, used by Alembic.
revision = "4e66033a2b9b"
down_revision = "1ccf7b18b283"
branch_labels = None
depends_on = None


base_xray = {
    "log": {"loglevel": "warning"},
    "routing": {"rules": [{"ip": ["geoip:private"], "outboundTag": "BLOCK", "type": "field"}]},
    "inbounds": [
        {
            "tag": "Shadowsocks TCP",
            "listen": "0.0.0.0",
            "port": 1080,
            "protocol": "shadowsocks",
            "settings": {"clients": [], "network": "tcp,udp"},
        }
    ],
    "outbounds": [{"protocol": "freedom", "tag": "DIRECT"}, {"protocol": "blackhole", "tag": "BLOCK"}],
}

def get_config(key, default=None, cast=None):
    if cast is not None:
        return decouple_config(key, default=default, cast=cast)
    else:
        return decouple_config(key, default=default)


XRAY_JSON = get_config("XRAY_JSON", default="./xray_config.json")
XRAY_FALLBACKS_INBOUND_TAGS = get_config(
    "XRAY_FALLBACKS_INBOUND_TAG", cast=lambda v: [tag.strip() for tag in v.split(",")] if v else [], default=""
)
XRAY_EXCLUDE_INBOUND_TAGS = get_config("XRAY_EXCLUDE_INBOUND_TAGS", default="").split()



def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "core_configs",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
        sa.Column("name", sa.String(length=256), nullable=False),
        sa.Column("config", sa.JSON(), nullable=False),
        sa.Column("exclude_inbound_tags", sa.String(length=2048), nullable=True),
        sa.Column("fallbacks_inbound_tags", sa.String(length=2048), nullable=True),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column("nodes", sa.Column("core_config_id", sa.Integer(), nullable=True))
    with op.batch_alter_table("nodes", schema=None) as batch_op:
        batch_op.create_foreign_key(
            "nodes_ibfk_1",
            "core_configs",
            ["core_config_id"],
            ["id"],
            ondelete="SET NULL"
        )
    # ### end Alembic commands ###
    try:
        with open(XRAY_JSON, "r") as file:
            config = commentjson.loads(file.read())

        XRayConfig(config, XRAY_EXCLUDE_INBOUND_TAGS, XRAY_FALLBACKS_INBOUND_TAGS)
    except Exception:
        config = base_xray

    op.bulk_insert(
        sa.table(
            "core_configs",
            sa.Column("created_at", sa.DateTime),
            sa.Column("name", sa.String),
            sa.Column("config", sa.JSON),
            sa.Column("exclude_inbound_tags", sa.String),
            sa.Column("fallbacks_inbound_tags", sa.String),
        ),
        [
            {
                "created_at": dt.now(tz.utc).replace(tzinfo=None),
                "name": "Default Core Config",
                "config": config,
                "exclude_inbound_tags": ",".join(XRAY_EXCLUDE_INBOUND_TAGS),
                "fallbacks_inbound_tags": ",".join(XRAY_FALLBACKS_INBOUND_TAGS),
            }
        ],
    )


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("nodes_ibfk_1", 'nodes', type_='foreignkey')
    op.drop_column('nodes', 'core_config_id')
    op.drop_table('core_configs')
    # ### end Alembic commands ###

"""drop mux_enable column in hosts table

Revision ID: 9aa6559916be
Revises: 95da30deba8d
Create Date: 2025-02-27 11:28:13.464363

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '9aa6559916be'
down_revision = '95da30deba8d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('hosts', 'mux_enable')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('hosts', sa.Column('mux_enable', sa.BOOLEAN(), server_default=sa.text("'0'"), nullable=False))
    # ### end Alembic commands ###

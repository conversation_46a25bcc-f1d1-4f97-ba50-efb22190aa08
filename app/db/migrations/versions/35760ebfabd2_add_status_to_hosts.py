"""add status to hosts

Revision ID: 35760ebfabd2
Revises: f44ec4769d5d
Create Date: 2025-03-28 09:04:24.440491

"""
from alembic import op
import sqlalchemy as sa
from app.db.compiles_types import EnumArray


# revision identifiers, used by Alembic.
revision = '35760ebfabd2'
down_revision = 'f44ec4769d5d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    dialect = op.get_bind().dialect.name

    # Define the column type once for reusability
    status_column_type = EnumArray(sa.Enum('active', 'disabled', 'limited', 'expired', 'on_hold', name='userstatus'))

    if dialect == 'mysql':
        # MySQL: Add nullable, update, then alter to non-nullable
        op.add_column('hosts', sa.Column('status', status_column_type, nullable=True))
        op.execute("UPDATE hosts SET status = '[]'")  # Set default for existing rows
        op.alter_column('hosts', 'status', nullable=False,
                        existing_type=status_column_type)
    elif dialect == 'postgresql':
        # PostgreSQL: Use server_default='{}' as PostgreSQL uses array syntax
        op.add_column('hosts', sa.Column('status', status_column_type,
                      server_default='{}', nullable=False))
    else:
        # SQLite and others: Use server_default='[]'
        op.add_column('hosts', sa.Column('status', status_column_type,
                      server_default='[]', nullable=False))


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('hosts', 'status')
    # ### end Alembic commands ###

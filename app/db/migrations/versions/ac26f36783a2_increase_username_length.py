"""increase username length

Revision ID: ac26f36783a2
Revises: 6980e98bba01
Create Date: 2025-04-18 16:43:14.242833

"""
from alembic import op
import sqlalchemy as sa
from app.db.compiles_types import CaseSensitiveString


# revision identifiers, used by Alembic.
revision = 'ac26f36783a2'
down_revision = '6980e98bba01'
branch_labels = None
depends_on = None

table_name = 'users'

def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table(table_name) as batch_op:
        batch_op.alter_column('username',
            existing_type=sa.VARCHAR(length=34),
            type_=CaseSensitiveString(length=128),
            existing_nullable=False,
            )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table(table_name) as batch_op:
        batch_op.alter_column('username',
            existing_type=CaseSensitiveString(length=128),
            type_=sa.VARCHAR(length=34),
            existing_nullable=False,
            )
    # ### end Alembic commands ###

"""gozargah-node

Revision ID: c41c441de44c
Revises: 0b62f893092b
Create Date: 2025-03-12 15:01:01.918710

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision = 'c41c441de44c'
down_revision = '0b62f893092b'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('nodes', sa.Column('node_version', sa.String(length=32), nullable=True))
    
    # Create enum type for PostgreSQL
    if op.get_bind().dialect.name == 'postgresql':
        nodeconnectiontype = postgresql.ENUM('grpc', 'rest', name='nodeconnectiontype')
        nodeconnectiontype.create(op.get_bind())
    
    op.add_column('nodes', sa.Column('connection_type', sa.Enum('grpc', 'rest', name='nodeconnectiontype', create_type=False), server_default='grpc', nullable=False))
    op.add_column('nodes', sa.Column('server_ca', sa.String(length=2048), nullable=False, server_default=''))
    op.add_column('nodes', sa.Column('keep_alive', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('nodes', sa.Column('max_logs', sa.BigInteger(), server_default=sa.text('1000'), nullable=False))
    op.drop_column('nodes', 'api_port')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('nodes', sa.Column('api_port', sa.INTEGER(), nullable=False))
    op.drop_column('nodes', 'max_logs')
    op.drop_column('nodes', 'keep_alive')
    op.drop_column('nodes', 'server_ca')
    op.drop_column('nodes', 'connection_type')
    op.drop_column('nodes', 'node_version')
    
    # Drop enum type for PostgreSQL
    if op.get_bind().dialect.name == 'postgresql':
        nodeconnectiontype = postgresql.ENUM('grpc', 'rest', name='nodeconnectiontype')
        nodeconnectiontype.drop(op.get_bind())
    # ### end Alembic commands ###

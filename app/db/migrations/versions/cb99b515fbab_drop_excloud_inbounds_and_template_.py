"""drop excloud_inbounds and template_inbound tables

Revision ID: cb99b515fbab
Revises: 16e19723febc
Create Date: 2025-03-17 10:32:08.929368

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cb99b515fbab'
down_revision = '16e19723febc'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('template_inbounds_association')
    op.drop_table('exclude_inbounds_association')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('exclude_inbounds_association',
    sa.Column('proxy_id', sa.INTEGER(), nullable=True),
    sa.Column('inbound_tag', sa.VARCHAR(length=256), nullable=True),
    sa.ForeignKeyConstraint(['inbound_tag'], ['inbounds.tag'], ),
    sa.ForeignKeyConstraint(['proxy_id'], ['proxies.id'], )
    )
    op.create_table('template_inbounds_association',
    sa.Column('user_template_id', sa.INTEGER(), nullable=True),
    sa.Column('inbound_tag', sa.VARCHAR(length=256), nullable=True),
    sa.ForeignKeyConstraint(['inbound_tag'], ['inbounds.tag'], ),
    sa.ForeignKeyConstraint(['user_template_id'], ['user_templates.id'], )
    )
    # ### end Alembic commands ###

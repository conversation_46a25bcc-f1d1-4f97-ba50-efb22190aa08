"""gather node logs option

Revision ID: beb47f520963
Revises: 508061427170
Create Date: 2025-05-03 11:39:20.303870

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'beb47f520963'
down_revision = '508061427170'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('nodes', sa.Column('gather_logs', sa.<PERSON>(), server_default='1', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('nodes', 'gather_logs')
    # ### end Alembic commands ###

"""add is_disabled to userTemplates

Revision ID: cf67676aaf82
Revises: d085fae205b6
Create Date: 2025-05-16 18:20:41.833188

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cf67676aaf82'
down_revision = 'd085fae205b6'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_templates', sa.Column('is_disabled', sa.<PERSON>(), server_default='0', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_templates', 'is_disabled')
    # ### end Alembic commands ###

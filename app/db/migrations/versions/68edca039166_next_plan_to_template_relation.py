"""next plan to template relation

Revision ID: 68edca039166
Revises: 931ed40d6eec
Create Date: 2025-01-12 23:58:41.810295

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '68edca039166'
down_revision = '931ed40d6eec'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('next_plans') as batch_op:
        batch_op.add_column(sa.Column('user_template_id', sa.Integer(), nullable=True))
        batch_op.create_foreign_key('fk_next_plans_user_template_id_user_templates', 'user_templates', ['user_template_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('next_plans') as batch_op:
        batch_op.drop_constraint('fk_next_plans_user_template_id_user_templates', type_='foreignkey')
        batch_op.drop_column('user_template_id')
    # ### end Alembic commands ###

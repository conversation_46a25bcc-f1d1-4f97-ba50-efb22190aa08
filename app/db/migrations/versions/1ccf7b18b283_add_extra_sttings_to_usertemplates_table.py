"""add extra_sttings to usertemplates table

Revision ID: 1ccf7b18b283
Revises: 35760ebfabd2
Create Date: 2025-04-07 11:57:39.917127

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1ccf7b18b283'
down_revision = '35760ebfabd2'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_templates', sa.Column('extra_settings', sa.JSON(none_as_null=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_templates', 'extra_settings')
    # ### end Alembic commands ###

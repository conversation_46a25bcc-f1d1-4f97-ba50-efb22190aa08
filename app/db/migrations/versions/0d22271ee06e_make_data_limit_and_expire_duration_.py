"""make data_limit and expire_duration bigint and sub_last_user_agent string

Revision ID: 0d22271ee06e
Revises: 1dc5f89b706a
Create Date: 2025-03-19 22:04:10.485911

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '0d22271ee06e'
down_revision = '1dc5f89b706a'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_templates') as batch_op:
        batch_op.alter_column('data_limit',
                   existing_type=sa.INTEGER(),
                   type_=sa.BigInteger(),
                   existing_nullable=True)
        batch_op.alter_column('expire_duration',
                   existing_type=sa.INTEGER(),
                   type_=sa.BigInteger(),
                   existing_nullable=True)

    with op.batch_alter_table('users') as batch_op:
        batch_op.alter_column('sub_last_user_agent',
                   existing_type=sa.VARCHAR(length=64),
                   type_=sa.String(length=512),
                   existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users') as batch_op:
        batch_op.alter_column('sub_last_user_agent',
                   existing_type=sa.String(length=512),
                   type_=sa.VARCHAR(length=64),
                   existing_nullable=True)

    with op.batch_alter_table('user_templates') as batch_op:
        batch_op.alter_column('expire_duration',
                   existing_type=sa.BigInteger(),
                   type_=sa.INTEGER(),
                   existing_nullable=True)
        batch_op.alter_column('data_limit',
                   existing_type=sa.BigInteger(),
                   type_=sa.INTEGER(),
                   existing_nullable=True)
    # ### end Alembic commands ###

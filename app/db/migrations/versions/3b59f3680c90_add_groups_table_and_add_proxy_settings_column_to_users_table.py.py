"""add groups table and add proxy_settings column to users table

Revision ID: 3b59f3680c90
Revises: c41c441de44c
Create Date: 2025-03-17 08:35:44.071861

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql, postgresql


# revision identifiers, used by Alembic.
revision = '3b59f3680c90'
down_revision = 'c41c441de44c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('groups',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=64), nullable=True),
    sa.Column('is_disabled', sa.<PERSON>(), nullable=False, server_default='0', default=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('inbounds_groups_association',
    sa.Column('inbound_id', sa.Integer(), nullable=False),
    sa.Column('group_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['group_id'], ['groups.id'], ),
    sa.ForeignKeyConstraint(['inbound_id'], ['inbounds.id'], ),
    sa.PrimaryKeyConstraint('inbound_id', 'group_id')
    )
    op.create_table('template_group_association',
    sa.Column('user_template_id', sa.Integer(), nullable=True),
    sa.Column('group_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['group_id'], ['groups.id'], ),
    sa.ForeignKeyConstraint(['user_template_id'], ['user_templates.id'], )
    )
    op.create_table('users_groups_association',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('groups_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['groups_id'], ['groups.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'groups_id')
    )

    # Handle proxy_settings column addition based on database type
    dialect = op.get_bind().dialect.name
    if dialect == 'mysql':
        # For MySQL: Add column first, then update with default value
        op.add_column('users', sa.Column('proxy_settings', mysql.JSON(), nullable=True))
        op.execute("UPDATE users SET proxy_settings = '{}'")
        # Make it not nullable after setting default, specifying the existing type
        with op.batch_alter_table('users') as batch_op:
            batch_op.alter_column('proxy_settings',
                                existing_type=mysql.JSON(),
                                nullable=False)
    elif dialect == 'postgresql':
        # For PostgreSQL: Can use JSONB with default
        op.add_column('users', sa.Column('proxy_settings', 
                                       postgresql.JSONB(), 
                                       server_default='{}', 
                                       nullable=False))
    else:
        # For SQLite and others: Use standard JSON
        op.add_column('users', sa.Column('proxy_settings', 
                                       sa.JSON(none_as_null=True), 
                                       server_default='{}', 
                                       nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('users_groups_association')
    op.drop_table('template_group_association')
    op.drop_table('inbounds_groups_association')
    op.drop_table('groups')
    op.drop_column('users', 'proxy_settings')
    # ### end Alembic commands ###

"""user template status and reset usage

Revision ID: 9cfffef342cd
Revises: 53586547c10e
Create Date: 2025-04-14 11:08:05.711323

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '9cfffef342cd'
down_revision = '53586547c10e'
branch_labels = None
depends_on = None


new_enum = sa.Enum('active', 'on_hold', name='userstatuscreate')



def upgrade() -> None:
    connection = op.get_bind()
    dialect = connection.dialect.name

    if dialect == "postgresql":
        new_enum.create(op.get_bind(), checkfirst=False)

    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user_templates', sa.Column('status', new_enum, nullable=False, server_default='active'))
    op.add_column('user_templates', sa.Column('reset_usages', sa.<PERSON>(), server_default='0', nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    connection = op.get_bind()
    dialect = connection.dialect.name

    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user_templates', 'reset_usages')
    op.drop_column('user_templates', 'status')
    # ### end Alembic commands ###

    if dialect == "postgresql":
        new_enum.drop(op.get_bind(), checkfirst=False)
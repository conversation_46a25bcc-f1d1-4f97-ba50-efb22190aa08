"""add priority to proxy hosts

Revision ID: c5c734bd3da2
Revises: 68edca039166
Create Date: 2025-02-24 22:29:49.063544

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision = "c5c734bd3da2"
down_revision = "68edca039166"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    is_sqlite = op.get_bind().dialect.name == "sqlite"
    is_postgresql = op.get_bind().dialect.name == "postgresql"

    op.add_column("hosts", sa.Column("priority", sa.Integer(), nullable=True))
    op.execute("UPDATE hosts SET priority = id")

    with op.batch_alter_table("hosts") as batch_op:
        # Handle priority column first
        batch_op.alter_column("priority", existing_type=sa.Integer(), nullable=False)

    if is_sqlite:
        with op.batch_alter_table("hosts") as batch_op:
            batch_op.alter_column('inbound_tag',
                   existing_type=sa.VARCHAR(length=256),
                   nullable=True)
            batch_op.create_foreign_key("hosts_ibfk_1", 'inbounds', ['inbound_tag'], ['tag'], onupdate='CASCADE', ondelete='SET NULL')

    else:
        op.alter_column('hosts', 'inbound_tag',
               existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_bin', length=256),
               nullable=True)
        
        # Find and drop the existing foreign key constraint
        inspector = inspect(op.get_bind())
        for fk in inspector.get_foreign_keys('hosts'):
            if fk['referred_table'] == 'inbounds' and 'inbound_tag' in fk['constrained_columns']:
                op.drop_constraint(
                    fk['name'],
                    'hosts',
                    type_='foreignkey'
                )
                break

        # Create new foreign key with appropriate name
        constraint_name = "hosts_inbound_tag_fkey" if is_postgresql else "hosts_ibfk_1"
        op.create_foreign_key(
            constraint_name, 
            'hosts', 
            'inbounds', 
            ['inbound_tag'], 
            ['tag'], 
            onupdate='CASCADE', 
            ondelete='SET NULL'
        )

    # Replace the direct constraint drop with an inspection-based approach
    inspector = inspect(op.get_bind())
    for fk in inspector.get_foreign_keys('hosts'):
        if fk['referred_table'] == 'proxies' and 'proxy_id' in fk['constrained_columns']:
            op.drop_constraint(
                fk['name'],
                'hosts',
                type_='foreignkey'
            )
            break

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    is_sqlite = op.get_bind().dialect.name == "sqlite"
    is_postgresql = op.get_bind().dialect.name == "postgresql"
    
    if is_sqlite:
        with op.batch_alter_table("hosts") as batch_op:
            batch_op.drop_constraint('hosts_ibfk_1', type_='foreignkey')
            batch_op.alter_column('inbound_tag',
                       existing_type=sa.VARCHAR(length=256),
                       nullable=False)
            batch_op.create_foreign_key(
                'hosts_ibfk_1', 
                'inbounds', 
                ['inbound_tag'], 
                ['tag']
            )

    else:
        # Find and drop the existing foreign key constraint
        inspector = inspect(op.get_bind())
        for fk in inspector.get_foreign_keys('hosts'):
            if fk['referred_table'] == 'inbounds' and 'inbound_tag' in fk['constrained_columns']:
                op.drop_constraint(
                    fk['name'],
                    'hosts',
                    type_='foreignkey'
                )
                break

        # Create new foreign key with appropriate name
        constraint_name = "hosts_inbound_tag_fkey" if is_postgresql else "hosts_ibfk_1"
        op.create_foreign_key(
            constraint_name, 
            'hosts', 
            'inbounds', 
            ['inbound_tag'], 
            ['tag']
        )
        op.alter_column('hosts', 'inbound_tag',
                   existing_type=mysql.VARCHAR(charset='utf8mb4', collation='utf8mb4_bin', length=256),
                   nullable=False)

    op.drop_column("hosts", "priority")
    # ### end Alembic commands ###

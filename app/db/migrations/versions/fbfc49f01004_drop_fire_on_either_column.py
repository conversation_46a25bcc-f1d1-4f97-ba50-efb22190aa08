"""drop fire_on_either column

Revision ID: fbfc49f01004
Revises: cf67676aaf82
Create Date: 2025-05-26 12:32:37.431078

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fbfc49f01004'
down_revision = 'cf67676aaf82'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('next_plans', 'fire_on_either')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('next_plans', sa.Column('fire_on_either', sa.BOOLEAN(), server_default=sa.text("'0'"), nullable=False))
    # ### end Alembic commands ###

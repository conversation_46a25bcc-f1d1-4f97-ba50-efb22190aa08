import asyncio
from contextlib import asynccontextmanager

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi import <PERSON><PERSON><PERSON>, Request, status
from fastapi.encoders import jsonable_encoder
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.routing import APIRoute

from app.utils.logger import get_logger
from config import ALLOWED_ORIGINS, DOCS, XRAY_SUBSCRIPTION_PATH

__version__ = "1.0.0-beta-1"

startup_functions = []
shutdown_functions = []


def on_startup(func):
    startup_functions.append(func)
    return func


def on_shutdown(func):
    shutdown_functions.append(func)
    return func


@asynccontextmanager
async def lifespan(app: FastAPI):
    for func in startup_functions:
        if callable(func):
            if asyncio.iscoroutinefunction(func):  # Better way to check if it's async
                if "app" in func.__code__.co_varnames:
                    await func(app)
                else:
                    await func()
            else:
                if "app" in func.__code__.co_varnames:
                    func(app)
                else:
                    func()
    yield

    for func in shutdown_functions:
        if callable(func):
            if asyncio.iscoroutinefunction(func):
                if "app" in func.__code__.co_varnames:
                    await func(app)
                else:
                    await func()
            else:
                if "app" in func.__code__.co_varnames:
                    func(app)
                else:
                    func()


app = FastAPI(
    title="MarzbanAPI",
    description="Unified GUI Censorship Resistant Solution Powered by Xray",
    version=__version__,
    lifespan=lifespan,
    openapi_url="/openapi.json" if DOCS else None,
)

scheduler = AsyncIOScheduler(job_defaults={"max_instances": 20}, timezone="UTC")
logger = get_logger()


app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
from app import routers, telegram, jobs  # noqa
from app.routers import api_router  # noqa
from app.routers.subscription import router as subscription_router, custom_subscription_router  # noqa
# from app.routers.load_balancer import router as load_balancer_router # DEPRECATED
from app.routers.resilient_node_group import router as resilient_node_group_router

# Debug flag - set to False to disable custom subscription router
ENABLE_CUSTOM_SUBSCRIPTION = True

# Debug: Print routes being registered
print("=== REGISTERING API ROUTER ===")
for route in api_router.routes:
    if hasattr(route, 'path'):
        print(f"API Route: {route.path} - Methods: {getattr(route, 'methods', 'N/A')}")

app.include_router(api_router)
# app.include_router(load_balancer_router) # DEPRECATED
app.include_router(resilient_node_group_router)

print("=== REGISTERING SUBSCRIPTION ROUTER ===")
for route in subscription_router.routes:
    if hasattr(route, 'path'):
        print(f"Subscription Route: {route.path} - Methods: {getattr(route, 'methods', 'N/A')}")
app.include_router(subscription_router)

print("=== REGISTERING CUSTOM SUBSCRIPTION ROUTER ===")
for route in custom_subscription_router.routes:
    if hasattr(route, 'path'):
        print(f"Custom Route: {route.path} - Methods: {getattr(route, 'methods', 'N/A')}")

if ENABLE_CUSTOM_SUBSCRIPTION:
    app.include_router(custom_subscription_router)
else:
    print("=== CUSTOM SUBSCRIPTION ROUTER DISABLED ===")

def use_route_names_as_operation_ids(app: FastAPI) -> None:
    for route in app.routes:
        if isinstance(route, APIRoute):
            route.operation_id = route.name


use_route_names_as_operation_ids(app)


@on_startup
def validate_paths():
    # Debug: Print all final routes
    print("=== ALL FINAL ROUTES ===")
    for route in app.routes:
        if hasattr(route, 'path'):
            print(f"Final Route: {route.path} - Methods: {getattr(route, 'methods', 'N/A')}")
    
    # Debug: Print database info
    from app.db import get_db
    from app.db.crud import get_admins
    from config import SQLALCHEMY_DATABASE_URL
    print(f"=== DATABASE INFO ===")
    print(f"Database URL: {SQLALCHEMY_DATABASE_URL}")
    
    try:
        with next(get_db()) as db:
            admins = get_admins(db)
            print(f"Admin count on startup: {len(admins)}")
            for admin in admins:
                print(f"  - Admin: {admin.username} (sudo: {admin.is_sudo})")
    except Exception as e:
        print(f"Error checking admins: {e}")
    
    paths = [f"{r.path}/" for r in app.routes]
    paths.append("/api/")
    if f"/{XRAY_SUBSCRIPTION_PATH}/" in paths:
        raise ValueError(f"you can't use /{XRAY_SUBSCRIPTION_PATH}/ as subscription path it reserved for {app.title}")


on_startup(scheduler.start)
on_shutdown(scheduler.shutdown)
on_startup(lambda: logger.info(f"Marzban v{__version__}"))


@app.exception_handler(RequestValidationError)
def validation_exception_handler(request: Request, exc: RequestValidationError):
    details = {}
    for error in exc.errors():
        details[error["loc"][-1]] = error.get("msg")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=jsonable_encoder({"detail": details}),
    )

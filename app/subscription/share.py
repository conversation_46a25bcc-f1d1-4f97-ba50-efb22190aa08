import base64
import logging
import random
import secrets
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import TYPE_CHECKING, List, Literal, Union, Optional

from jdatetime import date as jd
from sqlalchemy.orm import Session

from app.core.hosts import hosts as hosts_storage
from app.core.manager import core_manager
from app.db.models import User, UserStatus
from app.settings import subscription_settings
from app.utils.system import get_public_ip, get_public_ipv6, readable_size
from app.models.node import NodeStatus

logger = logging.getLogger(__name__)

from . import (
    ClashConfiguration,
    ClashMetaConfiguration,
    OutlineConfiguration,
    SingBoxConfiguration,
    StandardLinks,
    XrayConfig,
)

if TYPE_CHECKING:
    from app.models.user import UserResponse
    from app.db.models import Node as NodeDbModel
    from app.db import crud as db_crud

from config import (
    ACTIVE_STATUS_TEXT,
    DISABLED_STATUS_TEXT,
    EXPIRED_STATUS_TEXT,
    LIMITED_STATUS_TEXT,
    ONHOLD_STATUS_TEXT,
)

SERVER_IP = get_public_ip()
SERVER_IPV6 = get_public_ipv6()

STATUS_EMOJIS = {
    "active": "✅",
    "expired": "⌛️",
    "limited": "🪫",
    "disabled": "❌",
    "on_hold": "🔌",
}


async def generate_standard_links(
    proxies: dict, inbounds: list[str], extra_data: dict, reverse: bool, user_status: UserStatus
) -> list:
    format_variables = setup_format_variables(extra_data)
    conf = StandardLinks()
    return await process_inbounds_and_tags(
        inbounds, proxies, format_variables, conf=conf, reverse=reverse, user_status=user_status
    )


async def generate_clash_subscription(
    proxies: dict,
    inbounds: dict,
    extra_data: dict,
    reverse: bool,
    db: Session,
    user: "UserResponse",
    is_meta: bool = False
) -> str:
    if is_meta:
        conf = ClashMetaConfiguration()
    else:
        conf = ClashConfiguration()

    format_variables = setup_format_variables(extra_data)
    return await process_inbounds_and_tags(
        inbounds, proxies, format_variables, conf=conf, reverse=reverse, db=db, user=user
    )


async def generate_singbox_subscription(
    proxies: dict,
    inbounds: dict,
    extra_data: dict,
    reverse: bool,
    db: Session,
    user: "UserResponse"
) -> str:
    conf = SingBoxConfiguration()

    format_variables = setup_format_variables(extra_data)
    return await process_inbounds_and_tags(
        inbounds, proxies, format_variables, conf=conf, reverse=reverse, db=db, user=user
    )


async def generate_outline_subscription(
    proxies: dict,
    inbounds: dict,
    extra_data: dict,
    reverse: bool,
    db: Session,
    user: "UserResponse"
) -> str:
    conf = OutlineConfiguration()

    format_variables = setup_format_variables(extra_data)
    return await process_inbounds_and_tags(
        inbounds, proxies, format_variables, conf=conf, reverse=reverse, db=db, user=user
    )


async def generate_xray_subscription(
    proxies: dict,
    inbounds: dict,
    extra_data: dict,
    reverse: bool,
    db: Session,
    user: "UserResponse"
) -> str:
    conf = XrayConfig()

    format_variables = setup_format_variables(extra_data)
    return await process_inbounds_and_tags(
        inbounds, proxies, format_variables, conf=conf, reverse=reverse, db=db, user=user
    )


async def generate_v2ray_json_subscription(
    proxies: dict,
    inbounds: dict,
    extra_data: dict,
    reverse: bool,
    db: Session,
    user: "UserResponse"
) -> str:
    conf = XrayConfig()

    format_variables = setup_format_variables(extra_data)
    return await process_inbounds_and_tags(
        inbounds, proxies, format_variables, conf=conf, reverse=reverse, db=db, user=user
    )


async def generate_subscription(
    user: "UserResponse",
    config_format: Literal["v2ray", "clash-meta", "clash", "sing-box", "outline", "v2ray-json"],
    as_base64: bool,
    reverse: bool,
    db: Session,
) -> str:
    kwargs = {
        "proxies": user.proxy_settings,
        "inbounds": await user.inbounds(),
        "extra_data": user.__dict__,
        "reverse": reverse,
        "db": db,
        "user": user
    }
    kwargs["extra_data"]["expire"] = user.expire

    if config_format == "v2ray":
        config = "\n".join(await generate_standard_links(
            proxies=user.proxy_settings,
            inbounds=await user.inbounds(),
            extra_data=user.__dict__,
            reverse=reverse,
            user_status=user.status
        ))
    elif config_format == "clash-meta":
        config = await generate_clash_subscription(**kwargs, is_meta=True)
    elif config_format == "clash":
        config = await generate_clash_subscription(**kwargs)
    elif config_format == "sing-box":
        config = await generate_singbox_subscription(**kwargs)
    elif config_format == "outline":
        config = await generate_outline_subscription(**kwargs)
    elif config_format == "v2ray-json":
        config = await generate_v2ray_json_subscription(**kwargs)
    else:
        raise ValueError(f'Unsupported format "{config_format}"')

    if as_base64:
        config = base64.b64encode(config.encode()).decode()

    return config


def _select_node_by_strategy(active_nodes: list, strategy_hint: str, user_id: int, db: Session):
    """
    Select a node from active nodes based on the client strategy hint with enhanced server-side logic.
    """
    from app.models.resilient_node_group import ClientStrategyHint

    if not active_nodes:
        return None

    if len(active_nodes) == 1:
        return active_nodes[0]

    # Convert string to enum if needed
    if isinstance(strategy_hint, str):
        try:
            strategy = ClientStrategyHint(strategy_hint)
        except ValueError:
            strategy = ClientStrategyHint.CLIENT_DEFAULT
    else:
        strategy = strategy_hint

    if strategy == ClientStrategyHint.URL_TEST:
        return _select_fastest_node(active_nodes, user_id)
    elif strategy == ClientStrategyHint.FALLBACK:
        return _select_fallback_node(active_nodes)
    elif strategy == ClientStrategyHint.LOAD_BALANCE:
        return _select_least_loaded_node(active_nodes, user_id)
    elif strategy == ClientStrategyHint.CLIENT_DEFAULT:
        return _select_consistent_node(active_nodes, user_id)
    else:
        return random.choice(active_nodes)


def _select_fastest_node(active_nodes: list, user_id: int):
    """Select node based on performance metrics."""
    def performance_score(node):
        response_time = node.avg_response_time or 1000.0
        success_rate = node.success_rate or 50.0
        time_score = max(0, 100 - (response_time / 10))
        combined_score = (success_rate * 0.7) + (time_score * 0.3)
        return combined_score

    sorted_nodes = sorted(active_nodes, key=performance_score, reverse=True)
    top_nodes_count = max(1, len(sorted_nodes) // 2)
    top_nodes = sorted_nodes[:top_nodes_count]
    return top_nodes[user_id % len(top_nodes)]

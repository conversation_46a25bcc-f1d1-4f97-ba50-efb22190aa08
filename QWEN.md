# AGENTS.md - Marzban Development Guide


## Quick Start Commands

### Development Setup
```bash
# Quick start with <PERSON><PERSON>
docker-compose up

# Manual development setup
make setup          # Install Python deps and uv
make install-front  # Setup Node.js/TypeScript
make run-migration  # Run database migrations  
make run            # Start application
```

### Common Development Tasks
```bash
make run-watch      # Auto-restart on changes
make test           # Run pytest with coverage
make format         # Format code with ruff
make check          # Lint with ruff

# Database operations
alembic upgrade head  # Run pending migrations
alembic revision -m "message" --autogenerate  # New migration
```

### API Development
```bash
# Test single endpoint
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8000/api/system/status

# Run specific test
python -m pytest tests/test_users.py::test_create_user -v
```

## Architecture Overview

Marzban is a Python-based proxy management system with FastAPI backend and React frontend.

### Core Components

**Backend Stack:**
- **Language**: Python 3.12+ (async/await native)
- **Framework**: FastAPI with async SQLAlchemy ORM
- **Database**: SQLite (default) or PostgreSQL/MySQL
- **Task Queue**: APScheduler for background jobs

**Frontend Stack:**
- **Dashboard**: React/TypeScript with Vite
- **UI**: Tailwind CSS + react-router + TanStack Query
- **Real-time**: Server-Sent Events for notifications

### Directory Structure

```
├── app/
│   ├── api/           # FastAPI routes and handlers
│   ├── core/          # Business logic and proxy management
│   ├── dashboard/     # React-based admin interface
│   ├── models/        # Pydantic schemas and validation
│   ├── db/            # Database models and CRUD operations
│   ├── templates/     # Proxy client configuration templates
│   └── xray/          # Xray server integration
├── scripts/           # Database and setup scripts
├── tests/             # Pytest test suite
└── docker-compose.yml # Development environment
```

### Key Technologies

- **Proxy Engine**: Xray-core integration
- **Database**: SQLAlchemy 2.0 with Alembic migrations
- **Authentication**: JWT tokens in Bearer header
- **Encryption**: AES-256 for sensitive data storage
- **Monitoring**: Node health checks and performance tracking
- **Notifications**: Discord, Telegram, Webhook integrations

### Configuration Files

- `.env` - Environment variables (database, SSL tokens, etc.)
- `config.py` - System configuration and settings
- Database migrations in `alembic/` directory

### Testing Framework

- **pytest** with async support for API tests
- **in-memory SQLite** for fast unit tests
- **fixture** setup for mock user data and test environment

### Common API Patterns

- **RESTful endpoints** under `/api/*` with HTTP verbs
- **JWT tokens** in `Authorization: Bearer <token>` header
- **Async database operations** via SQLAlchemy async sessions
- **Pydantic models** for request/response validation
- **Error handling** with standardized HTTP status codes
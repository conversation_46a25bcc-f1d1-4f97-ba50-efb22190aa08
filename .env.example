UVICORN_HOST = "0.0.0.0"
UVICORN_PORT = 8000
# ALLOWED_ORIGINS=http://localhost,http://localhost:8000,http://example.com

## We highly recommend add admin using `marzban cli` tool and do not use
## the following variables which is somehow hard codded infrmation.
# SUDO_USERNAME = "admin"
# SUDO_PASSWORD = "admin"

# UVICORN_UDS: "/run/marzban.socket"
# UVICORN_SSL_CERTFILE = "/var/lib/marzban/certs/example.com/fullchain.pem"
# UVICORN_SSL_KEYFILE = "/var/lib/marzban/certs/example.com/key.pem"
# UVICORN_SSL_CA_TYPE = "public"

# DASHBOARD_PATH = "/dashboard/"

# XRAY_SUBSCRIPTION_PATH = "sub"

# CUSTOM_TEMPLATES_DIRECTORY="/var/lib/marzban/templates/"
# CLASH_SUBSCRIPTION_TEMPLATE="clash/my-custom-template.yml"
# SUBSCRIPTION_PAGE_TEMPLATE="subscription/index.html"
# HOME_PAGE_TEMPLATE="home/index.html"
# XRAY_SUBSCRIPTION_TEMPLATE="xray/default.json"
# SINGBOX_SUBSCRIPTION_TEMPLATE="singbox/default.json"

## External config to import into v2ray format subscription
# EXTERNAL_CONFIG = "config://..."

# SQLALCHEMY_DATABASE_URL = "sqlite+aiosqlite:///db.sqlite3"
# SQLALCHEMY_DATABASE_URL="postgresql+asyncpg://postgres:DB_PASSWORD@localhost:5432/marzban"
# SQLALCHEMY_DATABASE_URL="mysql+asyncmy://root:DB_PASSWORD@127.0.0.1/marzban"
# SQLALCHEMY_POOL_SIZE = 10
# SQLALCHEMY_MAX_OVERFLOW = 30

### Use negative values to disable auto-delete by default
# USERS_AUTODELETE_DAYS = -1
# USER_AUTODELETE_INCLUDE_LIMITED_ACCOUNTS = false

### for developers
# DOCS=True
# DEBUG=True

# VITE_BASE_API="https://example.com/"
# JWT_ACCESS_TOKEN_EXPIRE_MINUTES = 1440

# due to high amount of data, this job is only available for postgresql and timescaledb
# ENABLE_RECORDING_NODES_STATS = False

# JOB_CORE_HEALTH_CHECK_INTERVAL = 10
# JOB_RECORD_NODE_USAGES_INTERVAL = 30
# JOB_RECORD_USER_USAGES_INTERVAL = 10
# JOB_REVIEW_USERS_INTERVAL = 10
# JOB_SEND_NOTIFICATIONS_INTERVAL = 30
# JOB_GHATER_NODES_STATS_INTERVAL = 25
# JOB_REMOVE_OLD_INBOUNDS_INTERVAL = 600
# JOB_REMOVE_EXPIRED_USERS_INTERVAL = 3600
# JOB_RESET_USER_DATA_USAGE_INTERVAL = 600

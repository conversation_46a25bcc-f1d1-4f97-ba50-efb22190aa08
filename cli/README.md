# Marzban CLI

A modern, interactive command-line interface for managing Marzban, built with Textual.

## Features

- 🎯 Interactive TUI (Text User Interface)
- 📱 Responsive design with dark mode support
- ⌨️ Keyboard shortcuts for quick navigation
- 🔄 Real-time updates
- 📊 Rich data visualization
- 🔒 Secure admin management

## Usage

### Starting the CLI

```bash
marzban cli
```

### Keyboard Shortcuts

#### Global Commands

- `q` - Quit the application
- `?` - Show help

#### Admin Section

- `c` - Create new admin
- `m` - Modify admin
- `r` - Reset admin usage
- `d` - Delete admin
- `i` - Import admins from environment

### Admin Management

- Create, modify, and delete admin accounts
- Reset admin usage statistics
- Import admins from environment variables
- View admin details and status

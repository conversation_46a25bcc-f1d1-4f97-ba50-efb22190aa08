name: Code Format Checker
on:
    push:
        branches:
            - "*"
        paths:
            - "**/*.py"

    pull_request:
        branches:
            - "*"
        paths:
            - "**/*.py"

jobs:
    ruff:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout code
              uses: actions/checkout@v2

            - name: Set up Python
              uses: actions/setup-python@v2
              with:
                  python-version: "3.12"

            - name: Install Ruff
              run: |
                  python -m pip install --upgrade pip
                  pip install ruff
            - name: Run Ruff Format
              run: |
                  ruff check --output-format=github .

name: "Frontend CodeQL"

on:
  push:
    branches:
      - "*"
    paths:
      - "dashboard/**"
  pull_request:
    branches:
      - "*"
    paths:
      - "dashboard/**"
  schedule:
    - cron: "0 11 * * 3"

jobs:
  analyze:
    name: Analyze
    runs-on: "ubuntu-latest"
    timeout-minutes: 120
    permissions:
      security-events: write
      actions: read
      contents: read

    strategy:
      fail-fast: false
      matrix:
        language:
          - "javascript-typescript"

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        with:
          category: "/language:${{matrix.language}}"

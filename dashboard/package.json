{"name": "marzban-ui", "private": true, "version": "0.0.0", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "gen:api": "orval", "wait-port": "wait-port http://localhost:$UVICORN_PORT/openapi.json", "wait-port-gen-api": "pnpm run wait-port && pnpm run gen:api"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@monaco-editor/react": "^4.6.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@stablelib/base64": "^2.0.1", "@stablelib/x25519": "^2.0.1", "@tanstack/react-query": "^5.64.0", "@tanstack/react-table": "^8.20.6", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cross-env": "^7.0.3", "date-fns": "^4.1.0", "date-fns-jalali": "4.1.0-0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.5.2", "es-toolkit": "^1.31.0", "eventsource": "^3.0.7", "i18next": "^24.2.1", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.1", "isbot": "^5.1.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.465.0", "next-themes": "^0.4.6", "ofetch": "^1.4.1", "qrcode.react": "^3.2.0", "react": "^18.3.1", "react-day-picker": "9.7.0", "react-dom": "^18.3.1", "react-github-btn": "^1.4.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.0", "react-router": "^7.1.1", "react-use-websocket": "^4.13.0", "recharts": "^2.15.1", "sonner": "^2.0.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "ufo": "^1.5.4", "uuid": "^11.1.0", "vaul": "^1.1.2", "vite-tsconfig-paths": "^5.1.4", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@react-router/dev": "^7.1.1", "@react-router/fs-routes": "^7.1.1", "@svgr/webpack": "^8.1.0", "@types/lodash.debounce": "^4.0.9", "@types/node": "^20.17.28", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "autoprefixer": "^10.4.20", "eslint": "^9.18.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.18", "orval": "^7.4.1", "postcss": "^8.4.49", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^3.4.17", "typescript": "~5.6.3", "typescript-eslint": "^8.19.1", "vite": "^6.2.4", "vite-plugin-svgr": "^4.3.0", "wait-port": "^1.1.0"}, "packageManager": "pnpm@9.15.3+sha256.c1da43727ccbc1ed42aff4fd6bdb4b1e91e65a818e6efff5b240fbf070ba4eaf", "pnpm": {"overrides": {"undici@>=6.0.0 <6.21.1": ">=6.21.1", "esbuild@<=0.24.2": ">=0.25.0", "vite@>=6.0.0 <=6.0.8": ">=6.0.9", "jsonpath-plus@<10.3.0": ">=10.3.0", "@babel/runtime@<7.26.10": ">=7.26.10", "@babel/helpers@<7.26.10": ">=7.26.10", "vite@>=6.0.0 <6.0.12": ">=6.0.12", "axios@>=1.0.0 <1.8.2": ">=1.8.2"}}}
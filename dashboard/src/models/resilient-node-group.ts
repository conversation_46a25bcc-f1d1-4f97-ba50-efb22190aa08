export type ClientStrategyHint = 'url-test' | 'fallback' | 'load-balance' | 'client-default' | ''

export interface ResilientNodeGroupBase {
  name: string
  client_strategy_hint: ClientStrategyHint
}

export interface ResilientNodeGroupCreate extends ResilientNodeGroupBase {
  node_ids: number[]
}

export interface ResilientNodeGroupUpdate {
  name?: string
  client_strategy_hint?: ClientStrategyHint
  node_ids?: number[]
}

export interface ResilientNodeGroupResponse extends ResilientNodeGroupBase {
  id: number
  nodes: {
    id: number
    name: string
    address: string
    port: number
    status: string
  }[]
  node_ids: number[]
  created_at: string
  updated_at: string
}
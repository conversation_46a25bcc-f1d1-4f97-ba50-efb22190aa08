import { useMutation, useQuery } from '@tanstack/react-query'
import { orvalFetcher } from '../http'
import type { ErrorType } from '../http'
import type { 
  ResilientNodeGroupCreate, 
  ResilientNodeGroupResponse, 
  ResilientNodeGroupUpdate 
} from '@/models/resilient-node-group'

// Resilient Node Groups API

export const getResilientNodeGroups = () => {
  return orvalFetcher<ResilientNodeGroupResponse[]>({ 
    url: `/api/resilient-node-groups`, 
    method: 'GET' 
  })
}

export const getGetResilientNodeGroupsQueryKey = () => {
  return [`/api/resilient-node-groups`] as const
}

export const getGetResilientNodeGroupsQueryOptions = <TData = Awaited<ReturnType<typeof getResilientNodeGroups>>, TError = ErrorType<unknown>>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getResilientNodeGroups>>, TError, TData>>
}) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getGetResilientNodeGroupsQueryKey()

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getResilientNodeGroups>>> = () => getResilientNodeGroups()

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getResilientNodeGroups>>, TError, TData> & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type GetResilientNodeGroupsQueryResult = NonNullable<Awaited<ReturnType<typeof getResilientNodeGroups>>>
export type GetResilientNodeGroupsQueryError = ErrorType<unknown>

export function useGetResilientNodeGroups<TData = Awaited<ReturnType<typeof getResilientNodeGroups>>, TError = ErrorType<unknown>>(options?: {
  query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getResilientNodeGroups>>, TError, TData>>
}): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getGetResilientNodeGroupsQueryOptions(options)

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

  query.queryKey = queryOptions.queryKey

  return query
}

export const createResilientNodeGroup = (resilientNodeGroupCreate: BodyType<ResilientNodeGroupCreate>) => {
  return orvalFetcher<ResilientNodeGroupResponse>({ 
    url: `/api/resilient-node-groups`, 
    method: 'POST', 
    headers: { 'Content-Type': 'application/json' }, 
    data: resilientNodeGroupCreate 
  })
}

export const getCreateResilientNodeGroupMutationOptions = <
  TData = Awaited<ReturnType<typeof createResilientNodeGroup>>,
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<TData, TError, { data: BodyType<ResilientNodeGroupCreate> }, TContext>
}) => {
  const mutationKey = ['createResilientNodeGroup']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof createResilientNodeGroup>>, { data: BodyType<ResilientNodeGroupCreate> }> = props => {
    const { data } = props ?? {}

    return createResilientNodeGroup(data)
  }

  return { mutationFn, ...mutationOptions } as UseMutationOptions<TData, TError, { data: BodyType<ResilientNodeGroupCreate> }, TContext>
}

export type CreateResilientNodeGroupMutationResult = NonNullable<Awaited<ReturnType<typeof createResilientNodeGroup>>>
export type CreateResilientNodeGroupMutationBody = BodyType<ResilientNodeGroupCreate>
export type CreateResilientNodeGroupMutationError = ErrorType<unknown>

export const useCreateResilientNodeGroup = <TData = Awaited<ReturnType<typeof createResilientNodeGroup>>, TError = ErrorType<unknown>, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<TData, TError, { data: BodyType<ResilientNodeGroupCreate> }, TContext>
}): UseMutationResult<TData, TError, { data: BodyType<ResilientNodeGroupCreate> }, TContext> => {
  const mutationOptions = getCreateResilientNodeGroupMutationOptions(options)

  return useMutation(mutationOptions)
}

export const getResilientNodeGroup = (groupId: number) => {
  return orvalFetcher<ResilientNodeGroupResponse>({ 
    url: `/api/resilient-node-groups/${groupId}`, 
    method: 'GET' 
  })
}

export const getGetResilientNodeGroupQueryKey = (groupId: number) => {
  return [`/api/resilient-node-groups/${groupId}`] as const
}

export const getGetResilientNodeGroupQueryOptions = <TData = Awaited<ReturnType<typeof getResilientNodeGroup>>, TError = ErrorType<unknown>>(
  groupId: number,
  options?: { query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getResilientNodeGroup>>, TError, TData>> },
) => {
  const { query: queryOptions } = options ?? {}

  const queryKey = queryOptions?.queryKey ?? getGetResilientNodeGroupQueryKey(groupId)

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getResilientNodeGroup>>> = () => getResilientNodeGroup(groupId)

  return { queryKey, queryFn, enabled: !!groupId, ...queryOptions } as UseQueryOptions<Awaited<ReturnType<typeof getResilientNodeGroup>>, TError, TData> & { queryKey: DataTag<QueryKey, TData, TError> }
}

export type GetResilientNodeGroupQueryResult = NonNullable<Awaited<ReturnType<typeof getResilientNodeGroup>>>
export type GetResilientNodeGroupQueryError = ErrorType<unknown>

export function useGetResilientNodeGroup<TData = Awaited<ReturnType<typeof getResilientNodeGroup>>, TError = ErrorType<unknown>>(
  groupId: number,
  options?: {
    query?: Partial<UseQueryOptions<Awaited<ReturnType<typeof getResilientNodeGroup>>, TError, TData>>
  },
): UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> } {
  const queryOptions = getGetResilientNodeGroupQueryOptions(groupId, options)

  const query = useQuery(queryOptions) as UseQueryResult<TData, TError> & { queryKey: DataTag<QueryKey, TData, TError> }

  query.queryKey = queryOptions.queryKey

  return query
}

export const updateResilientNodeGroup = (groupId: number, resilientNodeGroupUpdate: BodyType<ResilientNodeGroupUpdate>) => {
  return orvalFetcher<ResilientNodeGroupResponse>({ 
    url: `/api/resilient-node-groups/${groupId}`, 
    method: 'PUT', 
    headers: { 'Content-Type': 'application/json' }, 
    data: resilientNodeGroupUpdate 
  })
}

export const getUpdateResilientNodeGroupMutationOptions = <
  TData = Awaited<ReturnType<typeof updateResilientNodeGroup>>,
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<TData, TError, { groupId: number; data: BodyType<ResilientNodeGroupUpdate> }, TContext>
}) => {
  const mutationKey = ['updateResilientNodeGroup']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof updateResilientNodeGroup>>, { groupId: number; data: BodyType<ResilientNodeGroupUpdate> }> = props => {
    const { groupId, data } = props ?? {}

    return updateResilientNodeGroup(groupId, data)
  }

  return { mutationFn, ...mutationOptions } as UseMutationOptions<TData, TError, { groupId: number; data: BodyType<ResilientNodeGroupUpdate> }, TContext>
}

export type UpdateResilientNodeGroupMutationResult = NonNullable<Awaited<ReturnType<typeof updateResilientNodeGroup>>>
export type UpdateResilientNodeGroupMutationBody = BodyType<ResilientNodeGroupUpdate>
export type UpdateResilientNodeGroupMutationError = ErrorType<unknown>

export const useUpdateResilientNodeGroup = <TData = Awaited<ReturnType<typeof updateResilientNodeGroup>>, TError = ErrorType<unknown>, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<TData, TError, { groupId: number; data: BodyType<ResilientNodeGroupUpdate> }, TContext>
}): UseMutationResult<TData, TError, { groupId: number; data: BodyType<ResilientNodeGroupUpdate> }, TContext> => {
  const mutationOptions = getUpdateResilientNodeGroupMutationOptions(options)

  return useMutation(mutationOptions)
}

export const deleteResilientNodeGroup = (groupId: number) => {
  return orvalFetcher<void>({ 
    url: `/api/resilient-node-groups/${groupId}`, 
    method: 'DELETE' 
  })
}

export const getDeleteResilientNodeGroupMutationOptions = <
  TData = Awaited<ReturnType<typeof deleteResilientNodeGroup>>,
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<TData, TError, { groupId: number }, TContext>
}) => {
  const mutationKey = ['deleteResilientNodeGroup']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof deleteResilientNodeGroup>>, { groupId: number }> = props => {
    const { groupId } = props ?? {}

    return deleteResilientNodeGroup(groupId)
  }

  return { mutationFn, ...mutationOptions } as UseMutationOptions<TData, TError, { groupId: number }, TContext>
}

export type DeleteResilientNodeGroupMutationResult = NonNullable<Awaited<ReturnType<typeof deleteResilientNodeGroup>>>

export type DeleteResilientNodeGroupMutationError = ErrorType<unknown>

export const useDeleteResilientNodeGroup = <TData = Awaited<ReturnType<typeof deleteResilientNodeGroup>>, TError = ErrorType<unknown>, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<TData, TError, { groupId: number }, TContext>
}): UseMutationResult<TData, TError, { groupId: number }, TContext> => {
  const mutationOptions = getDeleteResilientNodeGroupMutationOptions(options)

  return useMutation(mutationOptions)
}

// Hiddify Import API

export interface HiddifyImportResponse {
  successful_imports: number
  failed_imports: number
  errors: string[]
}

export const importHiddifyUsers = (data: BodyType<FormData>) => {
  return orvalFetcher<HiddifyImportResponse>({ 
    url: `/api/users/import/hiddify`, 
    method: 'POST',
    headers: { 'Content-Type': 'multipart/form-data' },
    data
  })
}

export const getImportHiddifyUsersMutationOptions = <
  TData = Awaited<ReturnType<typeof importHiddifyUsers>>,
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<TData, TError, { data: BodyType<FormData> }, TContext>
}) => {
  const mutationKey = ['importHiddifyUsers']
  const { mutation: mutationOptions } = options
    ? options.mutation && 'mutationKey' in options.mutation && options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey } }

  const mutationFn: MutationFunction<Awaited<ReturnType<typeof importHiddifyUsers>>, { data: BodyType<FormData> }> = props => {
    const { data } = props ?? {}

    return importHiddifyUsers(data)
  }

  return { mutationFn, ...mutationOptions } as UseMutationOptions<TData, TError, { data: BodyType<FormData> }, TContext>
}

export type ImportHiddifyUsersMutationResult = NonNullable<Awaited<ReturnType<typeof importHiddifyUsers>>>
export type ImportHiddifyUsersMutationBody = BodyType<FormData>
export type ImportHiddifyUsersMutationError = ErrorType<unknown>

export const useImportHiddifyUsers = <TData = Awaited<ReturnType<typeof importHiddifyUsers>>, TError = ErrorType<unknown>, TContext = unknown>(options?: {
  mutation?: UseMutationOptions<TData, TError, { data: BodyType<FormData> }, TContext>
}): UseMutationResult<TData, TError, { data: BodyType<FormData> }, TContext> => {
  const mutationOptions = getImportHiddifyUsersMutationOptions(options)

  return useMutation(mutationOptions)
}
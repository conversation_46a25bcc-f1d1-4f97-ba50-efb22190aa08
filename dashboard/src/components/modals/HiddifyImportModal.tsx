import { useState, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { useImportHiddifyUsers } from '@/service/api'
import { toast } from 'sonner'
import { queryClient } from '@/utils/query-client'

interface HiddifyImportModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function HiddifyImportModal({ isOpen, onClose }: HiddifyImportModalProps) {
  const { t } = useTranslation()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [setUnlimitedExpire, setSetUnlimitedExpire] = useState(false)
  const [enableSmartUsernameParsing, setEnableSmartUsernameParsing] = useState(true)
  const [selectedProtocols, setSelectedProtocols] = useState<string[]>([])
  const importMutation = useImportHiddifyUsers()

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file && file.type === 'application/json') {
      setSelectedFile(file)
    } else {
      toast.error(t('hiddifyImport.invalidFile'), {
        description: t('hiddifyImport.invalidFileDesc')
      })
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleProtocolToggle = (protocol: string) => {
    setSelectedProtocols(prev => 
      prev.includes(protocol) 
        ? prev.filter(p => p !== protocol) 
        : [...prev, protocol]
    )
  }

  const handleImport = async () => {
    if (!selectedFile) {
      toast.error(t('hiddifyImport.noFileSelected'))
      return
    }

    if (selectedProtocols.length === 0) {
      toast.error(t('hiddifyImport.noProtocolsSelected'))
      return
    }

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('set_unlimited_expire', setUnlimitedExpire.toString())
      formData.append('enable_smart_username_parsing', enableSmartUsernameParsing.toString())
      formData.append('selected_protocols', JSON.stringify(selectedProtocols))
      formData.append('inbounds', JSON.stringify({}))
      formData.append('proxies', JSON.stringify({}))

      const result = await importMutation.mutateAsync({ data: formData })
      
      toast.success(t('hiddifyImport.importSuccess'), {
        description: t('hiddifyImport.importStats', {
          successful: result.successful_imports,
          failed: result.failed_imports
        })
      })
      
      // Refresh user data
      queryClient.invalidateQueries({ queryKey: ['/api/users'] })
      onClose()
    } catch (error) {
      toast.error(t('hiddifyImport.importError'), {
        description: t('hiddifyImport.importErrorDesc')
      })
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{t('hiddifyImport.title')}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <Label>{t('hiddifyImport.selectFile')}</Label>
            <Input
              ref={fileInputRef}
              type="file"
              accept=".json"
              onChange={handleFileChange}
            />
            {selectedFile && (
              <p className="text-sm text-green-600">
                {t('hiddifyImport.fileSelected', { filename: selectedFile.name })}
              </p>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="unlimited-expire"
              checked={setUnlimitedExpire}
              onCheckedChange={(checked) => setSetUnlimitedExpire(checked as boolean)}
            />
            <Label htmlFor="unlimited-expire">
              {t('hiddifyImport.unlimitedExpiration')}
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="smart-username"
              checked={enableSmartUsernameParsing}
              onCheckedChange={(checked) => setEnableSmartUsernameParsing(checked as boolean)}
            />
            <Label htmlFor="smart-username">
              {t('hiddifyImport.smartUsernameParsing')}
            </Label>
          </div>
          
          <div className="space-y-2">
            <Label>{t('hiddifyImport.protocolSelection')}</Label>
            <div className="grid grid-cols-2 gap-2">
              {['vmess', 'vless', 'trojan', 'shadowsocks'].map(protocol => (
                <div key={protocol} className="flex items-center space-x-2">
                  <Checkbox
                    id={protocol}
                    checked={selectedProtocols.includes(protocol)}
                    onCheckedChange={() => handleProtocolToggle(protocol)}
                  />
                  <Label htmlFor={protocol} className="capitalize">
                    {protocol}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="flex justify-end space-x-2">
          <Button variant="outline" onClick={onClose}>
            {t('cancel')}
          </Button>
          <Button 
            onClick={handleImport}
            disabled={!selectedFile || selectedProtocols.length === 0 || importMutation.isPending}
          >
            {importMutation.isPending ? t('hiddifyImport.importing') : t('hiddifyImport.importUsers')}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
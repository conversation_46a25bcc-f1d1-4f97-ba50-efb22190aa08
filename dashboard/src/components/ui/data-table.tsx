import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table'
import React, { useState, useEffect } from 'react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { cn } from '@/lib/utils'
import { LoaderCircle } from 'lucide-react'
import { useTranslation } from 'react-i18next'

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  loading?: boolean
}

export function DataTable<TData, TValue>({ columns, data, loading = false }: DataTableProps<TData, TValue>) {
  const { t } = useTranslation()
  const [visibleRows, setVisibleRows] = useState<number>(0)

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  })

  useEffect(() => {
    if (loading) {
      setVisibleRows(0)
      return
    }

    const totalRows = table.getRowModel().rows.length
    let currentRow = 0

    const loadNextRow = () => {
      if (currentRow < totalRows) {
        setVisibleRows(prev => prev + 1)
        currentRow++
        setTimeout(loadNextRow, 50)
      }
    }

    loadNextRow()
  }, [loading, table.getRowModel().rows.length])

  const LoadingState = (
    <TableRow>
      <TableCell colSpan={columns.length} className="h-24">
        <div className="flex flex-col items-center justify-center gap-2">
          <LoaderCircle className="h-8 w-8 animate-spin text-primary" />
          <span className="text-sm">{t('loading')}</span>
        </div>
      </TableCell>
    </TableRow>
  )

  const EmptyState = (
    <TableRow>
      <TableCell colSpan={columns.length} className="h-24 text-center">
        <span className="text-muted-foreground">{t('noResults')}</span>
      </TableCell>
    </TableRow>
  )

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          {table.getHeaderGroups().map(headerGroup => (
            <TableRow key={headerGroup.id} className="uppercase">
              {headerGroup.headers.map((header) => (
                <TableHead
                  key={header.id}
                  className="text-xs"
                >
                  {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>
        <TableBody>
          {loading ? LoadingState : table.getRowModel().rows?.length ? (
            table.getRowModel().rows.map((row, index) => (
              <TableRow
                key={row.id}
                className={cn(
                  index >= visibleRows && 'opacity-0',
                  'transition-all duration-300 ease-in-out'
                )}
                style={{
                  transform: index >= visibleRows ? 'translateY(10px)' : 'translateY(0)',
                }}
                data-state={row.getIsSelected() && 'selected'}
              >
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id} className="py-2 text-sm">
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : EmptyState}
        </TableBody>
      </Table>
    </div>
  )
}

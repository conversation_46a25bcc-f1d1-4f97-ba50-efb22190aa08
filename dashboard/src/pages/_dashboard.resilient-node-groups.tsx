import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, Edit, Trash2, Server } from 'lucide-react'
import { 
  useGetResilientNodeGroups, 
  useCreateResilientNodeGroup, 
  useUpdateResilientNodeGroup, 
  useDeleteResilientNodeGroup,
  ResilientNodeGroupResponse
} from '@/service/api'
import { toast } from 'sonner'
import { queryClient } from '@/utils/query-client'
import { DataTable } from '@/components/ui/data-table'
import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useGetNodes } from '@/service/api'
import { MultiSelect } from '@/components/ui/multi-select'

const formSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }).max(100),
  client_strategy_hint: z.enum(['url-test', 'fallback', 'load-balance', 'client-default', '']),
  node_ids: z.array(z.number()).min(1, { message: 'At least one node must be selected' })
})

type FormValues = z.infer<typeof formSchema>

export default function ResilientNodeGroups() {
  const { t } = useTranslation()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingGroup, setEditingGroup] = useState<ResilientNodeGroupResponse | null>(null)
  
  const { data: groupsData, isLoading: groupsLoading } = useGetResilientNodeGroups()
  const { data: nodesData } = useGetNodes()
  
  const createGroupMutation = useCreateResilientNodeGroup()
  const updateGroupMutation = useUpdateResilientNodeGroup()
  const deleteGroupMutation = useDeleteResilientNodeGroup()
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      client_strategy_hint: 'client-default',
      node_ids: []
    }
  })
  
  useEffect(() => {
    if (editingGroup) {
      form.reset({
        name: editingGroup.name,
        client_strategy_hint: editingGroup.client_strategy_hint,
        node_ids: editingGroup.node_ids
      })
    } else {
      form.reset({
        name: '',
        client_strategy_hint: 'client-default',
        node_ids: []
      })
    }
  }, [editingGroup, form])
  
  const handleSubmit = async (values: FormValues) => {
    try {
      if (editingGroup) {
        await updateGroupMutation.mutateAsync({
          groupId: editingGroup.id,
          data: values
        })
        toast.success(t('resilientNodeGroups.updateSuccess'))
      } else {
        await createGroupMutation.mutateAsync(values)
        toast.success(t('resilientNodeGroups.createSuccess'))
      }
      
      queryClient.invalidateQueries({ queryKey: ['/api/resilient-node-groups'] })
      setIsDialogOpen(false)
      setEditingGroup(null)
    } catch (error) {
      toast.error(t('error'), {
        description: t('resilientNodeGroups.operationFailed')
      })
    }
  }
  
  const handleDelete = async (groupId: number) => {
    try {
      await deleteGroupMutation.mutateAsync({ groupId })
      toast.success(t('resilientNodeGroups.deleteSuccess'))
      queryClient.invalidateQueries({ queryKey: ['/api/resilient-node-groups'] })
    } catch (error) {
      toast.error(t('error'), {
        description: t('resilientNodeGroups.deleteFailed')
      })
    }
  }
  
  const columns: ColumnDef<ResilientNodeGroupResponse>[] = [
    {
      accessorKey: 'name',
      header: t('resilientNodeGroups.name'),
    },
    {
      accessorKey: 'client_strategy_hint',
      header: t('resilientNodeGroups.strategy'),
      cell: ({ row }) => {
        const strategy = row.original.client_strategy_hint
        return (
          <Badge variant="secondary">
            {strategy || t('resilientNodeGroups.clientDefault')}
          </Badge>
        )
      }
    },
    {
      accessorKey: 'nodes',
      header: t('resilientNodeGroups.nodes'),
      cell: ({ row }) => {
        const nodes = row.original.nodes
        return (
          <div className="flex flex-wrap gap-1">
            {nodes.map(node => (
              <Badge key={node.id} variant="outline">
                <Server className="mr-1 h-3 w-3" />
                {node.name}
              </Badge>
            ))}
          </div>
        )
      }
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setEditingGroup(row.original)
              setIsDialogOpen(true)
            }}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDelete(row.original.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ]
  
  return (
    <div className="flex flex-col gap-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>{t('resilientNodeGroups.title')}</CardTitle>
              <CardDescription>{t('resilientNodeGroups.description')}</CardDescription>
            </div>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={() => setEditingGroup(null)}>
                  <Plus className="mr-2 h-4 w-4" />
                  {t('resilientNodeGroups.addGroup')}
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>
                    {editingGroup 
                      ? t('resilientNodeGroups.editGroup') 
                      : t('resilientNodeGroups.createGroup')}
                  </DialogTitle>
                </DialogHeader>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('resilientNodeGroups.name')}</FormLabel>
                          <FormControl>
                            <Input {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="client_strategy_hint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('resilientNodeGroups.strategy')}</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="url-test">{t('resilientNodeGroups.strategies.urlTest')}</SelectItem>
                              <SelectItem value="fallback">{t('resilientNodeGroups.strategies.fallback')}</SelectItem>
                              <SelectItem value="load-balance">{t('resilientNodeGroups.strategies.loadBalance')}</SelectItem>
                              <SelectItem value="client-default">{t('resilientNodeGroups.strategies.clientDefault')}</SelectItem>
                              <SelectItem value="">{t('resilientNodeGroups.strategies.none')}</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="node_ids"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>{t('resilientNodeGroups.nodes')}</FormLabel>
                          <FormControl>
                            <MultiSelect
                              options={(nodesData || []).map(node => ({
                                label: node.name,
                                value: node.id.toString()
                              }))}
                              selected={field.value.map(id => id.toString())}
                              onChange={values => field.onChange(values.map(v => parseInt(v)))}
                              placeholder={t('resilientNodeGroups.selectNodes')}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsDialogOpen(false)}
                      >
                        {t('cancel')}
                      </Button>
                      <Button type="submit" disabled={createGroupMutation.isPending || updateGroupMutation.isPending}>
                        {editingGroup ? t('update') : t('create')}
                      </Button>
                    </div>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable
            columns={columns}
            data={groupsData || []}
            loading={groupsLoading}
          />
        </CardContent>
      </Card>
    </div>
  )
}
{"resilientNodeGroups": {"title": "Resilient Node Groups", "description": "Manage resilient node groups for improved reliability", "addGroup": "Add Group", "createGroup": "Create Resilient Node Group", "editGroup": "Edit Resilient Node Group", "name": "Name", "strategy": "Client Strategy", "nodes": "Nodes", "selectNodes": "Select nodes", "createSuccess": "Resilient node group created successfully", "updateSuccess": "Resilient node group updated successfully", "deleteSuccess": "Resilient node group deleted successfully", "operationFailed": "Failed to perform operation on resilient node group", "deleteFailed": "Failed to delete resilient node group", "strategies": {"urlTest": "URL Test", "fallback": "Fallback", "loadBalance": "Load <PERSON>", "clientDefault": "<PERSON><PERSON>", "none": "None"}, "clientDefault": "<PERSON><PERSON>"}, "hiddifyImport": {"title": "Import Users from Hiddify", "selectFile": "Select Hiddify Backup File", "fileSelected": "File selected: {{filename}}", "invalidFile": "Invalid File", "invalidFileDesc": "Please select a valid JSON file.", "unlimitedExpiration": "Set unlimited expiration for all users", "unlimitedExpirationDesc": "If enabled, all imported users will have unlimited expiration (expire = 0), ignoring package_days from Hiddify.", "smartUsernameParsing": "Enable smart username & note parsing", "smartUsernameParsingDesc": "If enabled, names like '1234 <PERSON>' will be split: '1234' as username, '<PERSON>' as note. Otherwise, the full name will be used as username.", "protocolSelection": "Select Protocols", "protocolSelectionDesc": "Choose which protocols the imported users should have access to.", "noFileSelected": "No File Selected", "noProtocolsSelected": "No Protocols Selected", "importing": "Importing users...", "importUsers": "Import Users", "importComplete": "Import Complete", "importStats": "{{successful}} users imported successfully, {{failed}} failed.", "importSuccess": "Import Successful", "importSuccessDesc": "{{successful}} users imported successfully. {{failed}} users failed to import.", "importWarning": "Import Completed with Warnings", "importWarningDesc": "{{failed}} users failed to import. Check the details below.", "importError": "Import Failed", "importErrorDesc": "An error occurred during import. Please try again."}}
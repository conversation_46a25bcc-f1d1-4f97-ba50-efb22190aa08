{"marzban": "Marzban", "dashboard": "Панель управления", "master": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dashboardDescription": "Панель управления Marzban", "active": "активный", "online": "В сети", "platform": "Платформа", "allStatuses": "Все статусы", "status": "Статус", "statistics": "Статистика", "selectAll": "Выбрать все", "statistics.realTimeStats": "Статистика в реальном времени", "statistics.serverPerformance": "Производительность сервера", "statistics.realTimeData": "Данные в реальном времени", "statistics.realtimeDescription": "Живая статистика и графики для ваших серверов и пользователей.", "hosts": "Хо<PERSON>ты", "success": "Успешно", "error": "Ошибка", "modifying": "Изменение...", "removing": "Удаление...", "creating": "Создание...", "monitorUsers": "Мониторинг пользователей", "alltime": "Все время", "nodes": {"title": "Узлы", "description": "Управление узлами", "addNode": "Добавить узел", "editNode": "Редактировать узел", "deleteNode": "Удалить узел", "nodeName": "Имя узла", "nodeAddress": "Адрес узла", "nodePort": "Порт узла", "usageCoefficient": "Коэффициент использования", "connectionType": "Тип соединения", "serverCA": "Сертификат сервера", "keepAlive": "Поддержание соединения", "maxLogs": "Максимум логов", "maxLogsPlaceHolder": "Введите максимальное количество логов (в секундах)", "status": "Статус", "actions": "Действия", "connected": "Подключено", "disconnected": "Отключено", "disabled": "Отключено", "enabled": "Включено", "createSuccess": "Узел «{{name}}» успешно создан", "createFailed": "Не удалось создать узел «{{name}}»", "editSuccess": "Узел «{{name}}» успешно обновлен", "editFailed": "Не удалось обновить узел «{{name}}»", "deleteSuccess": "Узел «{{name}}» успешно удален", "deleteFailed": "Не удалось удалить узел «{{name}}»", "enableSuccess": "Узел «{{name}}» успешно включен", "enableFailed": "Не удалось включить узел «{{name}}»", "disableSuccess": "Узел «{{name}}» успешно отключен", "disableFailed": "Не удалось отключить узел «{{name}}»", "certificate": "Сертификат", "certificateCopied": "Сертификат скопирован в буфер обмена", "certificateDownloaded": "Сертификат успешно загружен", "certificateDescription": "Для настройки узла необходимо настроить этот сертификат для установки безопасного соединения между основным сервером и узлом", "selectNode": "Выберите узел", "logs": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Просмотр и мониторинг журналов узла", "noLogs": "Нет доступных журналов", "loading": "Загрузка журналов...", "timestamps": "Метки времени", "autoScroll": "Автопрокрутка", "scrollToEnd": "Прокрутить вниз", "clear": "Очистить журналы", "search": "Поиск в журналах", "filter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "levels": "Уровни журнала", "debug": "Отладка", "info": "Инфо", "warning": "Предупреждение", "error": "Ошибка", "maxLogsTooltip": "Максимальное количество записей в памяти", "memory": "Использование памяти", "custom": "Пользовательский", "unlimited": "Неограниченно", "memoryWarning": "Высокие значения могут вызвать проблемы с производительностью браузера", "setCustom": "Установить пользовательский лимит", "customLimit": "Введите пользовательский лимит", "apply": "Применить"}}, "groups": "Группы", "documentation": "Документация", "discussionGroup": "Группа обсуждений", "github": "GitHub", "community": "Сообщество", "supportUs": "Поддержать нас", "manageHosts": "Управление и контроль хостов.", "manageSettings": "Управление и контроль настроек.", "settings": {"title": "Настройки", "notifications": {"title": "Уведомления", "description": "Настройка уведомлений Telegram и Discord", "loadError": "Не удалось загрузить настройки уведомлений", "saveSuccess": "Настройки уведомлений успешно сохранены", "saveFailed": "Не удалось сохранить настройки уведомлений", "cancelSuccess": "Изменения отменены и восстановлены исходные настройки", "activeTypes": "Активные типы", "filterTitle": "Фильтр уведомлений", "filterDescription": "Выберите события, которые должны вызывать уведомления для информирования о важных системных действиях", "types": {"admin": "Админы", "core": "Ядро", "group": "Группы", "host": "Хо<PERSON>ты", "login": "Вход", "node": "Узлы", "user": "Пользователи", "userTemplate": "Шаблоны", "daysLeft": "Истечение", "percentageReached": "Использование"}, "telegram": {"title": "Telegram", "description": "Настройка уведомлений Telegram-бота для получения оповещений в реальном времени в вашем чате или канале Telegram", "apiToken": "API токен бота", "adminId": "ID чата администратора", "channelId": "ID канала", "topicId": "ID темы"}, "discord": {"title": "Discord", "description": "Настройка уведомлений Discord webhook для получения оповещений прямо в каналы вашего Discord сервера", "webhookUrl": "URL webhook"}, "advanced": {"title": "Расширенные настройки", "description": "Настройка расширенного поведения уведомлений, включая политики повторных попыток и настройки прокси", "maxRetries": "Максимум повторов", "proxyUrl": "URL прокси"}}, "subscriptions": {"title": "Подписки", "description": "Настройка URL подписок, интервалов обновления и правил для конкретных клиентов", "loadError": "Не удалось загрузить настройки подписки", "saveSuccess": "Настройки подписки успешно сохранены", "saveFailed": "Не удалось сохранить настройки подписки", "cancelSuccess": "Изменения отменены и восстановлены исходные настройки", "general": {"title": "Общие настройки", "description": "Основные параметры конфигурации подписки", "urlPrefix": "Префикс URL", "urlPrefixPlaceholder": "sub", "urlPrefixDescription": "Базовый URL для ссылок подписки", "updateInterval": "Интервал обновления (часы)", "updateIntervalDescription": "Как часто клиенты должны проверять обновления", "supportUrl": "URL поддержки", "supportUrlPlaceholder": "https://support.example.com", "supportUrlDescription": "URL для поддержки пользователей и помощи", "profileTitle": "Название профиля", "profileTitlePlaceholder": "Мой VPN сервис", "profileTitleDescription": "Отображаемое имя для профиля подписки", "hostStatusFilter": "Фильтр статуса хоста", "hostStatusFilterDescription": "Фильтровать хосты на основе их статуса"}, "rules": {"title": "Правила подписки", "description": "Настройка правил подписки для конкретных клиентов и форматов", "addRule": "Добавить правило", "removeRule": "Удалить правило", "pattern": "Шабл<PERSON>н", "patternPlaceholder": "Введите шаблон (например, шаблон user-agent)", "patternDescription": "Шаблон для сопоставления с запросами клиентов", "target": "Целевой формат", "targetDescription": "Формат конфигурации для обслуживания этого шаблона", "noRules": "Правила не настроены. Добавьте правила для настройки поведения подписки для разных клиентов."}, "formats": {"title": "Форматы ручной подписки", "description": "Включение или отключение определенных форматов подписки для ручных запросов", "links": "Ссылки", "linksDescription": "Ссылки подписки в виде простого текста", "linksBase64": "Ссылки (Base64)", "linksBase64Description": "Ссылки подписки в кодировке Base64", "xray": "Xray", "xrayDescription": "Формат конфигураци<PERSON> Xray", "singBox": "Sing-box", "singBoxDescription": "Формат конфигурации Sing-box", "clash": "Clash", "clashDescription": "Формат конфигурации Clash", "clashMeta": "<PERSON><PERSON>", "clashMetaDescription": "Формат конфигурации Clash Meta", "outline": "Outline", "outlineDescription": "Формат конфигурации Outline VPN"}, "configFormats": {"links": "Ссылки", "links_base64": "Ссылки (Base64)", "xray": "Xray", "sing_box": "Sing-box", "clash": "Clash", "clash_meta": "<PERSON><PERSON>", "outline": "Outline", "block": "Блок"}}, "telegram": {"title": "Telegram", "description": "Настройка интеграции Telegram бота и связанных настроек для вашей системы", "loadError": "Не удалось загрузить настройки Telegram. Пожалуйста, попробуйте снова.", "saveSuccess": "Настройки Telegram успешно сохранены", "saveFailed": "Не удалось сохранить настройки Telegram", "cancelSuccess": "Изменения отменены и восстановлены первоначальные настройки Telegram", "general": {"title": "Основные настройки", "description": "Базовая конфигурация Telegram бота и настройки подключения", "enable": "Включить бота Telegram", "enableDescription": "Включить или отключить функциональность бота Telegram для вашей системы", "token": "Токен API бота", "tokenPlaceholder": "Введите токен вашего Telegram бота", "tokenDescription": "Токен бота, полученный от @BotFather в Telegram", "webhookUrl": "URL Webhook", "webhookUrlPlaceholder": "https://your-domain.com/webhook", "webhookUrlDescription": "URL, на который Telegram будет отправлять обновления", "webhookSecret": "Секрет Webhook", "webhookSecretPlaceholder": "Введите секрет webhook", "webhookSecretDescription": "Секретный токен для безопасности webhook", "proxyUrl": "URL прокси", "proxyUrlPlaceholder": "socks5://proxy.example.com:1080", "proxyUrlDescription": "URL прокси для подключений к API Telegram (необязательно)"}, "advanced": {"title": "Расширенные настройки", "description": "Расширенные функции и конфигурации бота Telegram", "miniAppLogin": "Включить вход через Mini App", "miniAppLoginDescription": "Разрешить пользователям входить через мини-приложения Telegram"}}, "discord": {"title": "Discord", "description": "Настройте интеграцию Discord бота и связанные параметры для вашей системы", "loadError": "Не удалось загрузить настройки Discord. Попробуйте еще раз.", "saveSuccess": "Настройки Discord успешно сохранены", "saveFailed": "Не удалось сохранить настройки Discord", "cancelSuccess": "Изменения отменены и восстановлены исходные настройки Discord", "general": {"title": "Основные настройки", "description": "Базовая конфигурация Discord бота и настройки подключения", "enable": "Включить Discord бота", "enableDescription": "Включить или отключить функциональность Discord бота для вашей системы", "token": "То<PERSON>ен бота", "tokenPlaceholder": "Введите ваш токен Discord бота", "tokenDescription": "Токен бота, полученный из Discord Developer Portal", "proxyUrl": "URL прокси", "proxyUrlPlaceholder": "socks5://proxy.example.com:1080", "proxyUrlDescription": "URL прокси для подключений к Discord API (необязательно)"}}, "webhook": {"title": "Веб-хуки", "description": "Настройка уведомлений веб-хуков и конечных точек для вашей системы", "loadError": "Не удалось загрузить настройки веб-хуков. Пожалуйста, попробуйте снова.", "saveSuccess": "Настройки веб-хуков успешно сохранены", "saveFailed": "Не удалось сохранить настройки веб-хуков", "cancelSuccess": "Изменения отменены и настройки веб-хуков восстановлены", "general": {"title": "Основные настройки", "description": "Базовая конфигурация веб-хуков и настройки подключения", "enable": "Включить веб-хуки", "enableDescription": "Включить или отключить уведомления веб-хуков для вашей системы", "timeout": "Тайм-аут (секунды)", "timeoutDescription": "Время ожидания запроса для вызовов веб-хуков (1-300 секунд)", "recurrent": "Количество повторов", "recurrentDescription": "Количество повторных попыток для неудачных веб-хуков (1-24)", "proxyUrl": "URL прокси", "proxyUrlPlaceholder": "http://proxy.example.com:8080", "proxyUrlDescription": "URL прокси для запросов веб-хуков (необязательно)"}, "webhooks": {"title": "Конечные точки веб-хуков", "description": "Настройка URL веб-хуков и аутентификации", "add": "Добавить веб-хук", "addFirst": "Добавить первый веб-хук", "webhook": "Веб-хук", "empty": "Веб-хуки не настроены. Добавьте вашу первую конечную точку веб-хука.", "name": "Имя", "namePlaceholder": "Мой веб-хук", "url": "URL", "secret": "Секретный ключ", "secretPlaceholder": "Введите секретный ключ веб-хука"}, "triggers": {"daysLeft": {"title": "Уведомления об оставшихся днях", "description": "Отправлять уведомления, когда у пользователей остается определенное количество дней", "empty": "Триггеры не настроены"}, "usagePercent": {"title": "Уведомления о проценте использования", "description": "Отправлять уведомления, когда пользователи достигают определенного процента использования", "empty": "Триггеры не настроены"}}}, "cleanup": {"title": "Очистка", "description": "Управление просроченными аккаунтами и сброс использования данных для обслуживания системы", "loadError": "Не удалось загрузить настройки очистки. Пожалуйста, попробуйте еще раз.", "saveSuccess": "Операция очистки выполнена успешно", "saveFailed": "Не удалось завершить операцию очистки", "cancelSuccess": "Операция очистки отменена", "expiredUsers": {"title": "Удаление просроченных аккаунтов", "description": "Удаление аккаунтов, которые просрочены в определенном диапазоне дат", "expiredAfter": "Просрочено после", "expiredBefore": "Просрочено до", "expiredAfterPlaceholder": "Выберите дату начала", "expiredBeforePlaceholder": "Выберите дату окончания", "dateRange": "Диа<PERSON>азон дат", "deleteExpired": "Удалить просроченные", "deleting": "Удаление...", "deleteSuccess": "{{count}} просроченных аккаунтов успешно удалено", "deleteFailed": "Не удалось удалить просроченные аккаунты", "confirmDelete": "Удалить просроченные аккаунты", "confirmDeleteMessage": "Вы уверены, что хотите удалить все просроченные аккаунты в выбранном диапазоне дат? Это действие нельзя отменить.", "noDateSelected": "Пожалуйста, выберите хотя бы одну дату", "selectDateRange": "Выберите диапазон дат для просроченных аккаунтов для удаления (доступны только прошедшие даты)"}, "resetUsage": {"title": "Сброс всего использования данных", "description": "Сброс использования данных для всех аккаунтов в системе", "resetAll": "Сбросить все использование", "resetting": "Сброс...", "resetSuccess": "Все использование данных успешно сброшено", "resetFailed": "Не удалось сбросить использование данных", "confirmReset": "Сбросить все использование данных", "confirmResetMessage": "Вы уверены, что хотите сбросить использование данных для всех аккаунтов? Это действие нельзя отменить.", "warning": "Это действие сбросит использование данных для ВСЕХ аккаунтов в системе"}, "clearUsageData": {"title": "Очистка данных использования", "description": "Окончательное удаление данных использования из выбранных таблиц базы данных", "selectTable": "Выберите таблицу", "selectTablePlaceholder": "Выберите таблицу для очистки", "noTableSelected": "Пожалуйста, выберите таблицу для очистки", "dataAfter": "Данные после", "dataBefore": "Данные до", "dataAfterPlaceholder": "Выберите дату начала", "dataBeforePlaceholder": "Выберите дату окончания", "selectDateRange": "Выберите диапазон дат для фильтрации данных по времени создания (необязательно - оставьте пустым для очистки всех данных)", "clearData": "Очистить данные", "clearing": "Очистка...", "clearSuccess": "Данные из таблицы {{table}} успешно очищены", "clearFailed": "Не удалось очистить данные использования", "confirmClear": "Подтвердить очистку данных", "confirmClearMessage": "Вы уверены, что хотите окончательно удалить все данные из таблицы {{table}}? Это действие нельзя отменить.", "warning": "Предупреждение: Это действие окончательно удалит все данные использования из выбранной таблицы. Это действие нельзя отменить.", "tables": {"nodeUserUsages": "Использование пользователей узла", "userUsages": "Использование пользователей"}}}, "theme": {"title": "темы"}}, "saving": "Сохранение...", "general": "Общие", "core": "Ядра", "activeUsers": "Пользователи", "apply": "Применить", "cancel": "Отмена", "created": "Создано", "by": "от", "ip": "IP", "admin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sudo": "Суперпользователь", "save": "Сохранить", "test": "Тест", "testing": "Тестирование", "confirm": "Подтвердить", "admins": {"title": "Администраторы", "description": "Управление системными администраторами", "createAdmin": "Создать администратора", "editAdmin": "Редактировать администратора", "deleteAdmin": "Удалить администратора", "username": "Имя пользователя", "password": "Пароль", "isSudo": "Супер администратор", "createdAt": "Дата создания", "actions": "Действия", "createSuccess": "Админист<PERSON><PERSON><PERSON>о<PERSON> «{{name}}» успешно создан", "createFailed": "Не удалось создать администратора «{{name}}»", "editSuccess": "Админис<PERSON><PERSON><PERSON><PERSON>о<PERSON> «{{name}}» успешно обновлен", "editFailed": "Не удалось обновить администратора «{{name}}»", "deleteSuccess": "Администратор {{name}} успешно удалён", "deleteFailed": "Не удалось удалить администратора {{name}}", "enterUsername": "Введите имя пользователя", "enterPassword": "Введите пароль", "sudo": "Права супер администратора", "edit": "Сохранить изменения", "create": "Создать администратора", "status": "Статус", "role": "Роль", "total.users": "Всего пользователей", "used.traffic": "Использованный трафик", "total": "Всего администра<PERSON><PERSON>ров", "active": "Активные администраторы", "disable": "Отключённые администраторы", "telegramId": "Telegram ID", "discord": "Вебхук Discord", "supportUrl": "URL поддержки", "subDomain": "Домен подписки", "profile": "Заголовок профиля", "subTemplate": "Путь шаблона подписки", "passwordConfirm": "Подтвердите пароль", "enterPasswordConfirm": "Введите подтверждение пароля", "disableSuccess": "Админис<PERSON><PERSON><PERSON><PERSON>о<PERSON> «{{name}}» был успешно отключен", "enableSuccess": "Админист<PERSON><PERSON><PERSON>о<PERSON> «{{name}}» был успешно включен", "disableFailed": "Не удалось отключить администратора «{{name}}»", "enableFailed": "Не удалось включить администратора «{{name}}»", "resetUsersUsage": "Админ сбросить использование пользователей", "resetUsageSuccess": "Использование пользователя «{{name}}» успешно сброшено администратором", "resetUsageFailed": "Не удалось сбросить использование пользователя «{{name}}» администратором", "reset": "Сброс использования", "discordId": "Discord ID", "lifetime.used.traffic": "Всего", "used": {"traffic": "Использование трафика"}, "monitor": {"traffic": "Мониторинг использования трафика администратором во времени", "no_traffic": "Данные о трафике отсутствуют"}}, "shortcuts": {"title": "Горячие клавиши", "description": "Управляйте горячими клавишами панели для быстрого доступа", "add": "Добавить горячую клавишу", "empty": "Горячие клавиши не настроены"}, "quickActions": {"title": "Быстрые действия", "description": "Выберите действие для создания новых элементов в системе", "comingSoon": "Скоро"}, "resetUsersUsage.prompt": "Вы уверены, что хотите сбросить использование пользователя-админа <b>{{name}}</b>?", "admin.disable": "Администратор отключён", "admin.enable": "Администратор включён", "deleteAdmin.prompt": "Вы уверены, что хотите удалить администратора <b>{{name}}</b>?", "activeUsers.prompt": "Вы хотите активировать всех пользователей под управлением {{name}}?", "disableUsers.prompt": "Вы хотите отключить всех пользователей под управлением {{name}}?", "manageAccounts": "Контроль, обновление и упорядочивание учетных записей", "manageGroups": "Контроль, обновление и упорядочивание групп пользователей", "manageNodes": "Мо<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, управление, добавление, редактирование, удаление узлов", "onlineUsers": "Пользователи онлайн", "totalUsers": "Всего пользователей", "edit": "Редактировать", "editGroup": "Редактировать группу", "disable": "Отключить", "warning": "Предупреждение", "remove": "Удалить", "modify": "Изменить", "templates.userTemplates": "Шаблоны", "editUserTemplateModal.title": "Редактировать шаблон пользователя", "userTemplateModal.title": "Создать шаблон пользователя", "templates": {"title": "Шаблоны", "description": "Управление шаблонами.", "addTemplate": "Создать шаблон", "editSuccess": "Шаблон пользователя «{{name}}» успешно обновлен", "createSuccess": "Шаблон пользователя «{{name}}» успешно создан", "editFailed": "Не удалось обновить шаблон пользователя «{{name}}»", "createFailed": "Не удалось создать шаблон пользователя «{{name}}»", "name": "Имя", "status": "Статус", "prefix": "Префикс имени пользователя", "suffix": "Суффикс имени пользователя", "dataLimit": "Лимит данных", "expire": "Срок действия", "onHoldTimeout": "Тайм-аут при ожидании", "method": "Метод", "flow": "Поток", "groups": "Группы", "userDataLimitStrategy": "Стратегия сброса лимита данных", "resetUsage": "Сброс использования", "groupsExistingWarning": "Вы еще не добавили ни одной <a>группы</a>.", "deleteSuccess": "Шаб<PERSON>он {{name}} успешно удалён", "deleteFailed": "Не удалось удалить шаблон {{name}}", "deleteUserTemplateTitle": "Удалить пользовательский шаблон", "deleteUserTemplatePrompt": "Вы уверены, что хотите удалить шаблон <b>{{name}}</b>?", "duplicateSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> «{{name}}» успешно дублирован", "duplicateFailed": "Не удалось дублировать шаблон «{{name}}»", "enableSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> «{{name}}» был успешно включён", "disableSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> «{{name}}» был успешно отключён", "enableFailed": "Не удалось включить администратора «{{name}}»", "disableFailed": "Не удалось отключить администратора «{{name}}»"}, "core.configuration": "Конфигурация", "core.generalErrorMessage": "Что-то пошло не так, пожалуйста, проверьте конфигурацию", "core.logs": "Логи", "core.restartCore": "Перезагрузить ядро", "core.restarting": "Перезагрузка...", "core.save": "Сохранить", "core.socket.closed": "Закрыто", "core.socket.connected": "Подключено", "core.socket.connecting": "Соединение...", "core.socket.not_connected": "Не подключено", "core.successMessage": "Основные настройки успешно обновлены", "core.title": "Основные настройки", "core.toggleSuccess": "Ядро «{{name}}» успешно переключено", "core.toggleFailed": "Не удалось переключить ядро «{{name}}»", "core.deleteSuccess": "Ядро «{{name}}» успешно удалено", "core.deleteFailed": "Не удалось удалить ядро «{{name}}»", "core.deleteConfirm": "Вы уверены, что хотите удалить «{{name}}»?", "createNewUser": "Создать нового пользователя", "createUser": "Создать", "createGroup": "Создать группу", "dataUsage": "Трафик", "dateFormat": "MMMM d, yyy", "delete": "Удалить", "deleteNode.deleteSuccess": "Узел {{name}} успешно удалён", "deleteNode.prompt": "Вы уверены, что хотите удалить узел <b>{{name}}</b>?", "deleteNode.title": "Удалить узел", "deleteHost.title": "Удалить хост", "deleteHost.prompt": "Вы уверены, что хотите удалить хост <b>{{name}}</b>?", "deleteHost.deleteSuccess": "Хост {{name}} успешно удален", "deleteHost.deleteFailed": "Не удалось удалить хост {{name}}", "editHost.title": "Редактировать хост", "editNode.editSuccess": "Узел {{name}} успешно обновлен", "editNode.title": "Редактировать узел", "deleteUser.deleteSuccess": "{{username}} успешно удалён.", "deleteUser.prompt": "Вы уверены, что хотите удалить пользователя <b>{{username}}</b>?", "deleteUser.title": "Удалить пользователя", "disabled": "disabled", "expire": "Истекает", "expired": "Истекло {{time}} назад", "expires": "Истекает через {{time}}", "header.donation": "Пожертвование", "header.hostSettings": "Настройки хоста", "header.logout": "Выйти", "header.nodeSettings": "Настройки узлов", "header.nodesUsage": "Использование узлов", "hostsDialog.addHost": "Добави<PERSON>ь хост", "hostsDialog.advancedOptions": "Дополнительные опции", "hostsDialog.useSniAsHost": "Использовать SNI как хост", "hostsDialog.alpn": "ALPN", "hostsDialog.apply": "Применить", "hostsDialog.currentServer": "IP текущего сервера", "hostsDialog.currentServerv6": "IPv6 текущего сервера", "hostsDialog.dataLimit": "Лимит трафика пользователя", "hostsDialog.dataUsage": "Использованный трафик пользователя", "hostsDialog.desc": "Используйте эти переменные, чтобы сделать его динамическим", "hostsDialog.expireDate": "Дата истечения срока пользователя", "hostsDialog.fingerprint": "Отпечаток", "hostsDialog.fragment": {"title": "Фрагментация", "packets": "Пакеты", "packetsPlaceholder": "Введите количество пакетов", "length": "Длина", "lengthPlaceholder": "Введите длину", "interval": "Интервал", "intervalPlaceholder": "Введите интервал", "allFieldsRequired": "Если одно поле заполнено, все поля должны быть заполнены"}, "hostsDialog.fragment.info": "length,interval,packet (e.g. 10-100,100-200,tlshello)", "hostsDialog.fragment.info.attention": "Attention: currently, this feature only supported in streisand >= 1.6.12 and v2rayNG >= 1.8.16 (custom config)", "hostsDialog.fragment.info.examples": "Examples:", "hostsDialog.noise": {"title": "<PERSON>ум", "addNoise": "Добавить шум", "removeNoise": "Удалить шум", "packet": "Пак<PERSON>т", "packetPlaceholder": "Введите значение пакета (например: rand:10-20)", "delay": "Задержка", "delayPlaceholder": "Введите задержку", "rand": "Случайный"}, "hostsDialog.noise.info": "packet,delay (e.g. rand:10-20,100-200)", "hostsDialog.noise.info.attention": "Attention: currently, this feature only supported in streisand >= 1.6.32 and v2rayNG >= 1.8.39 (custom config)", "hostsDialog.noise.info.examples": "Examples:", "hostsDialog.host": "Host", "hostsDialog.host.info": "По умолчанию, если в конфигурации XRAY задан запрашиваемый хост, то он и будет использоваться. Однако, если необходимо, вы можете установить здесь пользовательский запрашиваемый хост.", "hostsDialog.host.multiHost": "Чтобы установить несколько адресов, разделяйте их с помощью <badge>,</badge>. Каждый раз будет выбран случайный адрес.", "hostsDialog.host.wildcard": "Используйте <badge>*</badge>, чтобы сгенерировать случайную строку (работает для wildcard доменов)", "hostsDialog.jalaliExpireDate": "Дата истечения срока по солнечному календарю", "hostsDialog.loading": "загрузка...", "hostsDialog.muxEnable": "Давать возможность MUX", "hostsDialog.path": "Path", "hostsDialog.path.info": "Set a path for host users, useful behind a reverse proxy.", "hostsDialog.port": "Port", "hostsDialog.port.info": "По умолчанию хост использует порт, значение которого обьявленно в inbound. Вы можете установить своё значение порта, если с него трафик перенаправляется на другой порт. Например, сервер может перенаправлять трафик с порта 443 на порт по умолчанию Вашего inbound", "hostsDialog.proxyMethod": "Метод транспорта прокси (например, ws)", "hostsDialog.proxyOutbound": "Исходящий прокси json", "hostsDialog.proxyOutbound.info": "Дополнительный исходящий трафик (только в пользовательской конфигурации v2ray)", "hostsDialog.proxyProtocol": "Протокол прокси (например, VMess)", "hostsDialog.randomUserAgent": "Использовать случайный User-Agent", "hostsDialog.remainingData": "Оставшийся трафик пользователя", "hostsDialog.remainingDays": "Оставшиеся дни пользователя", "hostsDialog.remainingTime": "Оставшееся время пользователя", "hostsDialog.savedSuccess": "Хосты успешно сохранены", "hostsDialog.security": "Security Layer", "hostsDialog.security.info": "Если промежуточный сервер этого хоста использует другой security layer, отличный от security layer Вашего inbound по умолчанию, Вы можете установить его здесь", "hostsDialog.sni": "SNI", "hostsDialog.sni.info": "По умолчанию хост использует SNI, значение которого обьявленно в inbound. Вы можете установить своё значение SNI, если этот хост содержит другие SNI. Например, сервер может принимать трафик с другим SSL-сертификатом, выполнять SSL-терминацию и перенаправлять его на ваш inbound.", "hostsDialog.sniPlaceholder": "SNI (например, example.com)", "hostsDialog.sockopt": "<PERSON><PERSON><PERSON><PERSON>", "hostsDialog.statusEmoji": "Статус пользователя в виде смайлика (✅,⌛️,🪫,❌,🔌)", "hostsDialog.statusText": "Статус пользователя", "hostsDialog.title": "Используя эту настройку, Вы можете настроить свои inbound.", "hostsDialog.username": "Имя пользователя", "inbound": "inbound", "itemsPerPage": "Элементов на страницу", "login": "Вход", "login.fieldRequired": "Это поле обязательно для заполнения", "login.loginYourAccount": "Войдите в свой аккаунт", "login.welcomeBack": "Пожалуйста, введите свои данные", "memoryUsage": "Память", "next": "Вперед", "monitorServers": "Следите за своими серверами и пользователями", "manageServers": "Управляйте данными вашего сервера", "nodes.addHostForEveryInbound": "Добавить этот узел, как новый хост для каждого inbound", "nodes.addNewMarzbanNode": "Добавить новый узел Marzban", "nodes.prompt": "Добавьте и настройте данные узла", "nodes.addNode": "Добавить узел", "nodes.addNodeSuccess": "Узел {{name}} успешно добавлен", "nodes.apply": "Настройки узла успешно обновлены", "nodes.certificate": "Сертификат", "nodes.certificateCopied": "Сертификат скопирован в буфер обмена", "nodes.certificateDownloaded": "Сертификат успешно загружен", "nodes.certificateDescription": "Для настройки узла необходимо настроить этот сертификат для установления безопасного соединения между основным сервером и узлом", "nodes.connection-hint": "Для настройки узла <PERSON>, необходимо установить на нём данный сертификат, для инициализации безопасного соединения между главным сервером и узлом", "nodes.download-certificate": "Скачать сертификат", "nodes.editNode": "Редактировать узел", "nodes.hide-certificate": "Скрыть сертификат", "nodes.nodeAPIPort": "API порт", "nodes.nodeAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodes.nodeName": "Имя", "nodes.nodePort": "Порт", "nodes.reconnect": "Переподключиться", "nodes.reconnecting": "Переподключение...", "nodes.show-certificate": "Показать сертификат", "nodes.title": "Используя Marzban-Node, Вы можете масштабировать инфраструктуру, добавляя узлы на разных серверах.", "nodes.usageCoefficient": "Коэффициент использования", "on_hold": "On Hold", "duplicate": "Дублировать", "createAndManageTemplates": "Создавать и управлять шаблонами", "password": "Пароль", "previous": "Назад", "qrcodeDialog.sublink": "Ссылка на подписку", "reset": "Сбросить", "resetAllUsage": "Сбросить расход трафика", "resetAllUsage.error": "Сброс расхода трафика не удался, пожалуйста, попробуйте ещё раз.", "resetAllUsage.prompt": "Это действие полностью очищает весь расход трафика пользователей. Вы уверены? ЭТО ДЕЙСТВИЕ НЕОБРАТИМО!", "resetAllUsage.success": "Расход трафика успешно сброшен.", "resetAllUsage.title": "Сбросить расход трафика для всех пользователей", "resetUserUsage.error": "Сброс расхода не удался, пожалуйста, попробуйте ещё раз.", "resetUserUsage.prompt": "Вы уверены, что хотите сбросить расход трафика для пользователя <b>{{username}}</b>?", "resetUserUsage.success": "Расход трафика пользователя {{username}} успешно сброшен.", "resetUserUsage.title": "Сбросить расход трафика пользователя", "revoke": "Отозвать", "revokeUserSub.error": "Отзыв подписки не удался, пожалуйста, попробуйте ещё раз.", "revokeUserSub.prompt": "Вы уверены, что хотите отозвать подписку для пользователя «{{username}}»?", "revokeUserSub.success": "Подписка пользователя {{username}} успешно отозвана.", "revokeUserSub.title": "Отозвать подписку пользователя", "search": "Поиск", "status.active": "Active", "status.enable": "Enable", "status.disabled": "Disable", "status.expired": "Expired", "status.limited": "Limited", "status.on_hold": "On Hold", "nodeModal.status.error": "Ошибка", "nodeModal.status.disabled": "инвалидный", "nodeModal.status.connecting": "соединяющий", "nodeModal.status.connected": "Подключен", "userDialog.absolute": "Абсолютно", "userDialog.custom": "Пользовательский", "userDialog.dataLimit": "Лимит трафика", "userDialog.days": "<PERSON><PERSON>и", "userDialog.editUser": "Редактировать", "userDialog.editUserTitle": "Редактировать пользователя", "userDialog.endDate": "Дата окончания", "userDialog.expiryDate": "Дата истечения срока", "userDialog.timeOutDate": "Дата истечения срока действия", "userDialog.generatedByDefault": "по умолчанию", "userDialog.hours": "<PERSON>а<PERSON>ы", "userDialog.method": "Метод", "userDialog.months": "Месяцы", "userDialog.note": "Примечание", "userDialog.onHold": "В режиме ожидания", "userDialog.onHoldExpireDuration": "Продолжительность срока действия", "userDialog.onHoldExpireDurationPlaceholder": "на<PERSON>ри<PERSON><PERSON><PERSON>, 7", "userDialog.optional": "необязательно", "userDialog.periodicUsageReset": "Период сброса трафика", "userDialog.protocols": "Протоколы", "userDialog.relative": "Относительно", "userDialog.resetStrategyAnnually": "Ежегодно", "userDialog.resetStrategyDaily": "Ежедневно", "userDialog.resetStrategyMonthly": "Ежемесячно", "userDialog.resetStrategyNo": "Нет", "userDialog.resetStrategyWeekly": "Еженедельно", "userDialog.resetUsage": "Сбросить трафик", "userDialog.revokeSubscription": "Отозвать подписку", "userDialog.subscriptionInfo": "Информация о подписке", "userDialog.subscriptionUpdated": "Последнее обновление", "userDialog.lastClient": "Последний клиент", "userDialog.subscriptionNotAccessed": "Еще не использовалась", "userDialog.unknownClient": "Неизвестный клиент", "userDialog.selectOneProtocol": "Пожалуйста, выберите хотя бы один протокол", "userDialog.shadowsocksDesc": "Быстрый и безопасный, но не такой эффективный, как другие", "userDialog.startDate": "Дата начала", "userDialog.total": "Всего: ", "userDialog.trojanDesc": "Легковесный, безопасный и быстрый", "userDialog.usage": "Потребление", "userDialog.userAlreadyExists": "Пользователь уже существует", "userDialog.userCreated": "Пользователь {{username}} создан.", "userDialog.userEdited": "Пользователь {{username}} изменён.", "userDialog.vlessDesc": "Легковесный, быстрый и безопасный", "userDialog.vmessDesc": "Быстрый и безопасный", "userDialog.warningNoProtocol": "Пожалуйста, выберите хотя бы один протокол", "userDialog.weeks": "Недели", "userDialog.expireDate": "Дата истечения срока", "username": "Имя пользователя", "users": "Пользователи", "usersTable.copied": "Скопировано", "usersTable.copyConfigs": "Скопировать конфигурации", "usersTable.copyLink": "Скопировать ссылку на подписку", "usersTable.dataUsage": "Расход трафика", "usersTable.noUser": "В системе нет созданных пользователей", "usersTable.noUserMatched": "Похоже, нет пользователя, соответствующего вашему запросу", "usersTable.status": "Статус", "usersTable.total": "Всего", "statistics.system": "Система", "statistics.totalTraffic": "Общий трафик", "statistics.ramUsage": "Использование RAM", "statistics.cpuUsage": "Использование CPU", "statistics.cores": "ядер", "hostsDialog": {"addHost": "Добави<PERSON>ь хост", "selectInbound": "Выберите входящий", "fingerprint": "Отпечаток", "selectStatus": "Выберите статус", "remarkRequired": "Примечание обязательно", "addressRequired": "Адрес обязателен", "portRequired": "Порт обязателен", "status": {"label": "Статус", "active": "Активный", "disabled": "Отключен", "limited": "Огра<PERSON><PERSON><PERSON><PERSON>н", "expired": "Истек", "onHold": "В ожидании"}, "loading": "загрузка...", "port": "Порт", "sni": "SNI", "host": "Хо<PERSON>т", "path": "Путь", "networkSettings": "Сетевые настройки", "transportSettings": "Настройки транспорта", "transportType": "Тип транспорта", "selectTransport": "Выберите тип транспорта", "serviceName": "Имя сервиса", "enterServiceName": "Введите имя сервиса gRPC", "seed": "Seed", "enterSeed": "Введите seed для KCP", "enterPath": "Введите путь (пример: /xray)", "enterHost": "Введите хост (пример: example.com)", "header": "Заголовок", "selectHeader": "Выберите тип заголовка", "multiMode": "Мульти режим", "securitySettings": "Настройки безопасности", "camouflagSettings": "Настройки маскировки", "security": "Security Layer", "alpn": "ALPN", "allowInsecure": "Разрешить небезопасное соединение", "muxEnable": "Включить MUX", "randomUserAgent": "Использовать случайный User-Agent", "useSniAsHost": "Использовать SNI как хост", "inboundDefault": "По умолчанию", "earlyData": "Ранние данные", "httpHeaders": "HTTP заголовки", "headersName": "Имя заголовка", "headersValue": "Значение заголовка", "addHeader": "Добавить заголовок", "removeHeader": "Удалить заголовок", "createSuccess": "Хост «{{name}}» успешно создан", "createFailed": "Не удалось создать хост «{{name}}»", "editSuccess": "Хост «{{name}}» успешно обновлен", "editFailed": "Не удалось обновить хост «{{name}}»", "noStatus": "Статус не выбран", "clearAllStatuses": "Очистить все статусы", "transportSettingsAccordion": "Настройки транспорта", "xhttp": {"mode": "Режим", "noGrpcHeader": "Без заголовка gRPC", "xPaddingBytes": "X-Padding байты", "scMaxEachPostBytes": "Максимум байт на пост", "scMinPostsIntervalMs": "Минимальный интервал постов (мс)", "scMaxBufferedPosts": "Максимум буферизованных постов", "scStreamUpServerSecs": "Сервер стриминга (сек)", "xmux": "Настройки XMux", "maxConcurrency": "Максимум параллельных", "maxConnections": "Максимум соединений", "cMaxReuseTimes": "Максимум повторных использований", "cMaxLifetime": "Максимальный срок службы", "hMaxRequestTimes": "Максимум запросов", "hKeepAlivePeriod": "Период поддержания соединения", "downloadSettings": "Настройки загрузки", "downloadSettingsInfo": "Выберите хост для настроек загрузки", "selectDownloadSettings": "Выберите хост настроек загрузки"}, "grpc": {"multiMode": "Мульти режим", "idleTimeout": "Таймаут бездействия", "healthCheckTimeout": "Таймаут проверки работоспособности", "permitWithoutStream": "Разрешить без потока", "initialWindowsSize": "Начальный размер окна"}, "kcp": {"header": "Заголовок", "mtu": "MTU", "tti": "TTI", "uplinkCapacity": "Пропускная способность входящего", "downlinkCapacity": "Пропускная способность исходящего", "congestion": "Перегрузка", "readBufferSize": "Размер буфера чтения", "writeBufferSize": "Размер буфера записи"}, "tcp": {"title": "Настройки TCP", "header": "Тип заголовка", "request": {"title": "Настройки запроса", "version": "Версия HTTP", "method": "Метод HTTP", "headers": "Заголовки запроса"}, "response": {"title": "Настройки ответа", "version": "Версия HTTP", "status": "Код состояния", "reason": "Причина состояния", "headers": "Заголовки ответа"}, "headerName": "Имя заголовка", "headerValue": "Значение заголовка", "addHeader": "Добавить заголовок", "removeHeader": "Удалить заголовок", "requestHeaders": "Заголовки запроса", "responseHeaders": "Заголовки ответа"}, "websocket": {"heartbeatPeriod": "Период сердцебиения"}, "muxSettings": "Настройки Mux", "enableMux": "Включи<PERSON>ь Mux", "xraySettings": "Настройки Xray", "singBoxSettings": "Настройки Sing-box", "clashSettings": "Настройки Clash", "addXraySettings": "Добавить настройки Xray", "addSingBoxSetting": "Добавить настройки Sing-box", "addClashSettings": "Добавить настройки Clash", "protocol": "Протокол", "selectProtocol": "Выберите протокол", "maxConnections": "Макс. соединений", "maxStreams": "Макс. потоков", "minStreams": "<PERSON>и<PERSON><PERSON> по<PERSON>о<PERSON><PERSON>", "padding": "Включить отступы", "enabled": "Включено", "concurrency": "Параллельность", "xudpConcurrency": "Параллельность XUDP", "xudpProxy443": "XUDP Прокси 443", "selectXudpProxy443": "Выберите XUDP Прокси 443", "statistic": "Включить статистику", "onlyTcp": "Только TCP", "brutal": {"enable": "Включить Brutal", "title": "Настройки Brutal", "upMbps": "Скорость загрузки (Мбит/с)", "downMbps": "Скорость скачивания (Мбит/с)"}, "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "variables": {"title": "Используйте эти переменные, чтобы сделать их динамическими", "server_ip": "IP-адрес текущего сервера", "server_ipv6": "IPv6 текущего сервера", "username": "Имя пользователя", "data_usage": "Текущий объем использованных данных", "data_left": "Оставшийся объем данных", "data_limit": "Лимит использования данных", "days_left": "Оставшиеся дни", "expire_date": "Дата истечения срока", "jalali_expire_date": "Дата истечения срока по солнечному календарю", "time_left": "Оставшееся время", "status_text": "Статус пользователя", "status_emoji": "Статус пользователя в виде эмодзи (✅,⌛️,🪫,❌,🔌)", "protocol": "Протокол прокси (например, VMess)", "transport": "Метод транспорта прокси (например, ws)"}, "httpVersions": {"1.0": "HTTP/1.0", "1.1": "HTTP/1.1", "2.0": "HTTP/2.0", "3.0": "HTTP/3.0"}, "httpMethods": {"GET": "GET", "POST": "POST", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD", "OPTIONS": "OPTIONS", "PATCH": "PATCH", "TRACE": "TRACE", "CONNECT": "CONNECT"}, "httpReasons": {"100": "Продолжить", "101": "Переключение протоколов", "200": "OK", "201": "Создано", "202": "Принято", "203": "Неавторитетная информация", "204": "Нет содержимого", "205": "Сбросить содержимое", "206": "Частичное содержимое", "300": "Множественный выбор", "301": "Перемещено навсегда", "302": "Найдено", "303": "Смотреть другое", "304": "Не изменялось", "305": "Использовать прокси", "307": "Временное перенаправление", "308": "Постоянное перенаправление", "400": "Неверный запрос", "401": "Не авторизован", "402": "Необходима оплата", "403": "Запрещено", "404": "Не найдено", "405": "Метод не разрешен", "406": "Неприемлемо", "407": "Требуется аутентификация прокси", "408": "Тайм-аут запроса", "409": "Конфликт", "410": "Удалено", "411": "Требуется длина", "412": "Предварительное условие не выполнено", "413": "Полезная нагрузка слишком велика", "414": "URI слишком длинный", "415": "Неподдерживаемый тип носителя", "416": "Диапазон не выполним", "417": "Ожидание не выполнено", "418": "Я чайник", "421": "Неправильно направленный запрос", "422": "Необрабатываемая сущность", "423": "Заблокировано", "424": "Невыполненная зависимость", "425": "Слишком рано", "426": "Требуется обновление", "428": "Требуется предварительное условие", "429": "Слишком много запросов", "431": "Поля заголовка запроса слишком большие", "451": "Недоступно по юридическим причинам", "500": "Внутренняя ошибка сервера", "501": "Не реализовано", "502": "Ошибочный шлюз", "503": "Служба недоступна", "504": "Тайм-аут шлюза", "505": "Версия HTTP не поддерживается"}, "selectReason": "Выбрать причину", "nextPlanTitle": "Следующий план", "nextPlanTemplateId": "ID шаблона", "nextPlanDataLimit": "<PERSON>им<PERSON><PERSON> данных (ГБ)", "nextPlanExpire": "Срок действия (дней)", "nextPlanAddRemainingTraffic": "Добавить оставшийся трафик"}, "enable": "Включить", "host": {"enableSuccess": "Хост «{{name}}» успешно включен", "enableFailed": "Не удалось включить хост «{{name}}»", "disableSuccess": "Хост «{{name}}» успешно отключен", "disableFailed": "Не удалось отключить хост «{{name}}»", "duplicateSuccess": "<PERSON>ост «{{name}}» успешно дублирован", "duplicateFailed": "Не удалось дублировать хост «{{name}}»", "xudp_proxy_443": "XUDP Прокси 443", "reject": "Отклонить", "allow": "Разрешить", "skip": "Пропустить"}, "group": {"createSuccess": "Группа «{{name}}» успешно создана", "createFailed": "Не удалось создать группу «{{name}}»", "editSuccess": "Группа «{{name}}» успешно обновлена", "editFailed": "Не удалось обновить группу «{{name}}»", "deleteSuccess": "Группа «{{name}}» успешно удалена", "deleteFailed": "Не удалось удалить группу «{{name}}»", "deleteConfirm": "Вы уверены, что хотите удалить группу «{{name}}»?", "enableSuccess": "Группа «{{name}}» успешно включена", "enableFailed": "Не удалось включить группу «{{name}}»", "disableSuccess": "Группа «{{name}}» успешно отключена", "disableFailed": "Не удалось отключить группу «{{name}}»"}, "name": "Имя", "nodeModal": {"title": "Добавить узел", "description": "Добавить новый узел в сеть", "name": "Название узла", "namePlaceholder": "Введите название узла", "address": "Адрес узла", "addressPlaceholder": "Введите адрес узла", "port": "Порт узла", "portPlaceholder": "Введите порт узла", "usageRatio": "Коэффициент использования", "usageRatioPlaceholder": "Введите коэффициент использования", "maxLogs": "Максимум логов", "maxLogsPlaceholder": "Введите максимум логов", "connectionType": "Тип подключения", "keepAlive": "Поддержание соединения", "keepAliveDescription": "Установите интервал поддержания соединения", "days": "<PERSON><PERSON>и", "minutes": "Минуты", "seconds": "Секунды", "certificate": "Сертификат", "certificatePlaceholder": "Введите сертификат", "status": "Статус узла", "statusCheckSuccess": "Узел успешно подключен", "statusCheckFailed": "Не удалось подключиться к узлу", "errorDetails": "Детали ошибки", "connectionError": "Ошибка подключения", "retryConnection": "Повторить подключение", "configurationError": "Ошибка конфигурации", "validateConfig": "Проверить конфигурацию", "fillRequiredFields": "Пожалуйста, заполните все обязательные поля перед проверкой статуса", "apiKey": "<PERSON> ключ", "apiKeyPlaceholder": "Введите API ключ узла", "generateUUID": "Сгенерировать UUID", "coreConfig": "Конфигурация ядра", "selectCoreConfig": "Выберите конфигурацию ядра", "hours": "<PERSON>а<PERSON>ы", "hideDetails": "Скрыть детали", "showDetails": "Показать детали", "statusCheck": "Проверить статус", "statusChecking": "Проверка...", "reconnect": "Переподключить", "reconnecting": "Переподключение...", "sync": "Синхронизировать", "syncing": "Синхронизация...", "syncSuccess": "Узел успешно синхронизирован", "syncFailed": "Не удалось синхронизировать узел", "gatherLogs": "Сбор логов", "gatherLogsDescription": "Включить сбор логов с этого узла для мониторинга и отладки", "statusMessages": {"checkUnavailableForNew": "Проверка статуса недоступна для новых узлов. Сначала создайте узел, чтобы проверить его статус."}, "onlineStats": {"button": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> статистика", "title": "Статистика онлайн пользователей", "ipListTitle": "IP адреса для {{username}}", "nodeInfo": "Узел: {{nodeName}}", "searchPlaceholder": "Введите имя пользователя...", "enterUsername": "Пожалуйста, введите имя пользователя", "refreshed": "Данные успешно обновлены", "refreshFailed": "Не удалось обновить данные", "backToStats": "Назад к статистике", "ipAddresses": "IP адреса", "errorLoading": "Ошибка загрузки статистики пользователя", "userNotOnline": "Пользователь не в сети", "searchUser": "Найдите пользователя для просмотра его онлайн статистики", "autoRefresh": "Автообновление каждые 5 секунд", "connections": "соединения", "protocols": "протоколы", "viewIPs": "Просмотр IP", "userNotFound": "Пользователь '{{username}}' не найден или не в сети", "errorLoadingIPs": "Ошибка загрузки IP адресов пользователя"}}, "theme": {"title": "Тема", "description": "Настройте тему вашего приложения", "light": "Светлая", "dark": "Тёмная", "system": "Системная", "selectTheme": "Выбрать тему", "themeSaved": "Тема успешно сохранена", "themeChanged": "Тема успешно изменена", "visitThemePage": "Посетите страницу темы для дальнейшей настройки", "radiusSaved": "Радиус границ успешно обновлён", "mode": "Режим", "color": "Цвет", "radius": "Радиус границ", "zinc": "<PERSON><PERSON>н<PERSON>", "rose": "Розовый", "blue": "Синий", "green": "Зелёный", "violet": "Фиолетовый", "orange": "Оранжевый", "yellow": "Жёлтый", "default": "По умолчанию", "red": "Красный", "modeDescription": "Выберите, как должен выглядеть интерфейс", "colorDescription": "Выберите предпочитаемую цветовую схему", "radiusDescription": "Настройте скругление элементов интерфейса", "lightDescription": "Яркая и чистая", "darkDescription": "Приятная для глаз", "systemDescription": "Соответствует устройству", "preview": "Предпросмотр", "previewDescription": "Посмотрите, как выглядят настройки темы в реальном времени", "dashboardPreview": "Предпросмотр панели", "dashboardDescription": "Мониторинг производительности системы и управление учетными записями пользователей", "currentTheme": "Текущая тема", "sampleInput": "Пример ввода", "primaryButton": "Основная кнопка", "resetToDefaults": "Сбросить к настройкам по умолчанию", "resetDescription": "Восстановить все настройки темы к значениям по умолчанию", "resetting": "Сброс...", "reset": "Сбросить", "resetSuccess": "Тема сброшена к настройкам по умолчанию", "resetFailed": "Не удалось сбросить настройки темы", "radiusNone": "Без", "radiusSmall": "Малый", "radiusMedium": "Средний", "radiusLarge": "Больш<PERSON>й"}, "coreConfigModal": {"addConfig": "Добавить конфигурацию ядра", "createNewConfig": "Создать новую конфигурацию ядра", "editCore": "Редактировать конфигурацию ядра", "invalidJson": "Недопустимая конфигурация JSON", "createSuccess": "Конфигурация ядра «{{name}}» успешно создана", "createFailed": "Не удалось создать конфигурацию ядра «{{name}}»", "editSuccess": "Конфигурация ядра «{{name}}» успешно обновлена", "editFailed": "Не удалось обновить конфигурацию ядра «{{name}}»", "keyPairGenerated": "Пара ключей успешно сгенерирована", "shortId": "Короткий ID", "shortIdGenerated": "Короткий ID успешно сгенерирован", "shortIdCopied": "Короткий ID скопирован в буфер обмена", "publicKeyCopied": "Публичный ключ скопирован в буфер обмена", "privateKeyCopied": "Приватный ключ скопирован в буфер обмена", "jsonConfig": "Конфигурация JSON", "editJson": "Редактировать конфигурацию JSON", "name": "Имя", "namePlaceholder": "Введите имя конфигурации", "fallback": "Резервный вариант", "selectFallback": "Выберите резервный вариант", "excludedInbound": "Исключенные входящие", "selectInbound": "Выберите входящий", "inbound": "Входящий", "generateKeyPair": "Сгенерировать пару ключей", "generateShortId": "Сгенерировать короткий ID", "publicKey": "Публичный ключ", "privateKey": "Приват<PERSON><PERSON>й ключ", "clearAllFallbacks": "Очистить все fallback", "clearAllExcluded": "Очистить все исключённые входящие", "copyPublicKey": "Копировать публичный ключ", "copyPrivateKey": "Копировать приватный ключ", "copyShortId": "Копировать короткий ID", "restartNodes": "Перезапустить узлы", "generateShadowsocksPassword": "Сгенерировать пароль Shadowsocks", "generatingShadowsocksPassword": "Генерация пароля...", "shadowsocksPassword": "Пароль Shadowsocks", "shadowsocksPasswordCopied": "Пароль скопирован в буфер обмена", "copyShadowsocksPassword": "Копировать пароль", "selectEncryptionMethod": "Выберите метод шифрования", "regeneratePassword": "Сгенерировать пароль заново"}, "settings.cores.title": "Ядра", "settings.cores.description": "Управление вашими ядрами", "settings.cores.addCore": "Добавить ядро", "settings.cores.noCores": "Нет доступных ядер. Добавьте одно, чтобы начать.", "settings.cores.duplicateSuccess": "Конфигурация ядра «{{name}}» успешно скопирована", "settings.cores.duplicateFailed": "Не удалось скопировать конфигурацию ядра «{{name}}»", "settings.cores.deleteSuccess": "Ядро успешно удалено", "settings.cores.deleteFailed": "Не удалось удалить ядро", "settings.cores.delete": "Удалить ядро", "settings.cores.coreNotFound": "Ядро не найдено", "createdAt": "Дата создания", "toggle": "Переключить статус", "close": "Закрыть", "copy": "Копировать", "userDialog": {"deleteSuccess": "Пользователь «{{name}}» был успешно удалён.", "resetUsageSuccess": "Использование пользователя «{{name}}» было сброшено.", "revokeSubSuccess": "Подписка пользователя «{{name}}» была отозвана.", "activeNextPlanSuccess": "Следующий план для пользователя «{{name}}» был активирован.", "activeNextPlanError": "Не удалось активировать следующий план для пользователя «{{name}}».", "deleteConfirmTitle": "Удалить пользователя", "deleteConfirm": "Вы уверены, что хотите удалить пользователя «{{name}}»?", "selectTemplate": "Выберите шаблон", "selectTemplatePlaceholder": "Выберите шаблон", "proxySettingsAccordion": "Настройки прокси", "proxySettings.vmess": "VMess", "proxySettings.vless": "VLESS", "proxySettings.trojan": "Trojan", "proxySettings.shadowsocks": "Shadowsocks", "proxySettings.id": "ID", "proxySettings.password": "Пароль", "proxySettings.method": "Метод", "proxySettings.flow": "Поток (Flow)", "proxySettings.desc": "Настройте параметры протокола для этого пользователя.", "selectNode": "Выберите узел", "allNodes": "Все узлы", "activeNextPlan": "Активировать следующий план", "unknownClient": "Неизвестный клиент", "selectedGroups": "{{count}} групп выбрано", "editError": "Не удалось обновить пользователя «{{name}}»", "createError": "Не удалось создать пользователя «{{name}}»", "selectStatus": "Выберите статус", "selectedTemplates": "Выбрано шаблонов: {{count}}"}, "sidebar.expand": "Развернуть меню", "sidebar.collapse": "Свернуть меню", "usersTable.deleteUserTitle": "Удалить пользователя", "usersTable.deleteUserPrompt": "Вы уверены, что хотите удалить пользователя «{{name}}»? Это действие необратимо.", "usersTable.deleteSuccess": "Пользователь «{{name}}» был успешно удалён.", "usersTable.deleteFailed": "Не удалось удалить пользователя «{{name}}». {{error}}", "usersTable.delete": "Удалить", "usersTable.cancel": "Отмена", "statistics.trafficUsage": "Использование трафика", "statistics.trafficUsageDescription": "Общее использование трафика на всех серверах", "statistics.usageDuringPeriod": "Использование за период", "statistics.users": "Пользователи", "statistics.onlineUsers": "Он<PERSON><PERSON><PERSON>н пользователи", "statistics.limitedUsers": "Ограниченные пользователи", "statistics.activeUsers": "Активные пользователи", "statistics.disabledUsers": "Отключенные пользователи", "statistics.expiredUsers": "Истекшие пользователи", "statistics.onHoldUsers": "Пользователи на удержании", "statistics.allUsers": "Все пользователи", "statistics.serverTraffic": "Трафик сервера", "statistics.nodeTrafficDistribution": "Распределение трафика узлов", "statistics.noNodesAvailable": "Нет доступных узлов", "statistics.noDataAvailable": "Нет данных", "statistics.noDataDescription": "Выберите временной диапазон для просмотра статистики и трендов использования трафика.", "statistics.noNodesDescription": "Нет подключенных узлов для отображения распределения трафика.", "statistics.loadingDescription": "Получение последних статистических данных...", "statistics.waitingForData": "Ожидание данных", "statistics.waitingForDataDescription": "Статистика в реальном времени появится здесь после начала передачи данных.", "statistics.selectTimeRange": "Выберите временной диапазон", "statistics.selectTimeRangeDescription": "Выберите диапазон дат выше, чтобы просмотреть статистику использования трафика.", "statistics.noDataInRange": "Нет данных в выбранном диапазоне", "statistics.noDataInRangeDescription": "Данные о трафике для выбранного периода времени не найдены. Попробуйте выбрать другой диапазон дат.", "statistics.showTimeRange": "Временной диапазон", "statistics.hideTimeRange": "Скрыть диапазон", "statistics.selectNodeToView": "Выберите узел для просмотра подробной статистики", "statistics.userStatisticsDescription": "Обзор состояния и активности учетных записей пользователей", "errors.connectionFailed": "Ошибка подключения. Проверьте сеть и попробуйте снова.", "errors.statisticsLoadFailed": "Не удалось загрузить статистику", "usersTable.resetUsageTitle": "Сбросить использование пользователя", "usersTable.resetUsagePrompt": "Вы уверены, что хотите сбросить использование для «{{name}}»?", "usersTable.resetUsageSubmit": "Сбросить использование", "usersTable.resetUsageSuccess": "Использование пользователя «{{name}}» успешно сброшено", "usersTable.resetUsageFailed": "Не удалось сбросить использование пользователя «{{name}}»", "time": {"expires": "истекает", "expired": "истек", "year": "год", "years": "года", "month": "мес<PERSON><PERSON>", "months": "месяца", "day": "день", "days": "дня", "hour": "час", "hours": "часа", "min": "мин", "mins": "мин", "ago": "назад"}, "notConnectedYet": "Еще не подключен", "node.xrayVersion": "Версия ядра", "node.coreVersion": "Версия узла", "calendar": {"selectRange": "Выбрать диапазон", "startDate": "Дата начала", "endDate": "Дата окончания"}, "usersTable": {"usageChart": "График использования", "noUsageData": "Нет данных за этот период.", "tryDifferentRange": "Попробуйте другой диапазон времени.", "trendingUp": "Рост на", "trendingDown": "Снижение на", "usageSummary": "Показано общее использование за выбранный период.", "loading": "загрузка...", "noResults": "Результаты не найдены", "emptyState": {"noUsers": {"title": "Пользователей пока нет", "description": "Начните с создания первой учетной записи пользователя", "createFirstUser": "Создать первого пользователя"}}}, "setOwnerModal": {"title": "Назначить владельца", "currentOwner": "Текущий владелец:", "none": "Нет", "selectAdmin": "Выбрать нового владельца", "confirm": "Назначить владельца", "success": "Владелец пользователя {{username}} успешно изменён на {{admin}}.", "error": "Не удалось изменить владельца пользователя {{username}} на {{admin}}.", "loadError": "Не удалось загрузить список администраторов."}, "inboundTags": "Входящие теги", "searchInbounds": "Поиск входящих...", "noInboundsFound": "Входящие не найдены", "resilientNodeGroups": {"title": "Resilient Node Groups", "description": "Manage resilient node groups for improved reliability", "addGroup": "Add Group", "createGroup": "Create Resilient Node Group", "editGroup": "Edit Resilient Node Group", "name": "Name", "strategy": "Client Strategy", "nodes": "Nodes", "selectNodes": "Select nodes", "createSuccess": "Resilient node group created successfully", "updateSuccess": "Resilient node group updated successfully", "deleteSuccess": "Resilient node group deleted successfully", "operationFailed": "Failed to perform operation on resilient node group", "deleteFailed": "Failed to delete resilient node group", "strategies": {"urlTest": "URL Test", "fallback": "Fallback", "loadBalance": "Load <PERSON>", "clientDefault": "<PERSON><PERSON>", "none": "None"}, "clientDefault": "<PERSON><PERSON>"}, "hiddifyImport": {"title": "Import Users from Hiddify", "selectFile": "Select Hiddify Backup File", "fileSelected": "File selected: {{filename}}", "invalidFile": "Invalid File", "invalidFileDesc": "Please select a valid JSON file.", "unlimitedExpiration": "Set unlimited expiration for all users", "unlimitedExpirationDesc": "If enabled, all imported users will have unlimited expiration (expire = 0), ignoring package_days from Hiddify.", "smartUsernameParsing": "Enable smart username & note parsing", "smartUsernameParsingDesc": "If enabled, names like \"1234 John <PERSON>\" will be split: \"1234\" as username, \"<PERSON>\" as note. Otherwise, the full name will be used as username.", "protocolSelection": "Select Protocols", "protocolSelectionDesc": "Choose which protocols the imported users should have access to.", "noFileSelected": "No File Selected", "noProtocolsSelected": "No Protocols Selected", "importing": "Importing users...", "importUsers": "Import Users", "importComplete": "Import Complete", "importStats": "{{successful}} users imported successfully, {{failed}} failed.", "importSuccess": "Import Successful", "importSuccessDesc": "{{successful}} users imported successfully. {{failed}} users failed to import.", "importWarning": "Import Completed with Warnings", "importWarningDesc": "{{failed}} users failed to import. Check the details below.", "importError": "Import Failed", "importErrorDesc": "An error occurred during import. Please try again."}}
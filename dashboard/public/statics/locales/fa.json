{"marzban": "مـرزبان", "dashboard": "داشبورد", "master": "سرور اصلی", "dashboardDescription": "داشبورد مدیریت مـرزبان", "active": "فعال", "online": "آنلاین", "platform": "پلتفرم", "allStatuses": "همه وضعیت‌ها", "status": "وضعیت", "statistics": "آمار", "selectAll": "انتخاب همه", "modifying": "در حال ویرایش...", "removing": "در حال حذف...", "creating": "در حال ایجاد...", "hosts": "هاست‌ها", "groups": "گروه‌ها", "documentation": "مستندات", "discussionGroup": "گروه گفتگو", "github": "گیت‌هاب", "community": "جامعه", "supportUs": "پشتیبانی از ما", "manageHosts": "مدیریت و کنترل هاست‌ها.", "manageSettings": "مدیریت و کنترل تنظیمات.", "monitorUsers": "مدیریت کاربران", "alltime": "همه زمان", "settings": {"title": "تنظیمات", "notifications": {"title": "اعلان‌ها", "description": "پیکربندی تنظیمات اعلان‌های تلگرام و دیسکورد", "loadError": "بارگیری تنظیمات اعلان‌ها ناموفق بود", "saveSuccess": "تنظیمات اعلان‌ها با موفقیت ذخیره شد", "saveFailed": "ذخیره تنظیمات اعلان‌ها ناموفق بود", "cancelSuccess": "تغییرات لغو شد و تنظیمات اصلی بازیابی شد", "filterTitle": "فیلتر اعلان‌ها", "filterDescription": "رویدادهایی را انتخاب کنید که باید اعلان‌هایی را برای اطلاع‌رسانی از فعالیت‌های مهم سیستم ایجاد کنند", "types": {"admin": "مدی<PERSON>ان", "core": "هسته", "group": "گروه‌ها", "host": "هاست‌ها", "login": "ورود", "node": "گره‌ها", "user": "کاربران", "userTemplate": "قالب‌ها", "daysLeft": "انقضا", "percentageReached": "مصرف"}, "telegram": {"title": "تلگرام", "description": "پیکربندی اعلان‌های ربات تلگرام برای دریافت هشدارهای بلادرنگ در چت یا کانال تلگرام شما", "apiToken": "توکن API ربات", "adminId": "شناسه چت مدیر", "channelId": "شناسه کانال", "topicId": "شناسه موضوع"}, "discord": {"title": "دیسکورد", "description": "تنظیم اعلان‌های وبهوک دیسکورد برای دریافت هشدارها مستقیماً در کانال‌های سرور دیسکورد شما", "webhookUrl": "آدرس وبهوک"}, "advanced": {"title": "تنظیمات پیشرفته", "description": "پیکربندی رفتار پیشرفته اعلان‌ها شامل سیاست‌های تلاش مجدد و تنظیمات پروکسی", "maxRetries": "حداکثر تلاش مجدد", "proxyUrl": "آدرس پروکسی"}}, "subscriptions": {"title": "اشتراک‌ها", "description": "پیکربندی URL های اشتراک، بازه‌های به‌روزرسانی و قوانین مخصوص کلاینت", "loadError": "بارگیری تنظیمات اشتراک ناموفق بود", "saveSuccess": "تنظیمات اشتراک با موفقیت ذخیره شد", "saveError": "ذخیره تنظیمات اشتراک ناموفق بود", "cancelSuccess": "تغییرات لغو شد و تنظیمات اصلی بازیابی شد", "general": {"title": "تنظیمات عمومی", "description": "گزینه‌های پیکربندی پایه اشتراک", "urlPrefix": "پیشوند آدرس", "urlPrefixPlaceholder": "sub", "urlPrefixDescription": "آدرس پایه برای لینک‌های اشتراک", "updateInterval": "فاصله به‌روزرسانی (ساعت)", "updateIntervalDescription": "هر چند وقت یکبار کلاینت‌ها باید به‌روزرسانی را بررسی کنند", "supportUrl": "آدرس پشتیبانی", "supportUrlPlaceholder": "https://support.example.com", "supportUrlDescription": "آدرس پشتیبانی و راهنمایی کاربران", "profileTitle": "عنوان پروفایل", "profileTitlePlaceholder": "سرویس VPN من", "profileTitleDescription": "نام نمایشی برای پروفایل اشتراک", "hostStatusFilter": "فیلتر وضعیت هاست", "hostStatusFilterDescription": "فیلتر کردن هاست‌ها بر اساس وضعیت آن‌ها"}, "rules": {"title": "قوانین اشتراک", "description": "پیکربندی قوانین اشتراک مخصوص کلاینت و فرمت‌ها", "addRule": "افزودن قانون", "removeRule": "<PERSON><PERSON><PERSON> قانون", "pattern": "الگو", "patternPlaceholder": "الگو را وارد کنید (مثال: الگوی user-agent)", "patternDescription": "الگو برای تطبیق با درخواست‌های کلاینت", "target": "فرمت هدف", "targetDescription": "فرمت پیکربندی برای ارائه این الگو", "noRules": "هیچ قانونی پیکربندی نشده است. قوانین را اضافه کنید تا رفتار اشتراک را برای کلاینت‌های مختلف سفارشی کنید."}, "formats": {"title": "فرمت‌های اشتراک دستی", "description": "فعال یا غیرفعال کردن فرمت‌های اشتراک خاص برای درخواست‌های دستی", "links": "لینک‌ها", "linksDescription": "لینک‌های اشتراک متن ساده", "linksBase64": "لینک‌ها (Base64)", "linksBase64Description": "لینک‌های اشتراک کدگذاری شده با Base64", "xray": "Xray", "xrayDescription": "فرمت پیکربندی <PERSON>ray", "singBox": "Sing-box", "singBoxDescription": "فرمت پیکربندی Sing-box", "clash": "Clash", "clashDescription": "فرمت پیکربندی Clash", "clashMeta": "<PERSON><PERSON>", "clashMetaDescription": "فرمت پیکربندی Clash <PERSON>a", "outline": "Outline", "outlineDescription": "فرمت پیکربندی Outline VPN"}, "configFormats": {"links": "لینک‌ها", "links_base64": "لینک‌ها (Base64)", "xray": "Xray", "sing_box": "Sing-box", "clash": "Clash", "clash_meta": "<PERSON><PERSON>", "outline": "Outline", "block": "بلاک"}}, "telegram": {"title": "تلگرام", "description": "پیکربندی ادغام ربات تلگرام و تنظیمات مرتبط برای سیستم شما", "loadError": "بارگیری تنظیمات تلگرام ناموفق بود. لطفاً دوباره تلاش کنید.", "saveSuccess": "تنظیمات تلگرام با موفقیت ذخیره شد", "saveFailed": "ذخیره تنظیمات تلگرام ناموفق بود", "cancelSuccess": "تغییرات لغو شد و تنظیمات اصلی تلگرام بازیابی شد", "general": {"title": "تنظیمات عمومی", "description": "پیکربندی اصلی ربات تلگرام و تنظیمات اتصال", "enable": "فعال‌سازی ربات تلگرام", "enableDescription": "فعال یا غیرفعال کردن عملکرد ربات تلگرام برای سیستم شما", "token": "توکن API ربات", "tokenPlaceholder": "توکن ربات تلگرام خود را وارد کنید", "tokenDescription": "توکن ربات دریافت شده از @BotFather در تلگرام", "webhookUrl": "آدرس Webhook", "webhookUrlPlaceholder": "https://your-domain.com/webhook", "webhookUrlDescription": "آدرسی که تلگرام به‌روزرسانی‌ها را ارسال می‌کند", "webhookSecret": "<PERSON><PERSON><PERSON> Webhook", "webhookSecretPlaceholder": "رمز webhook را وارد کنید", "webhookSecretDescription": "توکن مخفی برای امنیت webhook", "proxyUrl": "آدرس پروکسی", "proxyUrlPlaceholder": "socks5://proxy.example.com:1080", "proxyUrlDescription": "آدرس پروکسی برای اتصالات API تلگرام (اختیاری)"}, "advanced": {"title": "تنظیمات پیشرفته", "description": "ویژگی‌ها و پیکربندی‌های پیشرفته ربات تلگرام", "miniAppLogin": "فعال‌سازی ورود Mini App", "miniAppLoginDescription": "اجازه ورود کاربران از طریق اپلیکیشن‌های کوچک تلگرام"}}, "discord": {"title": "دیسکورد", "description": "تنظیم یکپارچگی ربات دیسکورد و تنظیمات مرتبط برای سیستم شما", "loadError": "بارگذاری تنظیمات دیسکورد ناموفق بود. لطفاً دوباره تلاش کنید.", "saveSuccess": "تنظیمات دیسکورد با موفقیت ذخیره شد", "saveFailed": "ذخیره تنظیمات دیسکورد ناموفق بود", "cancelSuccess": "تغییرات لغو شد و تنظیمات اصلی دیسکورد بازیابی شد", "general": {"title": "تنظیمات عمومی", "description": "پیکربندی اولیه ربات دیسکورد و تنظیمات اتصال", "enable": "فعال‌سازی ربات دیسکورد", "enableDescription": "فعال یا غیرفعال کردن عملکرد ربات دیسکورد برای سیستم شما", "token": "توکن ربات", "tokenPlaceholder": "توکن ربات دیسکورد خود را وارد کنید", "tokenDescription": "توکن ربات که از Discord Developer Portal دریافت شده", "proxyUrl": "آدرس پروکسی", "proxyUrlPlaceholder": "socks5://proxy.example.com:1080", "proxyUrlDescription": "آدرس پروکسی برای اتصالات Discord API (اختیاری)"}}, "webhook": {"title": "وبهوک", "description": "پیکربندی اعلان‌های وبهوک و تنظیمات endpoint برای سیستم شما", "loadError": "بارگذاری تنظیمات وبهوک ناموفق بود. لطفاً دوباره تلاش کنید.", "saveSuccess": "تنظیمات وبهوک با موفقیت ذخیره شد", "saveFailed": "ذخیره تنظیمات وبهوک ناموفق بود", "cancelSuccess": "تغییرات لغو شد و تنظیمات اصلی وبهوک بازیابی شد", "general": {"title": "تنظیمات عمومی", "description": "پیکربندی اولیه وبهوک و تنظیمات اتصال", "enable": "فعال‌سازی وبهوک‌ها", "enableDescription": "فعال یا غیرفعال کردن اعلان‌های وبهوک برای سیستم شما", "timeout": "زمان انقضا (ثان<PERSON>ه)", "timeoutDescription": "زمان انتظار درخواست برای فراخوانی وبهوک (۱-۳۰۰ ثانیه)", "recurrent": "تعداد تلاش مجدد", "recurrentDescription": "تعداد تلاش‌های مجدد برای وبهوک‌های ناموفق (۱-۲۴)", "proxyUrl": "آدرس پروکسی", "proxyUrlPlaceholder": "http://proxy.example.com:8080", "proxyUrlDescription": "آدرس پروکسی برای درخواست‌های وبهوک (اختیاری)"}, "webhooks": {"title": "نقاط پایانی وبهوک", "description": "پیکربندی آدرس‌های وبهوک و احراز هویت", "add": "افزودن وبهوک", "addFirst": "افزودن اولین وبهوک", "webhook": "وبهوک", "empty": "هیچ وبهوکی پیکربندی نشده. اولین نقطه پایانی وبهوک خود را اضافه کنید.", "name": "نام", "namePlaceholder": "وبهوک من", "url": "آدرس", "secret": "<PERSON><PERSON><PERSON><PERSON> مخفی", "secretPlaceholder": "کلید مخفی وبهوک را وارد کنید"}, "triggers": {"daysLeft": {"title": "اعلان‌های روزهای باقی‌مانده", "description": "اعلان زمانی که حساب‌های کاربری روزهای مشخصی باقی دارند", "empty": "هیچ محرکی تنظیم نشده"}, "usagePercent": {"title": "اعلان‌های درصد استفاده", "description": "اعلان زمانی که کاربران به درصدهای مشخص استفاده می‌رسند", "empty": "هیچ محرکی تنظیم نشده"}}}, "cleanup": {"title": "پاکسازی", "description": "مدیریت حساب‌های منقضی شده و بازنشانی مصرف داده‌ها برای نگهداری سیستم", "loadError": "بارگذاری تنظیمات پاکسازی ناموفق بود. لطفاً دوباره تلاش کنید.", "saveSuccess": "عملیات پاکسازی با موفقیت تکمیل شد", "saveFailed": "تکمیل عملیات پاکسازی ناموفق بود", "cancelSuccess": "عملیات پاکسازی لغو شد", "expiredUsers": {"title": "حذ<PERSON> حساب‌های منقضی شده", "description": "حذف حساب‌هایی که در بازه زمانی مشخصی منقضی شده‌اند", "expiredAfter": "منقضی شده بعد از", "expiredBefore": "منقضی شده قبل از", "expiredAfterPlaceholder": "تاریخ شروع را انتخاب کنید", "expiredBeforePlaceholder": "تاریخ پایان را انتخاب کنید", "dateRange": "بازه زمانی", "deleteExpired": "حذف منقضی‌ها", "deleting": "در حال حذف...", "deleteSuccess": "{{count}} حساب منقضی شده با موفقیت حذف شد", "deleteFailed": "حذف حساب‌های منقضی شده ناموفق بود", "confirmDelete": "حذ<PERSON> حساب‌های منقضی شده", "confirmDeleteMessage": "آیا مطمئن هستید که می‌خواهید همه حساب‌های منقضی شده در بازه زمانی انتخاب شده را حذف کنید؟ این عمل قابل بازگشت نیست.", "noDateSelected": "لطفاً حداقل یک تاریخ انتخاب کنید", "selectDateRange": "بازه زمانی حساب‌های منقضی شده برای حذف را انتخاب کنید (فقط تاریخ‌های گذشته قابل انتخاب هستند)"}, "resetUsage": {"title": "بازنشانی کل مصرف داده‌ها", "description": "بازنشانی مصرف داده‌ها برای همه حساب‌ها در سیستم", "resetAll": "بازنشانی همه مصرف‌ها", "resetting": "در حال بازنشانی...", "resetSuccess": "همه مصرف داده‌ها با موفقیت بازنشانی شد", "resetFailed": "بازنشانی مصرف داده‌ها ناموفق بود", "confirmReset": "بازنشانی همه مصرف داده‌ها", "confirmResetMessage": "آیا مطمئن هستید که می‌خواهید مصرف داده‌ها را برای همه حساب‌ها بازنشانی کنید؟ این عمل قابل بازگشت نیست.", "warning": "این عمل مصرف داده‌ها را برای همه حساب‌ها در سیستم بازنشانی می‌کند"}, "clearUsageData": {"title": "پاک کردن داده‌های مصرف", "description": "حذف دائمی داده‌های مصرف از جداول انتخاب شده پایگاه داده", "selectTable": "انتخاب جدول", "selectTablePlaceholder": "جدولی برای پاک کردن انتخاب کنید", "noTableSelected": "لطفاً جدولی برای پاک کردن انتخاب کنید", "dataAfter": "داده‌ها بعد از", "dataBefore": "داده‌ها قبل از", "dataAfterPlaceholder": "تاریخ شروع را انتخاب کنید", "dataBeforePlaceholder": "تاریخ پایان را انتخاب کنید", "selectDateRange": "بازه زمانی برای فیلتر کردن داده‌ها بر اساس زمان ایجاد انتخاب کنید (اختیاری - برای پاک کردن همه داده‌ها خالی بگذارید)", "clearData": "پاک کردن داده‌ها", "clearing": "در حال پاک کردن...", "clearSuccess": "داده‌ها از جدول {{table}} با موفقیت پاک شد", "clearFailed": "پاک کردن داده‌های مصرف ناموفق بود", "confirmClear": "تأیید پاک کردن داده‌ها", "confirmClearMessage": "آیا مطمئن هستید که می‌خواهید تمام داده‌ها از جدول {{table}} را به طور دائمی حذف کنید؟ این عمل قابل بازگشت نیست.", "warning": "هشدار: این عمل تمام داده‌های مصرف را از جدول انتخاب شده به طور دائمی حذف می‌کند. این عمل قابل بازگشت نیست.", "tables": {"nodeUserUsages": "مصرف کاربران گره", "userUsages": "مصر<PERSON> کاربران"}}}, "theme": {"title": "تم"}}, "saving": "در حال ذخیره...", "general": "عمومی", "loading": "در حال بارگذاری...", "noResults": "نتیجه‌ای یافت نشد", "emptyState": {"noUsers": {"title": "هنوز کاربری وجود ندارد", "description": "با ایجاد اولین حساب کاربری شروع کنید", "createFirstUser": "ایجاد اولین کاربر"}}, "core": "هسته‌ها", "activeUsers": "کاربران فعال", "apply": "تا<PERSON><PERSON>د", "cancel": "لغو", "created": "ایج<PERSON> شده", "by": "توسط", "ip": "آی‌پی", "admin": "مدیر", "sudo": "کاربر ریشه (سوپردا<PERSON>)", "save": "ذخیره", "test": "تست", "testing": "در حال تست", "confirm": "تا<PERSON><PERSON>د", "admins": {"title": "مدی<PERSON>ان", "description": "مدیریت مدیران سیستم", "createAdmin": "ای<PERSON><PERSON> مدیر", "editAdmin": "ویرایش مدیر", "deleteAdmin": "<PERSON><PERSON><PERSON> مدیر", "username": "نام کاربری", "password": "<PERSON><PERSON><PERSON> عبور", "isSudo": "مد<PERSON><PERSON> ار<PERSON>د", "createdAt": "تاریخ ایجاد", "actions": "عملیات", "createSuccess": "مدیر «{{name}}» با موفقیت ایجاد شد", "createFailed": "ایج<PERSON> مدیر «{{name}}» ناموفق بود", "editSuccess": "مدیر «{{name}}» با موفقیت به‌روزرسانی شد", "editFailed": "به‌روزرسانی مدیر «{{name}}» ناموفق بود", "deleteSuccess": "مدیر {{name}} با موفقیت حذف شد", "deleteFailed": "حذف مدیر {{name}} ناموفق بود", "enterUsername": "نام کاربری را وارد کنید", "enterPassword": "رمز عبور را وارد کنید", "sudo": "دسترسی مدیر ارشد", "edit": "ذخیره تغییرات", "create": "ای<PERSON><PERSON> مدیر", "status": "وضعیت", "role": "نقش", "total.users": "کل کاربران", "used.traffic": "ترافیک مصرف‌ شده", "monitor.traffic": "نظارت بر ترافیک مصرف شده", "monitor.no_traffic": "هیچ ترافیکی وجود ندارد", "total": "کل مدیران", "active": "مدیران فعال", "disable": "مدیران غیرفعال", "telegramId": "شناسه تلگرام", "discord": "وبهوک دیسکورد", "supportUrl": "آدرس پشتیبانی", "subDomain": "دامنه اشتراک", "profile": "عنوان پروفایل", "subTemplate": "مسیر قالب اشتراک", "passwordConfirm": "تأیید رمز عبور", "enterPasswordConfirm": "رمز عبور را دوباره وارد کنید", "disableSuccess": "مدیر «{{name}}» با موفقیت غیرفعال شد", "enableSuccess": "مدیر «{{name}}» با موفقیت فعال شد", "disableFailed": "غیرفعال‌<PERSON><PERSON><PERSON>ی مدیر «{{name}}» با شکست مواجه شد", "enableFailed": "فعال‌سازی مدیر «{{name}}» با شکست مواجه شد", "resetUsersUsage": "ریست مصرف کاربران توسط ادمین", "resetUsageSuccess": "مصرف کاربر «{{name}}» با موفقیت توسط ادمین ریست شد", "resetUsageFailed": "ریست مصرف کاربر «{{name}}» توسط ادمین ناموفق بود", "reset": "ریست مصرف", "discordId": "شناسه دیسکورد", "lifetime.used.traffic": "مجموع کل", "used": {"traffic": "مصرف ترافیک"}, "monitor": {"traffic": "نظارت بر مصرف ترافیک مدیر در طول زمان", "no_traffic": "داده‌ای برای ترافیک موجود نیست"}}, "shortcuts": {"title": "میانبرهای صفحه‌کلید", "description": "میانبرهای داشبورد خود را برای دسترسی سریع مدیریت کنید", "add": "افزودن میانبر", "empty": "هیچ میانبری پیکربندی نشده است"}, "quickActions": {"title": "عملیات سریع", "description": "عملیاتی را برای ایجاد آیتم‌های جدید در سیستم انتخاب کنید", "comingSoon": "به زودی"}, "resetUsersUsage.prompt": "آیا مطمئن هستید که می‌خواهید مصرف کاربر ادمین <b>{{name}}</b> را ریست کنید؟", "admin.disable": "غیر فعالسازی مدیر", "admin.enable": "فعالسازی مدیر", "deleteAdmin.prompt": "آیا مطمئن هستید که می‌خواهید مدیر <b>{{name}}</b> را حذف کنید؟", "activeUsers.prompt": "آیا می‌خواهید همه کاربران تحت مدیریت {{name}} را فعال کنید؟", "disableUsers.prompt": "آیا می‌خواهید همه کاربران تحت مدیریت {{name}} را غیرفعال کنید؟", "manageAccounts": "کنترل، بروزرسانی، و ترتیب حساب‌های کاربری", "manageGroups": "کنترل، بروزرسانی، و ترتیب گروه‌های کاربری", "manageNodes": "نظارت، کنترل، افزودن، ویرایش و حذف گره‌ها", "edit": "ویرایش", "editGroup": "ویرایش گروه", "disable": "غیرفعال کردن", "warning": "هشدار", "remove": "<PERSON><PERSON><PERSON>", "modify": "ویرایش", "templates.userTemplates": "قالب‌ها", "editUserTemplateModal.title": "ویرایش قالب کاربر", "userTemplateModal.title": "ایج<PERSON> قالب کاربر", "templates": {"title": "قالب‌ها", "description": "مدیریت قالب‌های شما.", "addTemplate": "ای<PERSON><PERSON> قالب", "editSuccess": "قالب کاربر «{{name}}» با موفقیت به‌روزرسانی شد", "createSuccess": "قالب کاربر «{{name}}» با موفقیت ایجاد شد", "editFailed": "به‌روزرسانی قالب کاربر «{{name}}» ناموفق بود", "createFailed": "ایج<PERSON> قالب کاربر «{{name}}» ناموفق بود", "name": "نام", "status": "وضعیت", "prefix": "پیشوند نام کاربری", "suffix": "پسوند نام کاربری", "dataLimit": "محدودیت داده", "expire": "مدت انقضا", "onHoldTimeout": "زمان انتظار توقف", "method": "روش", "flow": "جریان", "groups": "گروه‌ها", "userDataLimitStrategy": "استراتژی بازنشانی محدودیت داده‌ها", "resetUsage": "بازنشانی استفاده", "groupsExistingWarning": "شما هنوز هیچ <a>گروهی</a> اضافه نکرده‌اید.", "deleteSuccess": "قالب {{name}} با موفقیت حذف شد", "deleteFailed": "حذ<PERSON> قالب {{name}} ناموفق بود", "deleteUserTemplateTitle": "<PERSON><PERSON><PERSON> قالب کاربر", "deleteUserTemplatePrompt": "آیا مطمئن هستید که می‌خواهید قالب <b>{{name}}</b> را حذف کنید؟", "duplicateSuccess": "قالب «{{name}}» با موفقیت کپی شد", "duplicateFailed": "ک<PERSON>ی کردن قالب «{{name}}» ناموفق بود", "enableSuccess": "قالب «{{name}}» با موفقیت فعال شد", "disableSuccess": "قالب «{{name}}» با موفقیت غیرفعال شد", "enableFailed": "فعال‌سازی مدیر «{{name}}» ناموفق بود", "disableFailed": "غیرفعال‌<PERSON><PERSON><PERSON>ی مدیر «{{name}}» ناموفق بود"}, "core.configuration": "پیکربندی", "core.generalErrorMessage": "مشکلی پیش آمده، لطفا پیکربندی را بررسی کنید", "core.logs": "گزارش", "core.restartCore": "اجرا مجدد هسته", "core.restarting": "درحال اجرا مجدد...", "core.save": "ذخیره", "core.socket.closed": "بسته شده", "core.socket.connected": "متصل", "core.socket.connecting": "در حال اتصال...", "core.socket.not_connected": "عدم اتصال", "core.successMessage": "تنظیمات هسته با موفقیت ثبت شد", "core.title": "تنظیمات هسته", "createNewUser": "ساخت کاربر", "createUser": "افزودن کاربر", "createGroup": "افزودن گروه", "dataUsage": "مصرف داده", "dateFormat": "MM/dd/yyyy", "dateInfo.day": " روز", "dateInfo.hour": " ساعت", "dateInfo.min": " دق<PERSON><PERSON>ه", "dateInfo.month": " ماه", "dateInfo.year": " سال", "delete": "<PERSON><PERSON><PERSON>", "deleteNode.deleteSuccess": "گره {{name}} با موفقیت حذف شد", "deleteNode.prompt": "از حذف گره <b>{{name}}</b> مطمئن هستید؟", "deleteNode.title": "حذ<PERSON> گره", "deleteHost.title": "حذ<PERSON> ها<PERSON>ت", "deleteHost.prompt": "آیا مطمئن هستید که می‌خواهید هاست <b>{{name}}</b> را حذف کنید؟", "deleteHost.deleteSuccess": "هاست {{name}} با موفقیت حذف شد", "deleteHost.deleteFailed": "حذف هاست {{name}} ناموفق بود", "editNode.editSuccess": "گره {{name}} با موفقیت بروزرسانی شد", "editNode.title": "ویرایش گره", "deleteUser.deleteSuccess": "{{username}} با موفقیت حذف شد.", "deleteUser.prompt": "از حذف کاربر <b>{{username}}</b> مطمئن هستید?", "deleteUser.title": "<PERSON><PERSON><PERSON> کاربر", "disabled": "غیر فعال", "expire": "انقضا", "expired": "پایان یافته در {{time}}", "expires": "پایان در {{time}}", "header.donation": "کمک مالی", "header.hostSettings": "تنظیمات هاست", "header.logout": "خروج", "header.nodeSettings": "تنظیمات گره‌ها", "header.nodesUsage": "نمودار گره‌ها", "hostsDialog": {"addHost": "افزودن هاست", "loading": "در حال بارگذاری...", "port": "پورت", "fingerprint": "اثر انگشت", "sni": "SNI", "sniPlaceholder": "SNI (مثال: example.com)", "host": "هاست", "path": "مسیر", "networkSettings": "تنظیمات شبکه", "transportSettings": "تنظیمات انتقال", "transportType": "نوع انتقال", "selectTransport": "انتخاب نوع انتقال", "serviceName": "نام سرویس", "enterServiceName": "نام سرویس gRPC را وارد کنید", "seed": "سید", "enterSeed": "سید KCP را وارد کنید", "enterPath": "مسیر را وارد کنید (مثال: /xray)", "enterHost": "هاست را وارد کنید (مثال: example.com)", "header": "ه<PERSON><PERSON>", "selectHeader": "نوع هدر را انتخاب کنید", "multiMode": "حالت چندگانه", "securitySettings": "تنظیمات امنیتی", "camouflagSettings": "تنظیمات مخفی‌سازی", "security": "امنیت", "alpn": "ALPN", "allowInsecure": "اجازه ناامن", "muxEnable": "فعال‌سازی Mux", "randomUserAgent": "استفاده از User-Agent تصادفی", "inboundDefault": "ورودی پیش‌فرض", "earlyData": "داده‌های اولیه", "httpHeaders": "هدرهای HTTP", "headersName": "نام هدر", "headersValue": "مقدا<PERSON> هدر", "addHeader": "افزودن هدر", "removeHeader": "<PERSON><PERSON><PERSON>", "createSuccess": "هاست «{{name}}» با موفقیت ایجاد شد", "createFailed": "ایجاد هاست «{{name}}» با خطا مواجه شد", "editSuccess": "هاست «{{name}}» با موفقیت ویرایش شد", "editFailed": "ویرایش هاست «{{name}}» با خطا مواجه شد", "selectInbound": "انتخاب ورودی", "selectStatus": "انتخاب وضعیت", "remarkRequired": "توضیحات الزامی است", "address": "آدرس", "addressRequired": "آدرس الزامی است", "portRequired": "پورت الزامی است", "status": {"label": "وضعیت", "active": "فعال", "disabled": "غیرفعال", "limited": "م<PERSON><PERSON><PERSON><PERSON>", "expired": "منقضی شده", "onHold": "در انتظار"}, "noStatus": "وضعیتی انتخاب نشده", "clearAllStatuses": "پاک کردن همه وضعیت‌ها", "transportSettingsAccordion": "تنظیمات انتقال", "xhttp": {"mode": "حالت", "noGrpcHeader": "بدون هدر gRPC", "xPaddingBytes": "بایت‌های X-Padding", "scMaxEachPostBytes": "حداکثر بایت هر پست", "scMinPostsIntervalMs": "حداقل فاصله پست‌ها (میلی‌ثانیه)", "scMaxBufferedPosts": "حداکثر پست‌های بافر شده", "scStreamUpServerSecs": "سرور استریم بالا (ثانیه)", "xmux": "تنظیمات XMux", "maxConcurrency": "حداکثر همزمانی", "maxConnections": "حداکثر اتصالات", "cMaxReuseTimes": "حداکثر دفعات استفاده مجدد", "cMaxLifetime": "حداک<PERSON>ر طول عمر", "hMaxRequestTimes": "حداکثر دفعات درخواست", "hKeepAlivePeriod": "دوره نگهداری اتصال", "downloadSettings": "تنظیمات دانلود", "downloadSettingsInfo": "انتخاب هاست برای تنظیمات دانلود", "selectDownloadSettings": "انتخاب هاست تنظیمات دانلود"}, "grpc": {"multiMode": "حالت چندگانه", "idleTimeout": "مه<PERSON><PERSON> بیکاری", "healthCheckTimeout": "مهلت بررسی سلامت", "permitWithoutStream": "اجازه بدون جریان", "initialWindowsSize": "اندازه اولیه پنجره"}, "kcp": {"header": "ه<PERSON><PERSON>", "mtu": "MTU", "tti": "TTI", "uplinkCapacity": "ظر<PERSON><PERSON>ت آپلینک", "downlinkCapacity": "ظر<PERSON><PERSON>ت دانلینک", "congestion": "تراکم", "readBufferSize": "اندازه بافر خواندن", "writeBufferSize": "اندازه بافر نوشتن"}, "tcp": {"title": "تنظیمات TCP", "header": "نوع هدر", "request": {"title": "تنظیمات درخواست", "version": "نسخه HTTP", "method": "متد HTTP", "headers": "هدرهای درخواست"}, "response": {"title": "تنظیمات پاسخ", "version": "نسخه HTTP", "status": "کد وضعیت", "reason": "دلیل وضعیت", "headers": "هدرها<PERSON> پاسخ"}, "headerName": "نام هدر", "headerValue": "مقدا<PERSON> هدر", "addHeader": "افزودن هدر", "removeHeader": "<PERSON><PERSON><PERSON>", "requestHeaders": "هدرهای درخواست", "responseHeaders": "هدرها<PERSON> پاسخ"}, "websocket": {"heartbeatPeriod": "دوره ضربان قلب"}, "fragment": {"title": "قطعه‌بندی", "packets": "بسته‌ها", "packetsPlaceholder": "تعداد بسته‌ها را وارد کنید", "length": "طول", "lengthPlaceholder": "طول را وارد کنید", "interval": "فاصله", "intervalPlaceholder": "فاصله را وارد کنید", "allFieldsRequired": "اگر یک فیلد پر شود، همه فیلدها باید پر شوند", "info": "طول،فاصله،بسته (مثال: 10-100,100-200,tlshello)", "info.attention": "توجه: در حال حاضر، این ویژگی فقط در streisand >= 1.6.12 و v2rayNG >= 1.8.16 (پیکربندی سفارشی) پشتیبانی می‌شود", "info.examples": "مثال‌ها:"}, "noise": {"title": "نویز", "addNoise": "افزودن نویز", "removeNoise": "<PERSON><PERSON><PERSON> نویز", "packet": "بسته", "packetPlaceholder": "مقدار پکت را وارد کنید (مثال: rand:10-20)", "delay": "تاخیر", "delayPlaceholder": "تاخیر را وارد کنید", "rand": "تصادفی", "info": "بسته،تاخیر (مثال: rand:10-20,100-200)", "info.attention": "توجه: در حال حاضر، این ویژگی فقط در streisand >= 1.6.32 و v2rayNG >= 1.8.39 (پیکربندی سفارشی) پشتیبانی می‌شود", "info.examples": "مثال‌ها:"}, "muxSettings": "تنظیمات Mux", "enableMux": "فعال‌سازی Mux", "xraySettings": "تنظیمات <PERSON>", "singBoxSettings": "تنظیمات Sing-box", "clashSettings": "تنظیمات Clash", "addXraySettings": "افزودن تنظیمات Xray", "addSingBoxSetting": "افزودن تنظیمات Sing-box", "addClashSettings": "افزودن تنظیمات Clash", "protocol": "پروتکل", "selectProtocol": "انتخاب پروتکل", "maxConnections": "حداکثر اتصالات", "maxStreams": "حداکثر جریان‌ها", "minStreams": "حداقل جریان‌ها", "padding": "فعال‌سازی پدینگ", "enabled": "فعال", "concurrency": "همزمانی", "xudpConcurrency": "همزمانی XUDP", "xudpProxy443": "پروکسی XUDP 443", "selectXudpProxy443": "انتخاب پروکسی XUDP 443", "statistic": "فعال‌سازی آمار", "onlyTcp": "فقط TCP", "brutal": {"enable": "فعال‌سا<PERSON><PERSON> Bru<PERSON>", "title": "تنظیمات Bru<PERSON>", "upMbps": "سرعت آپلود (مگابیت بر ثانیه)", "downMbps": "سرعت دانلود (مگابیت بر ثانیه)"}, "variables": {"title": "از این متغیرها برای پویا کردن استفاده کنید", "server_ip": "آدرس IP سرور فعلی", "server_ipv6": "آدرس IPv6 سرور فعلی", "username": "نام کاربری", "data_usage": "مصرف داده فعلی", "data_left": "داده باقی‌مانده", "data_limit": "محدودیت مصرف داده", "days_left": "روزهای باقی‌مانده", "expire_date": "تاریخ انقضا", "jalali_expire_date": "تاریخ انقضا به تقویم خورشیدی", "time_left": "ز<PERSON>ان باقی‌مانده", "status_text": "وضعیت کاربر", "status_emoji": "وضعیت کاربر به صورت ایموجی (✅,⌛️,🪫,❌,🔌)", "protocol": "پروتکل پروکسی (مثال: VMess)", "transport": "روش انتقال پروکسی (مثال: ws)"}, "advancedOptions": "تنظیمات پیشرفته", "host.info": "به طور پیش‌فرض، اگر میزبان درخواست در پیکربندی XRAY تنظیم شده باشد، از آن استفاده می‌شود. با این حال، در صورت نیاز، می‌توانید یک میزبان درخواست سفارشی در اینجا تنظیم کنید.", "host.multiHost": "برای تنظیم چند آدرس، آنها را با <badge>,</badge> جدا کنید. هر بار یک آدرس به صورت تصادفی انتخاب می‌شود.", "host.wildcard": "از <badge>*</badge> برای تولید یک رشته تصادفی استفاده کنید (برای دامنه‌های wildcard کار می‌کند)", "path.info": "تنظیم مسیر برای کاربران هاست، مفید در پشت پروکسی معکوس.", "port.info": "به طور پیش‌فرض، هاست از پورت اعلام شده در ورودی استفاده می‌کند. اگر ترافیک از پورت دیگری هدایت می‌شود، می‌توانید پورت سفارشی تنظیم کنید. به عنوان مثال، سرور ممکن است ترافیک را از پورت 443 به پورت پیش‌فرض ورودی شما هدایت کند.", "proxyOutbound.info": "ترافیک خروجی اضافی (فقط در پیکربندی سفارشی v2ray)", "security.info": "اگر سرور واسط این هاست از لایه امنیتی متفاوتی نسبت به پیش‌فرض ورودی شما استفاده می‌کند، می‌توانید لایه امنیتی سفارشی را اینجا تنظیم کنید.", "useSniAsHost": "استفاده از SNI به عنوان هاست", "httpVersions": {"1.0": "HTTP/1.0", "1.1": "HTTP/1.1", "2.0": "HTTP/2.0", "3.0": "HTTP/3.0"}, "httpMethods": {"GET": "GET", "POST": "POST", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD", "OPTIONS": "OPTIONS", "PATCH": "PATCH", "TRACE": "TRACE", "CONNECT": "CONNECT"}, "httpReasons": {"100": "ادامه", "101": "تغییر پروتکل", "200": "موفق", "201": "ایج<PERSON> شد", "202": "پذیرفته شد", "203": "اطلاعات غیرمجاز", "204": "بدون محتوا", "205": "بازنشانی محتوا", "206": "محت<PERSON><PERSON> جزئی", "300": "انتخاب چندگانه", "301": "انتقال دائمی", "302": "پیدا شد", "303": "مشاهده مکان دیگر", "304": "تغییر نکرده", "305": "استفاده از پراکسی", "307": "انتقال موقت", "308": "انتقال دائمی", "400": "درخواست نامعتبر", "401": "غیرمجاز", "402": "نیاز به پرداخت", "403": "ممنوع", "404": "پیدا نشد", "405": "متد مجاز نیست", "406": "قابل قبول نیست", "407": "نیاز به احراز هویت پراکسی", "408": "زمان درخواست به پایان رسید", "409": "تضاد", "410": "حذ<PERSON> ش<PERSON>ه", "411": "نیاز به طول", "412": "شرط اولیه برقرار نیست", "413": "بار درخواست خیلی بزرگ است", "414": "URI خیلی طولانی است", "415": "نوع رسانه پشتیبانی نمی‌شود", "416": "محدوده درخواست قابل اجرا نیست", "417": "انتظار برآورده نشد", "418": "من یک قوری هستم", "421": "درخواست اشتباه", "422": "موجودیت غیرقابل پردازش", "423": "قفل شده", "424": "وابستگی شکست خورده", "425": "<PERSON><PERSON><PERSON><PERSON> زود", "426": "نیاز به ارتقا", "428": "نیاز به شرط اولیه", "429": "درخواست‌های خیلی زیاد", "431": "فیلدهای هدر درخواست خیلی بزرگ هستند", "451": "به دلایل قانونی در دسترس نیست", "500": "خطای داخلی سرور", "501": "پیاده‌سازی نشده", "502": "درگاه نامعتبر", "503": "سرویس در دسترس نیست", "504": "زمان درگاه به پایان رسید", "505": "نسخه HTTP پشتیبانی نمی‌شود"}, "selectReason": "انتخاب دلیل"}, "inbound": "ورودی", "remark": "اسم هاست", "none": "<PERSON><PERSON><PERSON>", "create": "ایجاد", "itemsPerPage": "تعداد در صفحه", "login": "ورود", "login.fieldRequired": "این فیلد باید پر شود!", "login.loginYourAccount": "وارد حسا<PERSON> خود شوید", "login.welcomeBack": "خوش آمدید, لطفا اطلاعات خود را وارد کنید", "memoryUsage": "مصرف حافظه", "next": "بعدی", "monitorServers": "نظارت بر سرورها و کاربران شما", "manageServers": "کنترل جزئیات سرور شما", "nodes.prompt": "افزودن و پیکربندی جزئیات گره", "nodes.addHostForEveryInbound": "استفاده از این گره به عنوان میزبان تمام ورودی‌ها", "nodes.addNewMarzbanNode": "افزودن گره مرزبان", "nodes.addNode": "افزودن گره", "nodes.addNodeSuccess": "گره {{name}} با موفقیت افزوده شد", "nodes.apply": "ویرایش گره", "nodes.certificate": "گواهی", "nodes.certificateCopied": "گواهی در کلیپ‌بورد کپی شد", "nodes.certificateDownloaded": "گواهی با موفقیت دانلود شد", "nodes.certificateDescription": "برای راه‌ان<PERSON><PERSON><PERSON>ی نود، باید این گواهی را برای برقراری ارتباط امن بین سرور اصلی و نود پیکربندی کنید", "nodes.connection-hint": "برای راه اندازی یک گره جدید، لازم است گواهی زیر را روی گره تنظیم کنید تا یک اتصال امن بین سرور اصلی و گره ایجاد شود.", "nodes.download-certificate": "د<PERSON><PERSON><PERSON><PERSON> گواهی", "nodes.editNode": "ویرایش گره", "nodes.hide-certificate": "مخ<PERSON>ی کردن گواهی", "nodes.nodeAPIPort": "پورت API", "nodes.nodeAddress": "آدرس", "nodes.nodeName": "نام", "nodes.nodePort": "پورت", "nodes.reconnect": "اتصال مجدد", "nodes.reconnecting": "درحال اتصال مجدد...", "nodes.show-certificate": "نمایش دادن گواهی", "nodes.title": "گره‌ها", "nodes.usageCoefficient": "ضریب گره", "on_hold": "در انتظار اتصال", "duplicate": "تکرار", "createAndManageTemplates": "ایجاد و مدیریت قالب‌ها", "password": "گذرواژه", "previous": "قب<PERSON>ی", "qrcodeDialog.sublink": "لینک اشتراک", "reset": "بازنشانی", "resetAllUsage": "بازنشانی کل مصرف", "resetAllUsage.error": "بازنشانی مصرف انجام نشد، دوباره تلاش کنید.", "resetAllUsage.prompt": "این عمل مصرف تمام کاربران را بازنشانی می‌کند. از این بازنشانی مطمئن هستید؟ این عمل قابل بازگشت نیست!", "resetAllUsage.success": "مصرف کل با موفقیت بازنشانی شد.", "resetAllUsage.title": "بازنشانی مصرف تمام کاربران", "resetUserUsage.error": "بازنشانی مصرف انجام نشد، دوباره تلاش کنید.", "resetUserUsage.prompt": "از بازنشانی مصرف <b>{{username}}</b> مطئمن هستید؟", "resetUserUsage.success": "مصرف {{username}} با موفقیت بازنشانی شد.", "resetUserUsage.title": "بازنشانی مصرف کاربر", "revoke": "بازنشانی", "revokeUserSub.error": "بازنشانی اشتراک انجام نشد، لطفاً دوباره امتحان کنید.", "revokeUserSub.prompt": "آیا مطمئن هستید که می خواهید اشتراک «{{username}}» را بازنشانی کنید؟", "revokeUserSub.success": "اشتراک {{username}} با موفقیت بازنشانی شد.", "revokeUserSub.title": "بازنشانی اشتراک کاربر", "search": "جستجو", "status.active": "فعال", "status.enable": "فعال", "status.disabled": "غیرفعال", "status.expired": "منقضی شده", "status.limited": "اتمام حد مصرف", "status.on_hold": "در انتظار اتصال", "nodeModal.status.error": "خطا در اتصال", "nodeModal.status.disabled": "غیرفعال", "nodeModal.status.connecting": "درحال اتصال", "nodeModal.status.connected": "متصل", "userDialog.absolute": "مطلق", "userDialog.custom": "انتخابی", "userDialog.dataLimit": "حد مصرف داده", "userDialog.days": "روزها", "userDialog.editUser": "ویرایش کاربر", "userDialog.editUserTitle": "ویرایش کاربر", "userDialog.endDate": "تاریخ پایان", "userDialog.expiryDate": "تاریخ پایان", "userDialog.timeOutDate": "تاریخ تایم اوت", "userDialog.generatedByDefault": "پیش‌فرض ساخته می‌شود", "userDialog.hours": "ساعات", "userDialog.method": "روش", "userDialog.months": "ماه‌ها", "userDialog.note": "توضیحات", "userDialog.onHold": "در انتظار", "userDialog.onHoldExpireDuration": "مدت انقضا", "userDialog.onHoldExpireDurationPlaceholder": "به عنوان مثال: 7", "userDialog.optional": "اختیاری", "userDialog.periodicUsageReset": "بازنشانی دوره‌ای مصرف", "userDialog.protocols": "پروتکل‌ها", "userDialog.relative": "نسبی", "userDialog.resetStrategyAnnually": "سالانه", "userDialog.resetStrategyDaily": "روزانه", "userDialog.resetStrategyMonthly": "ماهانه", "userDialog.resetStrategyNo": "<PERSON><PERSON><PERSON>", "userDialog.resetStrategyWeekly": "هفتگی", "userDialog.resetUsage": "بازنشانی مصرف", "userDialog.revokeSubscription": "بازنشانی اشتراک", "userDialog.subscriptionInfo": "اطلاعات اشتراک", "userDialog.subscriptionUpdated": "آخرین بروزرسانی", "userDialog.lastClient": "آ<PERSON>رین کلاینت", "userDialog.subscriptionNotAccessed": "هنوز دسترسی نشده", "userDialog.unknownClient": "کلاینت ناشناخته", "userDialog.selectOneProtocol": "لط<PERSON>ا حداق<PERSON> یک پروتکل انتخاب کنید", "userDialog.shadowsocksDesc": "سریع و امن، اما کارآمد کمتر از بقیه", "userDialog.startDate": "تاریخ شروع", "userDialog.total": "مجموع: ", "userDialog.trojanDesc": "سبک، امن و فوق‌العاده سریع", "userDialog.usage": "مصرف", "userDialog.userAlreadyExists": "کاربر وجود دارد", "userDialog.userCreated": "کاربر {{username}} ساخته شد.", "userDialog.userEdited": "کاربر {{username}} ویر<PERSON><PERSON>ش شد.", "userDialog.vlessDesc": "سبک، سریع و امن", "userDialog.vmessDesc": "سریع و امن", "userDialog.warningNoProtocol": "لط<PERSON>ا حداق<PERSON> یک پروتکل انتخاب کنید", "userDialog.weeks": "هفته‌ها", "userDialog.proxySettingsAccordion": "تنظیمات پروکسی", "userDialog.proxySettings.vmess": "وی مس (VMess)", "userDialog.proxySettings.vless": "وی لس (VLESS)", "userDialog.proxySettings.trojan": "ترو<PERSON><PERSON> (Trojan)", "userDialog.proxySettings.shadowsocks": "شدو ساکس (Shadowsocks)", "userDialog.proxySettings.id": "شناسه (ID)", "userDialog.proxySettings.password": "<PERSON><PERSON><PERSON> عبور", "userDialog.proxySettings.method": "روش رمزنگاری", "userDialog.proxySettings.flow": "جریان (Flow)", "userDialog.proxySettings.desc": "تنظیمات اختصاصی پروتکل برای این کاربر را پیکربندی کنید.", "userDialog.expireDate": "تاریخ انقضا", "username": "نام کاربری", "users": "کاربران", "usersTable.copied": "کپی شد", "usersTable.copyConfigs": "تنظیمات کپی", "usersTable.copyLink": "کپی لینک اشتراک", "usersTable.dataUsage": "مصرف داده", "usersTable.noUser": "کاربری افزوده نشده است", "usersTable.noUserMatched": "به‌نظر میرسه کاربری که جستجو کردید، وجود ندارد", "usersTable.status": "وضعیت", "usersTable.total": "مجموع", "statistics.system": "سیستم", "statistics.totalTraffic": "کل ترافیک", "statistics.ramUsage": "مصر<PERSON> رم", "statistics.cpuUsage": "مصرف پردازنده", "statistics.cores": "هسته", "statistics.serverSelection": "انتخاب سرور", "statistics.selectServerToMonitor": "سروری را برای نظارت بر معیارهای عملکرد انتخاب کنید", "statistics.selectServer": "انتخاب سرور", "statistics.realTimeData": "داده‌های لحظه‌ای", "statistics.historicalData": "داده‌های تاریخی", "statistics.viewHistorical": "مشاهده تاریخی", "statistics.viewRealtime": "مشاهده زمان واقعی", "statistics.historical": "تاریخی", "statistics.realtime": "زمان واقعی", "statistics.realtimeDescription": "آمار و نمودارهای زنده برای سرورها و کاربران شما.", "statistics.historicalDescription": "داده‌های عملکرد تاریخی سرور برای بازه زمانی انتخاب شده", "statistics.selectTimeRange": "انتخاب بازه زمانی", "statistics.selectTimeRangeDescription": "دوره مورد نظر را برای تجزیه و تحلیل داده‌های تاریخی انتخاب کنید", "statistics.selectTimeRangeToView": "لطفاً یک بازه زمانی برای مشاهده داده‌های تاریخی انتخاب کنید", "enable": "فعالسازی", "editHost.title": "ویرایش هاست", "host": {"enableSuccess": "میز<PERSON><PERSON> «{{name}}» با موفقیت فعال شد", "enableFailed": "فعال کردن میزبان «{{name}}» با شکست مواجه شد", "disableSuccess": "میز<PERSON><PERSON> «{{name}}» با موفقیت غیرفعال شد", "disableFailed": "غیرفعال کردن میزبان «{{name}}» با شکست مواجه شد", "duplicateSuccess": "میز<PERSON><PERSON> «{{name}}» با موفقیت کپی شد", "duplicateFailed": "کپی کردن میزبان «{{name}}» با شکست مواجه شد", "xudp_proxy_443": "پراکسی XUDP 443", "reject": "ر<PERSON> کر<PERSON>ن", "allow": "اجازه دادن", "skip": "ر<PERSON> ش<PERSON>ن"}, "usersTable.sortByExpire": "مرتب‌سازی بر اساس زمان انقضا", "group": {"createSuccess": "گروه «{{name}}» با موفقیت ایجاد شد", "createFailed": "ایجاد گروه «{{name}}» با خطا مواجه شد", "editSuccess": "گروه «{{name}}» با موفقیت ویرایش شد", "disableSuccess": "گروه «{{name}}» با موفقیت غیرفعال شد", "disableFailed": "غیرفعال‌سازی گروه «{{name}}» با خطا مواجه شد", "enableSuccess": "گروه «{{name}}» با موفقیت فعال شد", "enableFailed": "فعال‌سازی گروه «{{name}}» با خطا مواجه شد", "editFailed": "ویرایش گروه «{{name}}» با خطا مواجه شد", "deleteSuccess": "گروه «{{name}}» با موفقیت حذف شد", "deleteFailed": "حذف گروه «{{name}}» با خطا مواجه شد", "deleteConfirm": "آیا مطمئن هستید که می‌خواهید گروه «{{name}}» را حذف کنید؟"}, "name": "نام", "nodeModal": {"title": "افزودن گره", "description": "افزودن یک گره جدید به شبکه", "name": "نام گره", "namePlaceholder": "نام گره را وارد کنید", "address": "آدرس گره", "addressPlaceholder": "آدرس گره را وارد کنید", "port": "پورت گره", "portPlaceholder": "پورت گره را وارد کنید", "usageRatio": "نسبت استفاده", "usageRatioPlaceholder": "نسبت استفاده را وارد کنید", "maxLogs": "حداکثر لاگ‌ها", "maxLogsPlaceholder": "حداکثر لاگ‌ها را وارد کنید", "connectionType": "نوع اتصال", "keepAlive": "نگهداری اتصال", "keepAliveDescription": "تنظیم فاصله نگهداری اتصال", "days": "روز", "minutes": "دق<PERSON><PERSON>ه", "seconds": "ثانیه", "certificate": "گواهی", "certificatePlaceholder": "گواهی را وارد کنید", "status": "وضعیت گره", "statusCheckSuccess": "گره با موفقیت متصل شد", "statusCheckFailed": "اتصال به گره با شکست مواجه شد", "errorDetails": "جزئیا<PERSON> خطا", "connectionError": "خطای اتصال", "retryConnection": "تلاش مجدد اتصال", "configurationError": "خطای پیکربندی", "validateConfig": "اعتبارسنجی پیکربندی", "fillRequiredFields": "لطفا تمام فیلدهای الزامی را قبل از بررسی وضعیت پر کنید", "apiKey": "کلید API", "apiKeyPlaceholder": "کلید API گره را وارد کنید", "generateUUID": "تو<PERSON><PERSON><PERSON> UUID", "coreConfig": "پیکربندی هسته", "selectCoreConfig": "هسته را انتخاب کنید", "hours": "ساعت", "hideDetails": "مخ<PERSON>ی کردن جزئیات", "showDetails": "نمایش جزئیات", "statusCheck": "بررسی وضعیت", "statusChecking": "در حال بررسی...", "reconnect": "دوباره متصل کن", "reconnecting": "در حال دوباره متصل کردن...", "sync": "همگام‌سازی", "syncing": "در حال همگام‌سازی...", "syncSuccess": "گره با موفقیت همگام‌سازی شد", "syncFailed": "همگام‌سازی گره ناموفق بود", "gatherLogs": "جمع‌آوری لاگ‌ها", "gatherLogsDescription": "فعال‌سازی جمع‌آوری لاگ‌ها از این گره برای نظارت و عیب‌یابی", "statusMessages": {"checkUnavailableForNew": "بررسی وضعیت برای گره‌های جدید در دسترس نیست. ابتدا گره را ایجاد کنید تا بتوانید وضعیت آن را بررسی کنید."}, "onlineStats": {"button": "آ<PERSON>ار آنلاین", "title": "آ<PERSON>ار کاربران آنلاین", "ipListTitle": "آدرس‌های IP برای {{username}}", "nodeInfo": "گره: {{nodeName}}", "searchPlaceholder": "نام کاربری را وارد کنید...", "enterUsername": "لطفاً نام کاربری را وارد کنید", "refreshed": "داده‌ها با موفقیت به‌روزرسانی شدند", "refreshFailed": "به‌روزرسانی داده‌ها ناموفق بود", "backToStats": "بازگشت به آمار", "ipAddresses": "آدرس IP", "errorLoading": "خطا در بارگذاری آمار کاربر", "userNotOnline": "کاربر آنلاین نیست", "searchUser": "برای مشاهده آمار آنلاین، کاربری را جستجو کنید", "autoRefresh": "به‌روزرسانی خودکار هر 5 ثانیه", "connections": "اتصال", "protocols": "پروتکل", "viewIPs": "مشاهده IP ‌ها", "userNotFound": "کاربر '{{username}}' یافت نشد یا آنلاین نیست", "errorLoadingIPs": "خطا در بارگذاری آدرس‌های IP کاربر"}}, "nodes": {"title": "گره‌ها", "description": "مدیریت گره‌ها", "addNode": "افزودن گره", "editNode": "ویرایش گره", "deleteNode": "حذ<PERSON> گره", "nodeName": "نام گره", "nodeAddress": "آدرس گره", "nodePort": "پورت گره", "usageCoefficient": "ضریب مصرف", "connectionType": "نوع اتصال", "serverCA": "گواهی سرور", "keepAlive": "حفظ اتصال", "maxLogs": "حداکثر لاگ‌ها", "maxLogsPlaceHolder": "حداکثر لاگ‌ها را وارد کنید (ثانیه)", "status": "وضعیت", "actions": "عملیات", "connected": "متصل", "disconnected": "قطع شده", "disabled": "غیرفعال", "enabled": "فعال", "createSuccess": "گره «{{name}}» با موفقیت ایجاد شد", "createFailed": "ایجاد گره «{{name}}» با خطا مواجه شد", "editSuccess": "گره «{{name}}» با موفقیت ویرایش شد", "editFailed": "ویرایش گره «{{name}}» با خطا مواجه شد", "deleteSuccess": "گره «{{name}}» با موفقیت حذف شد", "deleteFailed": "حذف گره «{{name}}» با خطا مواجه شد", "enableSuccess": "گره «{{name}}» با موفقیت فعال شد", "enableFailed": "فعال‌سازی گره «{{name}}» با خطا مواجه شد", "disableSuccess": "گره «{{name}}» با موفقیت غیرفعال شد", "disableFailed": "غیرفعال‌سازی گره «{{name}}» با خطا مواجه شد", "certificate": "گواهینامه", "certificateCopied": "گواهینامه در کلیپ‌بورد کپی شد", "certificateDownloaded": "گواهینامه با موفقیت دانلود شد", "certificateDescription": "برای تنظیم گره، باید این گواهینامه را برای برقراری ارتباط امن بین سرور اصلی و گره پیکربندی کنید", "selectNode": "یک گره انتخاب کنید", "logs": {"title": "لاگ‌ها", "description": "مشاهده و نظارت بر لاگ‌های گره‌ها", "noLogs": "لاگی موجود نیست", "loading": "در حال بارگذاری لاگ‌ها...", "timestamps": "زمان", "autoScroll": "اسکرول خودکار", "scrollToEnd": "اسکرول به پایین", "clear": "پاک کردن لاگ‌ها", "search": "جستجوی لاگ", "filter": "فیلتر لاگ‌ها", "levels": "سطوح لاگ", "debug": "دیباگ", "info": "اطلاعات", "warning": "هشدار", "error": "خطا", "maxLogsTooltip": "حداکثر لاگ‌های ذخیره شده در حافظه", "memory": "استفاده از حافظه", "custom": "سفار<PERSON>ی", "unlimited": "نامحدود", "memoryWarning": "مقادیر بالا ممکن است باعث مشکلات عملکردی مرورگر شود", "setCustom": "تنظیم محدودیت سفارشی", "customLimit": "وارد کردن محدودیت سفارشی", "apply": "اعمال"}}, "success": "موفق", "error": "خطا", "theme": {"title": "تم", "description": "سفارشی‌سازی تم برنامه شما", "light": "روشن", "dark": "تیره", "system": "سیستم", "selectTheme": "انتخاب تم", "themeSaved": "تم با موفقیت ذخیره شد", "themeChanged": "تم با موفقیت تغییر کرد", "visitThemePage": "از صفحه تم برای سفارشی‌سازی بیشتر دیدن کنید", "radiusSaved": "شعاع گوشه با موفقیت به‌روزرسانی شد", "mode": "حالت", "color": "رنگ", "radius": "شعاع گوشه", "zinc": "خاکستری", "rose": "صورتی", "blue": "آ<PERSON><PERSON>", "green": "سبز", "violet": "بنفش", "orange": "نارنجی", "yellow": "زرد", "default": "پیش‌<PERSON><PERSON>ض", "red": "قرمز", "modeDescription": "نحوه نمایش رابط کاربری را انتخاب کنید", "colorDescription": "طرح رنگی مورد نظر خود را انتخاب کنید", "radiusDescription": "میزان گردی عناصر رابط کاربری را تنظیم کنید", "lightDescription": "روشن و تمیز", "darkDescription": "آسان برای چشم‌ها", "systemDescription": "متناسب با دستگاه", "preview": "پیش‌نمایش زنده", "previewDescription": "تنظیمات تم خود را به صورت زمان واقعی ببینید", "dashboardPreview": "پیش‌نمایش داشبورد", "dashboardDescription": "نظارت بر عملکرد سیستم و مدیریت حساب‌های کاربری", "currentTheme": "تم فعلی", "sampleInput": "ورودی نمونه", "primaryButton": "دکمه اصلی", "resetToDefaults": "بازگشت به پیش‌فرض", "resetDescription": "تمام تنظیمات تم را به مقادیر پیش‌فرض بازگردانید", "resetting": "در حال بازگشت...", "reset": "بازگشت", "resetSuccess": "تم به تنظیمات پیش‌فرض بازگشت داده شد", "resetFailed": "بازگشت تنظیمات تم ناموفق بود", "radiusNone": "<PERSON><PERSON><PERSON>", "radiusSmall": "کوچک", "radiusMedium": "متوسط", "radiusLarge": "بزرگ"}, "coreConfigModal": {"addConfig": "افزودن پیکربندی هسته", "createNewConfig": "ایجاد پیکربندی جدید هسته", "editCore": "ویرایش پیکربندی هسته", "invalidJson": "پیکربندی JSON نامعتبر است", "createSuccess": "پیکربندی هسته «{{name}}» با موفقیت ایجاد شد", "createFailed": "ایجاد پیکربندی هسته «{{name}}» با خطا مواجه شد", "editSuccess": "پیکربندی هسته «{{name}}» با موفقیت به‌روزرسانی شد", "editFailed": "به‌روزرسانی پیکربندی هسته «{{name}}» با خطا مواجه شد", "keyPairGenerated": "ج<PERSON>ت کلید با موفقیت تولید شد", "shortIdGenerated": "شناسه کوتاه با موفقیت ایجاد شد", "shortId": "شناسه کوتاه", "shortIdCopied": "شناسه کوتاه در کلیپ‌بورد کپی شد", "publicKeyCopied": "کلید عمومی در کلیپ‌بورد کپی شد", "privateKeyCopied": "کلید خصوصی در کلیپ‌بورد کپی شد", "jsonConfig": "پیکربندی JSON", "editJson": "ویرایش پیکربندی JSON", "name": "نام", "namePlaceholder": "نام پیکربندی را وارد کنید", "fallback": "پشتیبان", "selectFallback": "پشتیبان را انتخاب کنید", "excludedInbound": "ورودی‌های مستثنی", "selectInbound": "ورودی را انتخاب کنید", "inbound": "ورودی", "generateKeyPair": "تو<PERSON><PERSON>د جفت کلید", "generateShortId": "تولید شناسه کوتاه", "publicKey": "<PERSON><PERSON><PERSON><PERSON> عمومی", "privateKey": "<PERSON><PERSON><PERSON><PERSON> خصوصی", "clearAllFallbacks": "پاک‌کردن همه فال‌بک‌ها", "clearAllExcluded": "پاک‌کردن همه ورودی‌های مستثنی‌شده", "copyPublicKey": "ک<PERSON><PERSON> کلید عمومی", "copyPrivateKey": "ک<PERSON><PERSON> کلید خصوصی", "copyShortId": "کپی شناسه کوتاه", "restartNodes": "راه‌اندازی مجدد گره‌ها", "generateShadowsocksPassword": "تولید رمز عبور Shadowsocks", "generatingShadowsocksPassword": "در حال تولید رمز عبور...", "shadowsocksPassword": "ر<PERSON>ز عبور Shadowsocks", "shadowsocksPasswordCopied": "رمز عبور در کلیپ‌بورد کپی شد", "copyShadowsocksPassword": "کپی رمز عبور", "selectEncryptionMethod": "انتخاب روش رمزنگاری", "regeneratePassword": "تولید مجدد رمز عبور"}, "settings.cores.title": "هسته‌ها", "settings.cores.description": "مدیریت هسته‌های شما", "settings.cores.addCore": "افزودن هسته", "settings.cores.noCores": "هیچ هسته‌ای موجود نیست. یکی اضافه کنید تا شروع کنید.", "settings.cores.duplicateSuccess": "پیکربندی هسته «{{name}}» با موفقیت کپی شد", "settings.cores.duplicateFailed": "خطا در کپی پیکربندی هسته «{{name}}»", "settings.cores.deleteSuccess": "هسته با موفقیت حذف شد", "settings.cores.deleteFailed": "حذف هسته با خطا مواجه شد", "settings.cores.delete": "حذ<PERSON> هسته", "settings.cores.coreNotFound": "هسته یافت نشد", "createdAt": "تاریخ ایجاد", "toggle": "تغییر وضعیت", "core.toggleSuccess": "هسته «{{name}}» با موفقیت تغییر وضعیت داده شد", "core.toggleFailed": "تغییر وضعیت هسته «{{name}}» با خطا مواجه شد", "core.deleteSuccess": "هسته «{{name}}» با موفقیت حذف شد", "core.deleteFailed": "حذف هسته «{{name}}» با خطا مواجه شد", "core.deleteConfirm": "آیا مطمئن هستید که می‌خواهید هسته «{{name}}» را حذف کنید؟", "close": "بستن", "copy": "کپی", "validation": {"generic": "{{field}} نامعتبر است", "min": "{{field}} با<PERSON><PERSON> حداقل {{min}} باشد", "max": "{{field}} باید حداکثر {{max}} باشد", "required": "وارد کردن {{field}} الزامی است", "invalid_type": "وارد کردن {{field}} الزامی است", "too_small": "{{field}} خی<PERSON><PERSON> کوتاه است", "too_big": "{{field}} <PERSON><PERSON><PERSON><PERSON> بلند است"}, "fields": {"username": "نام کاربری", "data_limit": "سقف مصرف داده", "expire": "تاریخ پایان", "note": "توضیحات", "on_hold_expire_duration": "مدت تعلیق", "group_ids": "گروه‌ها"}, "userDialog": {"deleteSuccess": "کاربر «{{name}}» با موفقیت حذف شد.", "resetUsageSuccess": "مصرف کاربر «{{name}}» بازنشانی شد.", "revokeSubSuccess": "اشتراک کاربر «{{name}}» لغو شد.", "activeNextPlanSuccess": "پلن بعدی برای کاربر «{{name}}» فعال شد.", "activeNextPlanError": "فعال‌سازی پلن بعدی برای کاربر «{{name}}» با شکست مواجه شد.", "deleteConfirmTitle": "<PERSON><PERSON><PERSON> کاربر", "deleteConfirm": "آیا مطمئن هستید که می‌خواهید کاربر «{{name}}» را حذف کنید؟", "nextPlanTitle": "پلن بعدی", "nextPlanTemplateId": "شناسه قالب", "nextPlanDataLimit": "حجم داده (گیگابایت)", "nextPlanExpire": "انقضا (روز)", "nextPlanAddRemainingTraffic": "افزودن ترافیک باقی‌مانده", "selectTemplate": "انت<PERSON>اب قالب", "selectTemplatePlaceholder": "یک قالب را انتخاب کنید", "selectNode": "انتخاب گره", "allNodes": "همه گره‌ها", "activeNextPlan": "فعال‌سازی پلن بعدی", "selectedGroups": "{{count}} گروه انتخاب شده", "editError": "خطا در ویرایش کاربر «{{name}}»", "createError": "خطا در ایج<PERSON> کاربر «{{name}}»", "selectStatus": "انتخاب وضعیت", "selectedTemplates": "{{count}} قالب انتخاب شده"}, "sidebar.expand": "باز کردن منو", "sidebar.collapse": "بستن منو", "usersTable.deleteUserTitle": "<PERSON><PERSON><PERSON> کاربر", "usersTable.deleteUserPrompt": "آیا مطمئن هستید که می‌خواهید کاربر «{{name}}» را حذف کنید؟ این عملیات غیرقابل بازگشت است.", "usersTable.deleteSuccess": "کاربر «{{name}}» با موفقیت حذف شد.", "usersTable.deleteFailed": "حذف کاربر «{{name}}» با خطا مواجه شد. {{error}}", "usersTable.delete": "<PERSON><PERSON><PERSON>", "usersTable.cancel": "لغو", "statistics.trafficUsage": "میزان مصرف ترافیک", "statistics.trafficUsageDescription": "کل مصرف ترافیک در تمام سرورها", "statistics.usageDuringPeriod": "مصرف در دوره زمانی", "statistics.users": "کاربران", "statistics.onlineUsers": "کاربران آنلاین", "statistics.limitedUsers": "کاربران محدود شده", "statistics.activeUsers": "کاربران فعال", "statistics.disabledUsers": "کاربران غیرفعال", "statistics.expiredUsers": "کاربران منقضی شده", "statistics.onHoldUsers": "کاربران در انتظار", "statistics.allUsers": "همه کاربران", "statistics.serverTraffic": "ترافیک سرور", "statistics.nodeTrafficDistribution": "توزیع ترافیک نودها", "statistics.noNodesAvailable": "هیچ نودی در دسترس نیست", "statistics.noDataAvailable": "هیچ داده‌ای در دسترس نیست", "statistics.noDataDescription": "یک بازه زمانی را انتخاب کنید تا آمار و روند مصرف ترافیک را مشاهده کنید.", "statistics.noNodesDescription": "هیچ نودی در حال حاضر متصل نیست تا توزیع ترافیک را نمایش دهد.", "statistics.loadingDescription": "در حال دریافت آخرین داده‌های آمار...", "statistics.waitingForData": "در انتظار داده‌ها", "statistics.waitingForDataDescription": "آمار لحظه‌ای در اینجا نمایش داده می‌شود پس از شروع جریان داده‌ها.", "statistics.noDataInRange": "داده‌ای در بازه انتخابی وجود ندارد", "statistics.noDataInRangeDescription": "هیچ داده ترافیکی برای دوره زمانی انتخابی یافت نشد. بازه زمانی متفاوتی را انتخاب کنید.", "statistics.selectNodeToView": "یک نود را انتخاب کنید تا آمار دقیق را مشاهده کنید", "statistics.userStatisticsDescription": "نمای کلی از وضعیت و فعالیت حساب‌های کاربری", "errors.connectionFailed": "اتصال با خطا مواجه شد. لطفاً شبکه خود را بررسی کنید و دوباره تلاش کنید.", "errors.statisticsLoadFailed": "بارگذاری آمار با خطا مواجه شد", "usersTable.resetUsageTitle": "ریست مصرف کاربر", "usersTable.resetUsagePrompt": "آیا مطمئن هستید که می‌خواهید مصرف «{{name}}» را ریست کنید؟", "usersTable.resetUsageSubmit": "ریست مصرف", "usersTable.resetUsageSuccess": "مصرف کاربر «{{name}}» با موفقیت ریست شد", "usersTable.resetUsageFailed": "ریست مصرف کاربر «{{name}}» ناموفق بود", "time": {"expires": "منقضی می‌شود", "expired": "منقضی شده", "year": "سال", "years": "سال", "month": "ماه", "months": "ماه", "day": "روز", "days": "روز", "hour": "ساعت", "hours": "ساعت", "min": "دق<PERSON><PERSON>ه", "mins": "دق<PERSON><PERSON>ه", "ago": "پ<PERSON>ش"}, "notConnectedYet": "هنوز متصل نشده", "node.xrayVersion": "نسخه هسته", "node.coreVersion": "نسخه گره", "calendar": {"selectRange": "انتخاب بازه زمانی", "startDate": "تاریخ شروع", "endDate": "تاریخ پایان"}, "usersTable": {"usageChart": "نمودار مصرف", "noUsageData": "داده‌ای برای این بازه زمانی وجود ندارد.", "tryDifferentRange": "بازه زمانی دیگری را امتحان کنید.", "trendingUp": "رون<PERSON> افزایشی", "trendingDown": "رون<PERSON> کاهشی", "usageSummary": "نمایش مجموع مصرف در بازه انتخاب شده."}, "setOwnerModal": {"title": "تعیین مالک", "currentOwner": "مالک فعلی:", "none": "بدون مالک", "selectAdmin": "انتخاب مالک جدید", "confirm": "تعیین مالک", "success": "مال<PERSON> کاربر {{username}} با موفقیت به {{admin}} تغییر یافت.", "error": "تغییر مالک کاربر {{username}} به {{admin}} ناموفق بود.", "loadError": "بارگذاری لیست مدیران ناموفق بود."}, "inboundTags": "تگ‌های ورودی", "searchInbounds": "جستجوی ورودی‌ها...", "noInboundsFound": "هیچ ورودی‌ای یافت نشد", "resilientNodeGroups": {"title": "Resilient Node Groups", "description": "Manage resilient node groups for improved reliability", "addGroup": "Add Group", "createGroup": "Create Resilient Node Group", "editGroup": "Edit Resilient Node Group", "name": "Name", "strategy": "Client Strategy", "nodes": "Nodes", "selectNodes": "Select nodes", "createSuccess": "Resilient node group created successfully", "updateSuccess": "Resilient node group updated successfully", "deleteSuccess": "Resilient node group deleted successfully", "operationFailed": "Failed to perform operation on resilient node group", "deleteFailed": "Failed to delete resilient node group", "strategies": {"urlTest": "URL Test", "fallback": "Fallback", "loadBalance": "Load <PERSON>", "clientDefault": "<PERSON><PERSON>", "none": "None"}, "clientDefault": "<PERSON><PERSON>"}, "hiddifyImport": {"title": "Import Users from Hiddify", "selectFile": "Select Hiddify Backup File", "fileSelected": "File selected: {{filename}}", "invalidFile": "Invalid File", "invalidFileDesc": "Please select a valid JSON file.", "unlimitedExpiration": "Set unlimited expiration for all users", "unlimitedExpirationDesc": "If enabled, all imported users will have unlimited expiration (expire = 0), ignoring package_days from Hiddify.", "smartUsernameParsing": "Enable smart username & note parsing", "smartUsernameParsingDesc": "If enabled, names like \"1234 John <PERSON>\" will be split: \"1234\" as username, \"<PERSON>\" as note. Otherwise, the full name will be used as username.", "protocolSelection": "Select Protocols", "protocolSelectionDesc": "Choose which protocols the imported users should have access to.", "noFileSelected": "No File Selected", "noProtocolsSelected": "No Protocols Selected", "importing": "Importing users...", "importUsers": "Import Users", "importComplete": "Import Complete", "importStats": "{{successful}} users imported successfully, {{failed}} failed.", "importSuccess": "Import Successful", "importSuccessDesc": "{{successful}} users imported successfully. {{failed}} users failed to import.", "importWarning": "Import Completed with Warnings", "importWarningDesc": "{{failed}} users failed to import. Check the details below.", "importError": "Import Failed", "importErrorDesc": "An error occurred during import. Please try again."}}
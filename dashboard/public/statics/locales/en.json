{"marzban": "Marzban", "dashboard": "Dashboard", "master": "Master", "dashboardDescription": "Marzban Management Dashboard", "active": "active", "online": "Online", "platform": "Platform", "allStatuses": "All Statuses", "status": "Status", "statistics": "Statistics", "selectAll": "Select All", "hosts": "Hosts", "success": "Success", "error": "Error", "username": "Username", "modifying": "Modifying...", "removing": "Removing...", "creating": "Creating", "monitorUsers": "Monitor Users", "alltime": "All time", "nodes": {"title": "Nodes", "description": "Manage your nodes", "addNode": "Create Node", "editNode": "Modify Node", "deleteNode": "Remove Node", "nodeName": "Node Name", "nodeAddress": "Node Address", "nodePort": "Node Port", "usageCoefficient": "Usage Coefficient", "connectionType": "Connection Type", "serverCA": "Server CA", "keepAlive": "Keep Alive", "maxLogs": "<PERSON>", "maxLogsPlaceHolder": "Enter max logs (in seconds)", "status": "Status", "actions": "Actions", "connected": "Connected", "disconnected": "Disconnected", "disabled": "Disabled", "enabled": "Enabled", "createSuccess": "Node «{{name}}» has been created successfully", "createFailed": "Failed to create node «{{name}}»", "editSuccess": "Node «{{name}}» has been modified successfully", "editFailed": "Failed to modify node «{{name}}»", "deleteSuccess": "Node «{{name}}» has been removed successfully", "deleteFailed": "Failed to remove node «{{name}}»", "enableSuccess": "Node «{{name}}» has been enabled successfully", "enableFailed": "Failed to enable node «{{name}}»", "disableSuccess": "Node «{{name}}» has been disabled successfully", "disableFailed": "Failed to disable node «{{name}}»", "certificate": "Certificate", "certificateCopied": "Certificate copied to clipboard", "certificateDownloaded": "Certificate downloaded successfully", "certificateDescription": "To set up the node, you need to configure this certificate to establish a secure connection between the main server and the node", "selectNode": "Select a node", "logs": {"title": "Logs", "description": "View and monitor node logs", "noLogs": "No logs available", "loading": "Loading logs...", "timestamps": "Timestamps", "autoScroll": "Auto Scroll", "scrollToEnd": "Scroll to End", "clear": "Clear Logs", "search": "Search logs", "filter": "Filter Logs", "levels": "Log Levels", "debug": "Debug", "info": "Info", "warning": "Warning", "error": "Error", "maxLogsTooltip": "Maximum logs to keep in memory", "memory": "Memory Usage", "custom": "Custom", "unlimited": "Unlimited", "memoryWarning": "High values may cause browser performance issues", "setCustom": "Set Custom Limit", "customLimit": "Enter custom limit", "apply": "Apply"}}, "groups": "Groups", "documentation": "Documentation", "discussionGroup": "Discussion Group", "github": "GitHub", "community": "Community", "supportUs": "Support Us", "manageHosts": "Manage and control hosts.", "manageSettings": "Manage and control settings.", "settings": {"title": "Settings", "notifications": {"title": "Notifications", "description": "Configure notification settings and manage alert preferences for your system", "loadError": "Failed to load notification settings. Please try again.", "saveSuccess": "Notification settings saved successfully", "saveFailed": "Failed to save notification settings", "cancelSuccess": "Changes cancelled and original settings restored", "filterTitle": "Filter Notifications", "filterDescription": "Select events that should trigger notifications for important system activities", "types": {"admin": "Admin", "core": "Core", "group": "Groups", "host": "Hosts", "login": "<PERSON><PERSON>", "node": "Nodes", "user": "Users", "userTemplate": "Templates", "daysLeft": "Expiration", "percentageReached": "Usage"}, "telegram": {"title": "Telegram", "description": "Configure Telegram notifications for important system activities", "apiToken": "Bot API Token", "adminId": "Admin Chat ID", "channelId": "Channel ID", "topicId": "Topic ID"}, "discord": {"title": "Discord", "description": "Configure Discord notifications for important system activities", "webhookUrl": "Webhook URL"}, "advanced": {"title": "Advanced Settings", "description": "Configure advanced notification settings, including retry policies and proxy configurations", "maxRetries": "Max Retries", "proxyUrl": "Proxy URL"}}, "subscriptions": {"title": "Subscriptions", "description": "Configure subscription URLs, update intervals, and client-specific rules", "loadError": "Failed to load subscription settings", "saveSuccess": "Subscription settings saved successfully", "saveFailed": "Failed to save subscription settings", "cancelSuccess": "Changes cancelled and original settings restored", "general": {"title": "General Settings", "description": "Basic subscription configuration options", "urlPrefix": "URL Prefix", "urlPrefixPlaceholder": "sub", "urlPrefixDescription": "Base URL for subscription links", "updateInterval": "Update Interval (hours)", "updateIntervalDescription": "How often clients should check for updates", "supportUrl": "Support URL", "supportUrlPlaceholder": "https://support.example.com", "supportUrlDescription": "URL for user support and help", "profileTitle": "Profile Title", "profileTitlePlaceholder": "My VPN Service", "profileTitleDescription": "Display name for the subscription profile", "hostStatusFilter": "Host Status Filter", "hostStatusFilterDescription": "Filter hosts based on their status"}, "rules": {"title": "Subscription Rules", "description": "Configure client-specific subscription rules and formats", "addRule": "Add Rule", "removeRule": "Remove Rule", "pattern": "Pattern", "patternPlaceholder": "Enter pattern (e.g., user-agent pattern)", "patternDescription": "Pattern to match against client requests", "target": "Target Format", "targetDescription": "Configuration format to serve for this pattern", "noRules": "No rules configured. Add rules to customize subscription behavior for different clients."}, "formats": {"title": "Manual Subscription Formats", "description": "Enable or disable specific subscription formats for manual requests", "links": "Links", "linksDescription": "Plain text subscription links", "linksBase64": "Links (Base64)", "linksBase64Description": "Base64 encoded subscription links", "xray": "Xray", "xrayDescription": "Xray configuration format", "singBox": "Sing-box", "singBoxDescription": "Sing-box configuration format", "clash": "Clash", "clashDescription": "Clash configuration format", "clashMeta": "<PERSON><PERSON>", "clashMetaDescription": "Clash Meta configuration format", "outline": "Outline", "outlineDescription": "Outline VPN configuration format"}, "configFormats": {"links": "Links", "links_base64": "Links (Base64)", "xray": "Xray", "sing_box": "Sing-box", "clash": "Clash", "clash_meta": "<PERSON><PERSON>", "outline": "Outline", "block": "Block"}}, "telegram": {"title": "Telegram", "description": "Configure Telegram bot integration and related settings for your system", "loadError": "Failed to load Telegram settings. Please try again.", "saveSuccess": "Telegram settings saved successfully", "saveFailed": "Failed to save Telegram settings", "cancelSuccess": "Changes cancelled and original Telegram settings restored", "general": {"title": "General Settings", "description": "Basic Telegram bot configuration and connection settings", "enable": "Enable Telegram <PERSON>", "enableDescription": "Enable or disable Telegram bot functionality for your system", "token": "Bot API Token", "tokenPlaceholder": "Enter your Telegram bot token", "tokenDescription": "Bot token obtained from @BotFather on Telegram", "webhookUrl": "Webhook URL", "webhookUrlPlaceholder": "https://your-domain.com/webhook", "webhookUrlDescription": "URL where Telegram will send updates", "webhookSecret": "Webhook Secret", "webhookSecretPlaceholder": "Enter webhook secret", "webhookSecretDescription": "Secret token for webhook security", "proxyUrl": "Proxy URL", "proxyUrlPlaceholder": "socks5://proxy.example.com:1080", "proxyUrlDescription": "Proxy URL for Telegram API connections (optional)"}, "advanced": {"title": "Advanced Settings", "description": "Advanced Telegram bot features and configurations", "miniAppLogin": "Enable Mini App Login", "miniAppLoginDescription": "Allow users to log in through Telegram mini apps"}}, "discord": {"title": "Discord", "description": "Configure Discord bot integration and related settings for your system", "loadError": "Failed to load Discord settings. Please try again.", "saveSuccess": "Discord settings saved successfully", "saveFailed": "Failed to save Discord settings", "cancelSuccess": "Changes cancelled and original Discord settings restored", "general": {"title": "General Settings", "description": "Basic Discord bot configuration and connection settings", "enable": "Enable <PERSON><PERSON>", "enableDescription": "Enable or disable Discord bot functionality for your system", "token": "Bot <PERSON>", "tokenPlaceholder": "Enter your Discord bot token", "tokenDescription": "Bot token obtained from Discord Developer Portal", "proxyUrl": "Proxy URL", "proxyUrlPlaceholder": "socks5://proxy.example.com:1080", "proxyUrlDescription": "Proxy URL for Discord API connections (optional)"}}, "webhook": {"title": "Webhook", "description": "Configure webhook notifications and endpoint settings for your system", "loadError": "Failed to load webhook settings. Please try again.", "saveSuccess": "Webhook settings saved successfully", "saveFailed": "Failed to save webhook settings", "cancelSuccess": "Changes cancelled and original webhook settings restored", "general": {"title": "General Settings", "description": "Basic webhook configuration and connection settings", "enable": "Enable Webhooks", "enableDescription": "Enable or disable webhook notifications for your system", "timeout": "Timeout (seconds)", "timeoutDescription": "Request timeout for webhook calls (1-300 seconds)", "recurrent": "Retry Attempts", "recurrentDescription": "Number of retry attempts for failed webhooks (1-24)", "proxyUrl": "Proxy URL", "proxyUrlPlaceholder": "http://proxy.example.com:8080", "proxyUrlDescription": "Proxy URL for webhook requests (optional)"}, "webhooks": {"title": "Webhook Endpoints", "description": "Configure webhook URLs and authentication", "add": "Add Webhook", "addFirst": "Add First Webhook", "webhook": "Webhook", "empty": "No webhooks configured. Add your first webhook endpoint.", "name": "Name", "namePlaceholder": "My Webhook", "url": "URL", "secret": "Secret", "secretPlaceholder": "Enter webhook secret"}, "triggers": {"daysLeft": {"title": "Days Left Notifications", "description": "Notify when user accounts have specific days remaining", "empty": "No triggers set"}, "usagePercent": {"title": "Usage Percentage Notifications", "description": "Notify when users reach specific usage percentages", "empty": "No triggers set"}}}, "cleanup": {"title": "Cleanup", "description": "Manage expired accounts and reset data usage for system maintenance", "loadError": "Failed to load cleanup settings. Please try again.", "saveSuccess": "Cleanup operation completed successfully", "saveFailed": "Failed to complete cleanup operation", "cancelSuccess": "Cleanup operation cancelled", "expiredUsers": {"title": "Delete Expired Accounts", "description": "Remove accounts that have expired within a specific date range", "expiredAfter": "Expired After", "expiredBefore": "Expired Before", "expiredAfterPlaceholder": "Select start date", "expiredBeforePlaceholder": "Select end date", "dateRange": "Date Range", "deleteExpired": "Delete Expired", "deleting": "Deleting...", "deleteSuccess": "{{count}} expired accounts deleted successfully", "deleteFailed": "Failed to delete expired accounts", "confirmDelete": "Delete Expired Accounts", "confirmDeleteMessage": "Are you sure you want to delete all expired accounts in the selected date range? This action cannot be undone.", "noDateSelected": "Please select at least one date", "selectDateRange": "Select the date range for expired accounts to delete (only past dates are selectable)"}, "resetUsage": {"title": "Reset All Data Usage", "description": "Reset data usage for all accounts in the system", "resetAll": "Reset All Usage", "resetting": "Resetting...", "resetSuccess": "All data usage reset successfully", "resetFailed": "Failed to reset data usage", "confirmReset": "Reset All Data Usage", "confirmResetMessage": "Are you sure you want to reset data usage for all accounts? This action cannot be undone.", "warning": "This will reset data usage for ALL accounts in the system"}, "clearUsageData": {"title": "Clear Usage Data", "description": "Permanently delete usage data from selected database tables", "selectTable": "Select Table", "selectTablePlaceholder": "Choose a table to clear", "noTableSelected": "Please select a table to clear", "dataAfter": "Data After", "dataBefore": "Data Before", "dataAfterPlaceholder": "Select start date", "dataBeforePlaceholder": "Select end date", "selectDateRange": "Select date range to filter data by creation time (optional - leave blank to clear all data)", "clearData": "Clear Data", "clearing": "Clearing...", "clearSuccess": "Successfully cleared data from {{table}}", "clearFailed": "Failed to clear usage data", "confirmClear": "Confirm Clear Data", "confirmClearMessage": "Are you sure you want to permanently delete all data from the {{table}} table? This action cannot be undone.", "warning": "Warning: This will permanently delete all usage data from the selected table. This action cannot be undone.", "tables": {"nodeUserUsages": "Node User Usages", "userUsages": "User Usages"}}}, "theme": {"title": "Theme", "description": "Customize your application theme", "light": "Light", "dark": "Dark", "system": "System", "selectTheme": "Select theme", "themeSaved": "Theme saved successfully", "themeChanged": "Theme changed successfully", "visitThemePage": "Visit theme page to customize further", "radiusSaved": "Border radius updated successfully", "mode": "Mode", "color": "Color", "radius": "Border Radius", "zinc": "Zinc", "rose": "<PERSON>", "blue": "Blue", "green": "Green", "violet": "Violet", "orange": "Orange", "yellow": "Yellow", "default": "<PERSON><PERSON><PERSON>", "red": "Red", "modeDescription": "Choose how the interface should appear", "colorDescription": "Select your preferred color scheme", "radiusDescription": "Adjust the roundness of UI elements", "lightDescription": "Bright and clean", "darkDescription": "Easy on the eyes", "systemDescription": "Matches device", "preview": "Live Preview", "previewDescription": "See how your theme settings look in real-time", "dashboardPreview": "Dashboard Preview", "dashboardDescription": "Monitor system performance and manage user accounts", "currentTheme": "Current theme", "sampleInput": "Sample input", "primaryButton": "Primary Button", "resetToDefaults": "Reset to defaults", "resetDescription": "Restore all theme settings to their default values", "resetting": "Resetting...", "reset": "Reset", "resetSuccess": "Theme reset to default settings", "resetFailed": "Failed to reset theme settings", "radiusNone": "None", "radiusSmall": "Small", "radiusMedium": "Medium", "radiusLarge": "Large"}}, "saving": "Saving...", "general": "General", "core": "Core Configs", "activeUsers": "Active Users", "apply": "Apply", "cancel": "Cancel", "created": "Created", "by": "by", "ip": "IP", "admin": "Admin", "sudo": "<PERSON><PERSON>", "save": "Save", "test": "Test", "testing": "Testing", "confirm": "Confirm", "admins": {"title": "Admins", "description": "Manage system administrators", "createAdmin": "Create Admin", "editAdmin": "Modify Admin", "deleteAdmin": "Remove <PERSON>", "username": "Username", "password": "Password", "isSudo": "Super Admin", "createdAt": "Created At", "actions": "Actions", "createSuccess": "Admin «{{name}}» has been created successfully", "createFailed": "Failed to create admin «{{name}}»", "editSuccess": "Admin «{{name}}» has been modified successfully", "editFailed": "Failed to modify admin «{{name}}»", "deleteSuccess": "Admin {{name}} removed successfully", "deleteFailed": "Failed to remove admin {{name}}", "enterUsername": "Enter username", "enterPassword": "Enter password", "sudo": "<PERSON><PERSON> Privileges", "edit": "Save Changes", "create": "Create Admin", "status": "Status", "role": "Role", "total.users": "Total Users", "used.traffic": "Used Traffic", "total": "Total Admins", "active": "Active Admins", "disable": "Disabled <PERSON><PERSON>", "telegramId": "Telegram ID", "discord": "Discord Webhook", "supportUrl": "Support URL", "subDomain": "Subscription Domain", "profile": "Profile Title", "subTemplate": "Subscription Template Path", "passwordConfirm": "Confirm Password", "enterPasswordConfirm": "Enter Confirm Password", "disableSuccess": "Admin «{{name}}» has been disabled successfully", "enableSuccess": "Admin «{{name}}» has been enabled successfully", "disableFailed": "Failed to disable admin «{{name}}»", "enableFailed": "Failed to enable admin «{{name}}»", "resetUsersUsage": "Admin Reset Users Usage", "resetUsageSuccess": "Admin «{{name}}» user usage has been reset successfully", "resetUsageFailed": "Failed to reset admin «{{name}}» user usage", "reset": "Reset usage", "discordId": "Discord ID", "lifetime.used.traffic": "Total", "used": {"traffic": "Traffic Usage"}, "monitor": {"traffic": "Monitor admin traffic usage over time", "no_traffic": "No traffic data available"}}, "shortcuts": {"title": "Keyboard Shortcuts", "description": "Manage your dashboard shortcuts for quick access", "add": "Add Shortcut", "empty": "No shortcuts configured"}, "quickActions": {"title": "Quick Actions", "description": "Choose an action to create new items in the system", "comingSoon": "Coming Soon"}, "resetUsersUsage.prompt": "Are you sure you want to reset the <b>{{name}}</b> admin users usage?", "admin.disable": "Disabled Ad<PERSON>", "admin.enable": "Enabled Admin", "deleteAdmin.prompt": "Are you sure you want to remove the <b>{{name}}</b> admin?", "activeUsers.prompt": "Do you want to activate all users under this {{name}}?", "disableUsers.prompt": "Do you want to disable all users under this {{name}}?", "manageAccounts": "Control, Update, and Arrange User Accounts", "manageGroups": "Control, Update, and Arrange User Groups", "manageNodes": "Monitor, control, create, modify, remove nodes", "onlineUsers": "Online Users", "totalUsers": "Total Users", "edit": "Modify", "editGroup": "Modify Group", "disable": "Disable", "warning": "Warning", "remove": "Remove", "modify": "Modify", "templates.userTemplates": "Templates", "editUserTemplateModal.title": "Modify User Template", "userTemplateModal.title": "Create User Template", "templates": {"title": "Templates", "description": "Manage your Temp<PERSON>.", "addTemplate": "Create Template", "editSuccess": "User Template «{{name}}» has been modified successfully", "createSuccess": "User Templates «{{name}}» has been created successfully", "editFailed": "Failed to modify User Template «{{name}}»", "createFailed": "Failed to create User Template «{{name}}»", "name": "Name", "status": "Status", "prefix": "<PERSON><PERSON><PERSON>", "suffix": "<PERSON>rname Suffix", "dataLimit": "Data Limit", "expire": "Expire duration", "onHoldTimeout": "OnHold Timeout", "method": "Method", "flow": "Flow", "groups": "Groups", "userDataLimitStrategy": "Data Limit Reset Strategy", "resetUsage": "Reset Usage", "groupsExistingWarning": "You dont create any <a>Groups</a> yet.", "deleteSuccess": "Template {{name}} removed successfully", "deleteFailed": "Failed to remove template {{name}}", "deleteUserTemplateTitle": "Remove User Template", "deleteUserTemplatePrompt": "Are you sure you want to remove the <b>{{name}}</b> template?", "duplicateSuccess": "Template «{{name}}» duplicated successfully", "duplicateFailed": "Failed to duplicate template «{{name}}»", "enableSuccess": "Template «{{name}}» has been enabled successfully", "disableSuccess": "Template «{{name}}» has been disabled successfully", "enableFailed": "Failed to enable admin «{{name}}»", "disableFailed": "Failed to disable admin «{{name}}»"}, "core.configuration": "Configuration", "core.generalErrorMessage": "Something went wrong, please check the configuration", "core.logs": "Logs", "core.restartCore": "Restart Core", "core.restarting": "Restarting...", "core.save": "Save", "core.socket.closed": "Closed", "core.socket.connected": "Connected", "core.socket.connecting": "Connecting...", "core.socket.not_connected": "Not Connected", "core.successMessage": "Core settings updated successfully", "core.title": "Core Configs Settings", "core.toggleSuccess": "Core «{name}» has been toggled successfully", "core.toggleFailed": "Failed to toggle core «{name}»", "core.deleteSuccess": "Core «{name}» has been removed successfully", "core.deleteFailed": "Failed to remove core «{name}»", "core.deleteConfirm": "Are you sure you want to remove {{name}}?", "createNewUser": "Create new user", "createUser": "Create User", "createGroup": "Create Group", "dataUsage": "data usage", "dateFormat": "MMMM d, yyy", "delete": "Remove", "deleteNode.deleteSuccess": "Node {{name}} removed successfully", "deleteNode.prompt": "Are you sure you want to remove the {{name}} node?", "deleteNode.title": "Remove Node", "deleteHost.title": "Remove Host", "deleteHost.prompt": "Are you sure you want to remove the {{name}} host?", "deleteHost.deleteSuccess": "Host {{name}} removed successfully", "deleteHost.deleteFailed": "Failed to remove host {{name}}", "editHost.title": "Modify Host", "editNode.editSuccess": "Node {{name}} has been modified successfully", "editNode.title": "Modify Node", "deleteUser.deleteSuccess": "{{username}} removed successfully.", "deleteUser.prompt": "Are you sure you want to remove {{username}}?", "deleteUser.title": "Remove User", "disabled": "disabled", "expire": "Expire", "expires": "Expires in {{time}}", "expired": "Expired in {{time}}", "header.donation": "Donation", "header.hostSettings": "Host Settings", "header.logout": "Log out", "header.nodeSettings": "Node Settings", "header.nodesUsage": "Nodes Usage", "loading": "Loading...", "noResults": "No results found", "emptyState": {"noUsers": {"title": "No users yet", "description": "Get started by creating your first user account", "createFirstUser": "Create your first user"}}, "hostsDialog": {"addHost": "Create Host", "selectInbound": "Select an inbound", "selectStatus": "Select Status", "remarkRequired": "Remark is required", "addressRequired": "Address is required", "portRequired": "Port is required", "fingerprint": "Fingerprint", "status": {"label": "Status", "active": "Active", "disabled": "Disabled", "limited": "Limited", "expired": "Expired", "onHold": "On Hold"}, "port": "Port", "sni": "SNI", "sniPlaceholder": "SNI (e.g. example.com)", "host": "Host", "path": "Path", "networkSettings": "Network Settings", "transportSettings": "Transport Settings", "transportType": "Transport Type", "selectTransport": "Select transport type", "serviceName": "Service Name", "enterServiceName": "Enter gRPC service name", "seed": "Seed", "enterSeed": "Enter KCP seed", "enterPath": "Enter path (e.g. /xray)", "enterHost": "Enter host (e.g. example.com)", "header": "Header", "selectHeader": "Select header type", "multiMode": "Multi Mode", "securitySettings": "Security Settings", "camouflagSettings": "Camouflag Settings", "security": "Security", "alpn": "ALPN", "earlyData": "Early Data", "allowInsecure": "Allow Insecure", "muxEnable": "Enable Mux", "randomUserAgent": "Random User Agent", "inboundDefault": "Inbound Default", "httpHeaders": "HTTP Headers", "headersName": "Headers Name", "headersValue": "Headers Value", "addHeader": "<PERSON>d <PERSON>", "removeHeader": "<PERSON><PERSON><PERSON>", "createSuccess": "Host «{{name}}» has been created successfully", "createFailed": "Failed to create host «{{name}}»", "editSuccess": "Host «{{name}}» has been modified successfully", "editFailed": "Failed to modify host «{{name}}»", "noStatus": "No status selected", "clearAllStatuses": "Clear all statuses", "transportSettingsAccordion": "Transport Settings", "xhttp": {"mode": "Mode", "noGrpcHeader": "No gRPC Header", "xPaddingBytes": "X-Padding Bytes", "scMaxEachPostBytes": "Max Each Post Bytes", "scMinPostsIntervalMs": "Min Posts Interval (ms)", "scMaxBufferedPosts": "<PERSON> Buffered Posts", "scStreamUpServerSecs": "Stream Up Server (secs)", "xmux": "XMux Settings", "maxConcurrency": "<PERSON> Concurrency", "maxConnections": "Max Connections", "cMaxReuseTimes": "Max Reuse Times", "cMaxLifetime": "Max <PERSON>", "hMaxRequestTimes": "Max Request Times", "hKeepAlivePeriod": "Keep Alive Period", "downloadSettings": "Download Settings", "downloadSettingsInfo": "Select a host to use for download settings", "selectDownloadSettings": "Select download settings host"}, "grpc": {"multiMode": "Multi Mode", "idleTimeout": "Idle Timeout", "healthCheckTimeout": "Health Check Timeout", "permitWithoutStream": "Permit Without Stream", "initialWindowsSize": "Initial Windows Size"}, "kcp": {"header": "Header", "mtu": "MTU", "tti": "TTI", "uplinkCapacity": "Uplink Capacity", "downlinkCapacity": "Downlink Capacity", "congestion": "Congestion", "readBufferSize": "<PERSON> Buffer <PERSON>", "writeBufferSize": "Write Buffer <PERSON>"}, "tcp": {"title": "TCP Settings", "header": "Header Type", "request": {"title": "Request Settings", "version": "HTTP Version", "method": "HTTP Method", "headers": "Request Headers"}, "response": {"title": "Response Settings", "version": "HTTP Version", "status": "Status Code", "reason": "Status Reason", "headers": "Response Headers"}, "headerName": "Header Name", "headerValue": "Header Value", "addHeader": "<PERSON>d <PERSON>", "removeHeader": "<PERSON><PERSON><PERSON>", "requestHeaders": "Request Headers", "responseHeaders": "Response Headers"}, "websocket": {"heartbeatPeriod": "Heartbeat Period"}, "fragment": {"title": "Fragment", "packets": "Packets", "packetsPlaceholder": "Enter packets", "length": "Length", "lengthPlaceholder": "Enter length", "interval": "Interval", "intervalPlaceholder": "Enter interval", "allFieldsRequired": "If one field is entered, all fields must be filled", "info": "length,interval,packet (e.g. 10-100,100-200,tlshello)", "info.attention": "Attention: currently, this feature only supported in streisand >= 1.6.12 and v2rayNG >= 1.8.16 (custom config)", "info.examples": "Examples:"}, "noise": {"title": "Noise", "addNoise": "Add Noise", "removeNoise": "Remove Noise", "packet": "Packet", "packetPlaceholder": "Enter packet value (e.g. rand:10-20)", "delay": "Delay", "delayPlaceholder": "Enter delay", "rand": "Random", "info": "packet,delay (e.g. rand:10-20,100-200)", "info.attention": "Attention: currently, this feature only supported in streisand >= 1.6.32 and v2rayNG >= 1.8.39 (custom config)", "info.examples": "Examples:"}, "muxSettings": "<PERSON><PERSON>", "enableMux": "Enable Mux", "xraySettings": "<PERSON>ray Settings", "singBoxSettings": "Sing-box Settings", "clashSettings": "<PERSON><PERSON> Settings", "addXraySettings": "Add Xray Settings", "addSingBoxSetting": "Add Sing-box Settings", "addClashSettings": "Add <PERSON>lash Settings", "protocol": "Protocol", "selectProtocol": "Select protocol", "maxConnections": "Max Connections", "maxStreams": "Max Streams", "minStreams": "Min Streams", "padding": "Enable Padding", "enabled": "Enabled", "concurrency": "Concurrency", "xudpConcurrency": "XUDP Concurrency", "xudpProxy443": "XUDP Proxy 443", "selectXudpProxy443": "Select XUDP Proxy 443", "statistic": "Enable Statistics", "onlyTcp": "Only TCP", "brutal": {"enable": "Enable <PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> Settings", "upMbps": "Upload Speed (Mbps)", "downMbps": "Download Speed (Mbps)"}, "address": "Address", "variables": {"title": "Use these variables to make it dynamic", "server_ip": "IP Address of current server", "server_ipv6": "IPv6 of current server", "username": "The username of the user", "data_usage": "The current data usage of the user", "data_left": "Remaining data of the user", "data_limit": "The usage limit of the user", "days_left": "Remaining days of the user", "expire_date": "Expiry date of the user", "jalali_expire_date": "Expiry date of the user in solar calendar", "time_left": "Remaining time of the user", "status_text": "User status", "status_emoji": "User status as an emoji (✅,⌛️,🪫,❌,🔌)", "protocol": "Proxy protocol (e.g. VMess)", "transport": "Proxy transport method (e.g. ws)"}, "advancedOptions": "Advanced Options", "hos.info": "By default, if a request host is set in the XRAY configuration, it will be used. However, if needed, you can set a custom request host here.", "multiHost": "To set multiple addresses, separate them with <badge>,</badge>. A random address will be selected each time.", "wildcard": "Use <badge>*</badge> to generate a random string (works for wildcard domains)", "path.info": "Set a path for host users, useful behind a reverse proxy.", "port.nfo": "By default, the host uses the port declared in the inbound. You can set a custom port if traffic is redirected from a different port. For example, the server might redirect traffic from port 443 to your inbound's default port.", "proxyOutbound": {"info": "Additional outbound traffic (only in v2ray custom configuration)"}, "security.info": "If this host's middleware server uses a different security layer than your inbound default, you can set a custom security layer here.", "port.info": "By default, the host uses the port declared in the inbound. You can set a custom port if traffic is redirected from a different port. For example, the server might redirect traffic from port 443 to your inbound's default port.", "host.info": "By default, if a request host is set in the XRAY configuration, it will be used. However, if needed, you can set a custom request host here.", "host.multiHost": "To set multiple addresses, separate them with <badge>,</badge>. A random address will be selected each time.", "host.wildcard": "Use <badge>*</badge> to generate a random string (works for wildcard domains)", "sni.info": "SNI (Server Name Indication) is used to specify which hostname the client is trying to connect to. This is particularly useful when you have multiple domains pointing to the same IP address.", "useSniAsHost": "Use S<PERSON> as Host", "httpVersions": {"1.0": "HTTP/1.0", "1.1": "HTTP/1.1", "2.0": "HTTP/2.0", "3.0": "HTTP/3.0"}, "httpMethods": {"GET": "GET", "POST": "POST", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD", "OPTIONS": "OPTIONS", "PATCH": "PATCH", "TRACE": "TRACE", "CONNECT": "CONNECT"}, "httpReasons": {"100": "Continue", "101": "Switching Protocols", "200": "OK", "201": "Created", "202": "Accepted", "203": "Non-Authoritative Information", "204": "No Content", "205": "Reset Content", "206": "Partial Content", "300": "Multiple Choices", "301": "Moved Permanently", "302": "Found", "303": "See Other", "304": "Not Modified", "305": "Use Proxy", "307": "Temporary Redirect", "308": "Permanent Redirect", "400": "Bad Request", "401": "Unauthorized", "402": "Payment Required", "403": "Forbidden", "404": "Not Found", "405": "Method Not Allowed", "406": "Not Acceptable", "407": "Proxy Authentication Required", "408": "Request Timeout", "409": "Conflict", "410": "Gone", "411": "Length Required", "412": "Precondition Failed", "413": "Payload Too Large", "414": "URI Too Long", "415": "Unsupported Media Type", "416": "Range Not Satisfiable", "417": "Expectation Failed", "418": "I'm a teapot", "421": "Misdirected Request", "422": "Unprocessable Entity", "423": "Locked", "424": "Failed Dependency", "425": "Too Early", "426": "Upgrade Required", "428": "Precondition Required", "429": "Too Many Requests", "431": "Request Header Fields Too Large", "451": "Unavailable For Legal Reasons", "500": "Internal Server Error", "501": "Not Implemented", "502": "Bad Gateway", "503": "Service Unavailable", "504": "Gateway Timeout", "505": "HTTP Version Not Supported"}, "selectReason": "Select Reason", "nextPlanTitle": "Next Plan", "nextPlanTemplateId": "Template ID", "nextPlanDataLimit": "Data Limit (GB)", "nextPlanExpire": "Expire (days)", "nextPlanAddRemainingTraffic": "Add Remaining Traffic"}, "inbound": "Inbound", "remark": "Host Name", "none": "None", "create": "Create", "itemsPerPage": "Items per page", "login": "<PERSON><PERSON>", "login.fieldRequired": "This field is required", "login.loginYourAccount": "Login to your account", "login.welcomeBack": "Welcome back, please enter your details", "memoryUsage": "memory usage", "next": "Next", "monitorServers": "Monitor your servers and users", "manageServers": "Control your server details", "nodes.addHostForEveryInbound": "Add this node as a new host for every inbound", "nodes.addNewMarzbanNode": "Create New Marzban Node", "nodes.prompt": "Create and configure node details", "nodes.addNodeSuccess": "Node {{name}} created successfully", "nodes.apply": "modifyNode", "nodes.certificate": "Certificate", "nodes.editNode": "Modify Node", "nodes.nodeAPIPort": "API Port", "nodes.nodeName": "Name", "nodes.nodePort": "Port", "nodes.reconnect": "Reconnect", "nodes.reconnecting": "Reconnecting...", "nodes.title": "Using Marzban-Node, you are able to scale up your connection quality by creating different nodes on different servers.", "nodes.usageCoefficient": "Usage Ratio", "on_hold": "On Hold", "duplicate": "Duplicate", "createAndManageTemplates": "Create and manage templates", "password": "Password", "previous": "Previous", "qrcodeDialog.sublink": "Subscribe Link", "reset": "Reset", "resetAllUsage": "Reset All Usages", "resetAllUsage.error": "Usage reset failed, please try again.", "resetAllUsage.prompt": "This action clears all of the users' data usage completely. Are you sure you want to reset all usage? THIS CANNOT BE UNDONE!", "resetAllUsage.success": "All usage has reset successfully.", "resetAllUsage.title": "Reset data usage for all users", "resetUserUsage.error": "Usage reset failed, please try again.", "resetUserUsage.prompt": "Are you sure you want to reset <b>{{username}}</b>'s usage?", "resetUserUsage.success": "{{username}}'s usage has reset successfully.", "resetUserUsage.title": "Reset User Usage", "revoke": "Revoke", "revokeUserSub.error": "Subscription revoke failed, please try again.", "revokeUserSub.prompt": "Are you sure you want to revoke «{{username}}»'s subscription?", "revokeUserSub.success": "{{username}}'s subscription has revoked successfully.", "revokeUserSub.title": "Revoke User Subscription", "search": "Search", "status.active": "Active", "status.enable": "Enable", "status.disabled": "Disabled", "status.expired": "Expired", "status.limited": "Limited", "status.on_hold": "On Hold", "nodeModal.status.error": "Error", "nodeModal.status.disabled": "Disabled", "nodeModal.status.connecting": "Connecting", "nodeModal.status.connected": "Connected", "userDialog": {"absolute": "Absolute", "custom": "Custom", "dataLimit": "Data Limit", "days": "Days", "editUser": "Modify user", "editUserTitle": "Modify user", "endDate": "End date", "expiryDate": "Expiry Date", "timeOutDate": "Timeout Date", "generatedByDefault": "Automatically generated by default", "hours": "Hours", "method": "Method", "months": "Months", "note": "Note", "onHold": "On Hold", "onHoldExpireDuration": "On-Hold Expire Duration", "onHoldExpireDurationPlaceholder": "e.g. 7", "optional": "optional", "periodicUsageReset": "Periodic Usage Reset", "protocols": "Protocols", "relative": "Relative", "resetStrategyAnnually": "Annually", "resetStrategyDaily": "Daily", "resetStrategyMonthly": "Monthly", "resetStrategyNo": "No", "resetStrategyWeekly": "Weekly", "resetUsage": "Reset Usage", "revokeSubscription": "Revoke Subscription", "selectOneProtocol": "Please select at least one protocol", "shadowsocksDesc": "Fast and secure, but not efficient as others", "startDate": "Start date", "total": "Total: ", "trojanDesc": "Lightweight, secure and lightening fast", "usage": "Usage", "userAlreadyExists": "User already exists", "userCreated": "User {{username}} created.", "userEdited": "User {{username}} Modified.", "vlessDesc": "Lightweight, fast and secure", "vmessDesc": "Fast and secure", "warningNoProtocol": "Please select at least one protocol", "weeks": "Weeks", "username": "Username", "deleteSuccess": "User «{{name}}» has been removed successfully.", "resetUsageSuccess": "Usage for user «{{name}}» has been reset.", "revokeSubSuccess": "Subscription for user «{{name}}» has been revoked.", "activeNextPlanSuccess": "Next plan for user «{{name}}» has been activated.", "activeNextPlanError": "failed to activate next plan for user «{{name}}».", "deleteConfirmTitle": "Remove User", "deleteConfirm": "Are you sure you want to remove user «{{name}}»?", "nextPlanTitle": "Next Plan", "nextPlanTemplateId": "Template ID", "nextPlanDataLimit": "Data Limit (GB)", "nextPlanExpire": "Expire (days)", "nextPlanAddRemainingTraffic": "Add Remaining Traffic", "selectTemplate": "Select Template", "selectTemplatePlaceholder": "Choose a template", "proxySettingsAccordion": "Proxy Settings", "proxySettings.vmess": "VMess", "proxySettings.vless": "VLESS", "proxySettings.trojan": "Trojan", "proxySettings.shadowsocks": "Shadowsocks", "proxySettings.id": "ID", "proxySettings.password": "Password", "proxySettings.method": "Method", "proxySettings.flow": "Flow", "proxySettings.desc": "Configure protocol-specific settings for this user.", "selectNode": "Select Node", "allNodes": "All Nodes", "activeNextPlan": "Active Next Plan", "status": "Status", "dataLimitResetStrategy": "Data Limit Reset Strategy", "expire": "Expire Date", "onHoldTimeout": "On-Hold Timeout", "autoDeleteInDays": "Auto Delete In Days", "proxySettings": "Proxy Settings", "nextPlan": "Next Plan", "groups": "Groups", "subscriptionInfo": "Subscription Info", "subscriptionUpdated": "Last Updated", "lastClient": "Last Client", "subscriptionNotAccessed": "Not accessed yet", "unknownClient": "Unknown client", "selectedGroups": "{{count}} groups selected", "editError": "Failed to update user «{{name}}»", "createError": "Failed to create user «{{name}}»", "selectStatus": "Select status", "selectedTemplates": "{{count}} template(s) selected"}, "users": "Users", "usersTable.copied": "<PERSON>pied", "usersTable.copyConfigs": "Copy Configs", "usersTable.copyLink": "Copy Subscription Link", "usersTable.dataUsage": "data usage", "usersTable.noUser": "There is no user Created to the system", "usersTable.noUserMatched": "It seems there is no user matched with what you are looking for", "usersTable.status": "Status", "usersTable.total": "Total", "statistics.system": "System", "statistics.totalTraffic": "Total Traffic", "statistics.ramUsage": "RAM Usage", "statistics.cpuUsage": "CPU Usage", "statistics.cores": "cores", "statistics.realTimeData": "Real-Time Data", "statistics.realtimeDescription": "Live statistics and charts for your servers and users.", "enable": "Enable", "host": {"enableSuccess": "Host «{{name}}» has been enabled successfully", "enableFailed": "Failed to enable host «{{name}}»", "disableSuccess": "Host «{{name}}» has been disabled successfully", "disableFailed": "Failed to disable host «{{name}}»", "duplicateSuccess": "Host «{{name}}» has been duplicated successfully", "duplicateFailed": "Failed to duplicate host «{{name}}»", "xudp_proxy_443": "XUDP Proxy 443", "reject": "Reject", "allow": "Allow", "skip": "<PERSON><PERSON>"}, "usersTable.sortByExpire": "Sort by expiry time", "dateInfo.day": " day", "dateInfo.hour": " hour", "dateInfo.min": " minute", "dateInfo.month": " month", "dateInfo.year": " year", "group": {"createSuccess": "Group «{{name}}» has been created successfully", "createFailed": "Failed to create group «{{name}}»", "editSuccess": "Group «{{name}}» has been modified successfully", "editFailed": "Failed to modify group «{{name}}»", "deleteSuccess": "Group «{{name}}» has been removed successfully", "deleteFailed": "Failed to remove group «{{name}}»", "deleteConfirm": "Are you sure you want to remove group «{{name}}»?", "enableSuccess": "Group «{{name}}» has been enabled successfully", "enableFailed": "Failed to enable group «{{name}}»", "disableSuccess": "Group «{{name}}» has been disabled successfully", "disableFailed": "Failed to disable group «{{name}}»"}, "name": "Name", "inboundTags": "Inbound Tags", "searchInbounds": "Search inbounds...", "noInboundsFound": "No inbounds found", "nodeModal": {"title": "Create Node", "description": "Create a new node to your network", "name": "Node Name", "namePlaceholder": "Enter node name", "address": "Node Address", "addressPlaceholder": "Enter node address", "port": "Node Port", "portPlaceholder": "Enter node port", "usageRatio": "Usage Ratio", "usageRatioPlaceholder": "Enter usage ratio", "maxLogs": "<PERSON>", "maxLogsPlaceholder": "Enter max logs", "connectionType": "Connection Type", "keepAlive": "Keep Alive", "keepAliveDescription": "Set the keep alive interval", "days": "Days", "minutes": "Minutes", "seconds": "Seconds", "certificate": "Certificate", "certificatePlaceholder": "Enter certificate", "status": "Node Status", "statusCheckSuccess": "Node is connected successfully", "statusCheckFailed": "Failed to connect to node", "errorDetails": "<PERSON><PERSON><PERSON>", "connectionError": "Connection Error", "retryConnection": "Retry Connection", "configurationError": "Configuration Error", "validateConfig": "Validate Configuration", "fillRequiredFields": "Please fill out all required fields before checking status", "apiKey": "API Key", "apiKeyPlaceholder": "Enter the node's API Key", "generateUUID": "Generate UUID", "coreConfig": "Core Configuration", "hours": "Hours", "selectCoreConfig": "Select Core Configuration", "hideDetails": "Hide Details", "showDetails": "Show Details", "statusCheck": "Check Status", "statusChecking": "Checking...", "reconnect": "Reconnect", "reconnecting": "Reconnecting...", "sync": "Sync", "syncing": "Syncing...", "syncSuccess": "Node synchronized successfully", "syncFailed": "Failed to sync node", "gatherLogs": "Gather Logs", "gatherLogsDescription": "Enable log collection from this node for monitoring and debugging purposes", "statusMessages": {"checkUnavailableForNew": "Status check not available for new nodes. Create the node first to check its status."}, "onlineStats": {"button": "Online Stats", "title": "Online User Statistics", "ipListTitle": "IP Addresses for {{username}}", "nodeInfo": "Node: {{nodeName}}", "searchPlaceholder": "Enter username to search...", "enterUsername": "Please enter a username", "refreshed": "Data refreshed successfully", "refreshFailed": "Failed to refresh data", "backToStats": "Back to Stats", "ipAddresses": "IP addresses", "errorLoading": "Error loading user stats", "userNotOnline": "User is not online", "searchUser": "Search for a user to view their online stats", "autoRefresh": "Auto-refreshing every 5 seconds", "connections": "connections", "protocols": "protocols", "viewIPs": "View IPs", "userNotFound": "User '{{username}}' not found or not online", "errorLoadingIPs": "Error loading user IP addresses"}}, "theme": {"title": "Theme", "description": "Customize your application theme", "light": "Light", "dark": "Dark", "system": "System", "selectTheme": "Select theme", "themeSaved": "Theme saved successfully", "themeChanged": "Theme changed successfully", "visitThemePage": "Visit theme page to customize further", "radiusSaved": "Border radius updated successfully", "mode": "Mode", "color": "Color", "radius": "Border Radius", "zinc": "Zinc", "rose": "<PERSON>", "blue": "Blue", "green": "Green", "violet": "Violet", "orange": "Orange", "yellow": "Yellow", "default": "<PERSON><PERSON><PERSON>", "red": "Red", "modeDescription": "Choose how the interface should appear", "colorDescription": "Select your preferred color scheme", "radiusDescription": "Adjust the roundness of UI elements", "lightDescription": "Bright and clean", "darkDescription": "Easy on the eyes", "systemDescription": "Matches device", "preview": "Live Preview", "previewDescription": "See how your theme settings look in real-time", "dashboardPreview": "Dashboard Preview", "dashboardDescription": "Monitor system performance and manage user accounts", "currentTheme": "Current theme", "sampleInput": "Sample input", "primaryButton": "Primary Button", "resetToDefaults": "Reset to defaults", "resetDescription": "Restore all theme settings to their default values", "resetting": "Resetting...", "reset": "Reset", "resetSuccess": "Theme reset to default settings", "resetFailed": "Failed to reset theme settings", "radiusNone": "None", "radiusSmall": "Small", "radiusMedium": "Medium", "radiusLarge": "Large"}, "coreConfigModal": {"addConfig": "Create Core Configuration", "createNewConfig": "Create a new core configuration", "editCore": "Modify Core Configuration", "invalidJson": "Invalid JSON configuration", "createSuccess": "Core Configuration «{{name}}» has been created successfully", "createFailed": "Failed to create Core Configuration «{{name}}»", "editSuccess": "Core Configuration «{{name}}» has been modified successfully", "editFailed": "Failed to modify Core Configuration «{{name}}»", "keyPairGenerated": "Key pair generated successfully", "shortIdGenerated": "Short ID generated successfully", "shortId": "Short ID", "shortIdCopied": "Short ID copied to clipboard", "publicKeyCopied": "Public key copied to clipboard", "privateKeyCopied": "Private key copied to clipboard", "jsonConfig": "JSON Configuration", "editJson": "Modify JSON configuration", "name": "Name", "namePlaceholder": "Enter configuration name", "fallback": "Fallback", "selectFallback": "Select fallback", "excludedInbound": "Excluded inbound", "selectInbound": "Select inbound", "inbound": "Inbound", "generateKeyPair": "Generate Key Pair", "generateShortId": "Generate Short ID", "publicKey": "Public Key", "privateKey": "Private Key", "clearAllFallbacks": "Clear all fallbacks", "clearAllExcluded": "Clear all excluded inbounds", "copyPublicKey": "Copy Public Key", "copyPrivateKey": "Copy Private Key", "copyShortId": "Copy Short ID", "restartNodes": "<PERSON><PERSON>", "generateShadowsocksPassword": "Generate Shadowsocks Password", "generatingShadowsocksPassword": "Generating Password...", "shadowsocksPassword": "Shadowsocks Password", "shadowsocksPasswordCopied": "Password copied to clipboard", "copyShadowsocksPassword": "Copy Password", "selectEncryptionMethod": "Select Encryption Method", "regeneratePassword": "Regenerate Password"}, "settings.cores.title": "Cores", "settings.cores.description": "Manage Your Cores", "settings.cores.addCore": "Create Core", "settings.cores.noCores": "No cores available. Create one to get started.", "settings.cores.duplicateSuccess": "Core config «{{name}}» duplicated successfully", "settings.cores.duplicateFailed": "Failed to duplicate core config «{{name}}»", "settings.cores.deleteSuccess": "Core has been removed successfully", "settings.cores.deleteFailed": "Failed to remove core", "settings.cores.delete": "Remove Core Config", "settings.cores.coreNotFound": "Core not found", "createdAt": "Created at", "toggle": "Toggle status", "close": "Close", "copy": "Copy", "validation": {"generic": "{{field}} is invalid", "min": "{{field}} must be at least {{min}}", "max": "{{field}} must be at most {{max}}", "required": "{{field}} is required", "invalid_type": "{{field}} is required", "too_small": "{{field}} is too short", "too_big": "{{field}} is too long"}, "fields": {"username": "Username", "data_limit": "Data Limit", "expire": "Expire Date", "note": "Note", "on_hold_expire_duration": "On Hold Expire Duration", "group_ids": "Groups"}, "sidebar.expand": "Expand sidebar", "sidebar.collapse": "Collapse sidebar", "usersTable.deleteUserTitle": "Delete User", "usersTable.deleteUserPrompt": "Are you sure you want to delete user «{{name}}»? This action cannot be undone.", "usersTable.deleteSuccess": "User «{{name}}» has been deleted successfully.", "usersTable.deleteFailed": "Failed to delete user «{{name}}». {{error}}", "usersTable.delete": "Delete", "usersTable.cancel": "Cancel", "statistics.trafficUsage": "Traffic Usage", "statistics.trafficUsageDescription": "Total traffic usage across all servers", "statistics.usageDuringPeriod": "Usage During Period", "statistics.users": "Users", "statistics.onlineUsers": "Online Users", "statistics.limitedUsers": "Limited Users", "statistics.activeUsers": "Active Users", "statistics.disabledUsers": "Disabled Users", "statistics.expiredUsers": "Expired Users", "statistics.onHoldUsers": "On Hold Users", "statistics.allUsers": "All Users", "statistics.serverTraffic": "Server Traffic", "statistics.nodeTrafficDistribution": "Node Traffic Distribution", "statistics.noNodesAvailable": "No Nodes Available", "statistics.noDataAvailable": "No Data Available", "statistics.noDataDescription": "Select a time range to view traffic usage statistics and trends.", "statistics.noNodesDescription": "No nodes are currently connected to display traffic distribution.", "statistics.loadingDescription": "Fetching the latest statistics data...", "statistics.waitingForData": "Waiting for Data", "statistics.waitingForDataDescription": "Real-time statistics will appear here once data starts flowing.", "statistics.selectTimeRange": "Select Time Range", "statistics.selectTimeRangeDescription": "Choose a date range above to view traffic usage statistics.", "statistics.noDataInRange": "No Data in Selected Range", "statistics.noDataInRangeDescription": "No traffic data found for the selected time period. Try selecting a different date range.", "statistics.selectNodeToView": "Select a node to view detailed statistics", "statistics.userStatisticsDescription": "Overview of user account status and activity", "errors.connectionFailed": "Connection failed. Please check your network and try again.", "errors.statisticsLoadFailed": "Failed to Load Statistics", "usersTable.resetUsageTitle": "Reset User Usage", "usersTable.resetUsagePrompt": "Are you sure you want to reset the usage for «{{name}}»?", "usersTable.resetUsageSubmit": "Reset Usage", "usersTable.resetUsageSuccess": "User «{{name}}» usage has been reset successfully", "usersTable.resetUsageFailed": "Failed to reset usage for user «{{name}}»", "time": {"expires": "expires", "expired": "expired", "year": "year", "years": "years", "month": "month", "months": "months", "day": "day", "days": "days", "hour": "hour", "hours": "hours", "min": "min", "mins": "mins", "ago": "ago"}, "notConnectedYet": "Not connected yet", "node.xrayVersion": "Core Version", "node.coreVersion": "Node Version", "calendar": {"selectRange": "Select range", "startDate": "Start date", "endDate": "End date"}, "usersTable": {"usageChart": "Usage Chart", "noUsageData": "No usage data available for this period.", "tryDifferentRange": "Try a different time range.", "trendingUp": "Trending up by", "trendingDown": "Trending down by", "usageSummary": "Showing total usage for the selected period."}, "setOwnerModal": {"title": "Set Owner", "currentOwner": "Current owner:", "none": "None", "selectAdmin": "Select new owner", "confirm": "Set Owner", "success": "User {{username}}'s owner changed to {{admin}} successfully.", "error": "Failed to change owner of user {{username}} to {{admin}}.", "loadError": "Failed to load admins."}, "resilientNodeGroups": {"title": "Resilient Node Groups", "description": "Manage resilient node groups for improved reliability", "addGroup": "Add Group", "createGroup": "Create Resilient Node Group", "editGroup": "Edit Resilient Node Group", "name": "Name", "strategy": "Client Strategy", "nodes": "Nodes", "selectNodes": "Select nodes", "createSuccess": "Resilient node group created successfully", "updateSuccess": "Resilient node group updated successfully", "deleteSuccess": "Resilient node group deleted successfully", "operationFailed": "Failed to perform operation on resilient node group", "deleteFailed": "Failed to delete resilient node group", "strategies": {"urlTest": "URL Test", "fallback": "Fallback", "loadBalance": "Load <PERSON>", "clientDefault": "<PERSON><PERSON>", "none": "None"}, "clientDefault": "<PERSON><PERSON>"}, "hiddifyImport": {"title": "Import Users from Hiddify", "selectFile": "Select Hiddify Backup File", "fileSelected": "File selected: {{filename}}", "invalidFile": "Invalid File", "invalidFileDesc": "Please select a valid JSON file.", "unlimitedExpiration": "Set unlimited expiration for all users", "unlimitedExpirationDesc": "If enabled, all imported users will have unlimited expiration (expire = 0), ignoring package_days from Hiddify.", "smartUsernameParsing": "Enable smart username & note parsing", "smartUsernameParsingDesc": "If enabled, names like \"1234 John <PERSON>\" will be split: \"1234\" as username, \"<PERSON>\" as note. Otherwise, the full name will be used as username.", "protocolSelection": "Select Protocols", "protocolSelectionDesc": "Choose which protocols the imported users should have access to.", "noFileSelected": "No File Selected", "noProtocolsSelected": "No Protocols Selected", "importing": "Importing users...", "importUsers": "Import Users", "importComplete": "Import Complete", "importStats": "{{successful}} users imported successfully, {{failed}} failed.", "importSuccess": "Import Successful", "importSuccessDesc": "{{successful}} users imported successfully. {{failed}} users failed to import.", "importWarning": "Import Completed with Warnings", "importWarningDesc": "{{failed}} users failed to import. Check the details below.", "importError": "Import Failed", "importErrorDesc": "An error occurred during import. Please try again."}}
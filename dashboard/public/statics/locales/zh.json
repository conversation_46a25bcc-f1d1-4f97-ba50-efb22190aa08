{"active": "激活", "platform": "平台", "dashboard": "仪表板", "master": "主服务器", "dashboardDescription": "Marzban 管理仪表板", "allStatuses": "所有状态", "status": "状态", "statistics": "统计", "selectAll": "选择所有", "hosts": "主机", "success": "成功", "error": "错误", "modifying": "修改中...", "removing": "删除中...", "creating": "正在创建...", "monitorUsers": "监控用户", "alltime": "全部时间", "nodes": {"title": "节点", "description": "管理您的节点", "addNode": "添加节点", "editNode": "编辑节点", "deleteNode": "删除节点", "nodeName": "节点名称", "nodeAddress": "节点地址", "nodePort": "节点端口", "usageCoefficient": "使用系数", "connectionType": "连接类型", "serverCA": "服务器证书", "keepAlive": "保持连接", "maxLogs": "最大日志数", "maxLogsPlaceHolder": "输入最大日志数（以秒为单位）", "status": "状态", "actions": "操作", "connected": "已连接", "disconnected": "未连接", "disabled": "已禁用", "enabled": "已启用", "createSuccess": "节点 «{{name}}» 创建成功", "createFailed": "创建节点 «{{name}}» 失败", "editSuccess": "节点 «{{name}}» 更新成功", "editFailed": "更新节点 «{{name}}» 失败", "deleteSuccess": "节点 «{{name}}» 删除成功", "deleteFailed": "删除节点 «{{name}}» 失败", "enableSuccess": "节点 «{{name}}» 启用成功", "enableFailed": "启用节点 «{{name}}» 失败", "disableSuccess": "节点 «{{name}}» 禁用成功", "disableFailed": "禁用节点 «{{name}}» 失败", "certificate": "证书", "createdAt": "创建时间", "selectNode": "选择节点", "logs": {"title": "日志", "description": "查看和监控节点日志", "noLogs": "没有可用的日志", "loading": "正在加载日志...", "timestamps": "时间戳", "autoScroll": "自动滚动", "scrollToEnd": "滚动到底部", "clear": "清除日志", "search": "搜索日志", "filter": "过滤日志", "levels": "日志级别", "debug": "调试", "info": "信息", "warning": "警告", "error": "错误", "maxLogsTooltip": "内存中保留的最大日志数", "memory": "内存使用", "custom": "自定义", "unlimited": "无限制", "memoryWarning": "高值可能导致浏览器性能问题", "setCustom": "设置自定义限制", "customLimit": "输入自定义限制", "apply": "应用"}}, "groups": "群组", "documentation": "文档", "discussionGroup": "讨论组", "github": "GitHub", "community": "社区", "supportUs": "支持我们", "manageHosts": "管理和控制主机。", "manageSettings": "管理和控制设置。", "settings": {"title": "设置", "notifications": {"title": "通知", "description": "配置通知设置并管理系统的通知偏好", "loadError": "无法加载通知设置。请重试。", "saveSuccess": "通知设置保存成功", "saveFailed": "保存通知设置失败", "cancelSuccess": "已取消更改并恢复原始设置", "activeTypes": "活跃类型", "filterTitle": "通知过滤器", "filterDescription": "选择应触发通知的事件，以便了解重要的系统活动", "types": {"admin": "管理员", "core": "核心", "group": "群组", "host": "主机", "login": "登录", "node": "节点", "user": "用户", "userTemplate": "模板", "daysLeft": "到期", "percentageReached": "使用量"}, "telegram": {"title": "Telegram", "description": "配置 Telegram 机器人通知，在您的 Telegram 聊天或频道中接收实时警报", "apiToken": "机器人 API 令牌", "adminId": "管理员聊天 ID", "channelId": "频道 ID", "topicId": "主题 ID"}, "discord": {"title": "Discord", "description": "配置 Discord webhook 通知，直接在您的 Discord 服务器频道中接收警报", "webhookUrl": "Webhook URL"}, "advanced": {"title": "高级设置", "description": "配置高级通知行为，包括重试策略和代理设置", "maxRetries": "最大重试次数", "proxyUrl": "代理 URL"}}, "subscriptions": {"title": "订阅", "description": "配置订阅 URL、更新间隔和特定客户端规则", "loadError": "无法加载订阅设置", "saveSuccess": "订阅设置保存成功", "saveError": "保存订阅设置失败", "cancelSuccess": "已取消更改并恢复原始设置", "general": {"title": "常规设置", "description": "配置基本订阅参数", "urlPrefix": "URL 前缀", "urlPrefixPlaceholder": "sub", "urlPrefixDescription": "添加到所有订阅 URL 的前缀", "updateInterval": "更新间隔（小时）", "updateIntervalDescription": "订阅自动更新的间隔时间", "supportUrl": "支持链接", "supportUrlPlaceholder": "输入支持页面 URL", "supportUrlDescription": "用户支持和帮助的链接", "profileTitle": "配置文件标题", "profileTitlePlaceholder": "输入配置文件标题", "profileTitleDescription": "显示在客户端中的配置文件名称", "hostStatusFilter": "主机状态过滤", "hostStatusFilterDescription": "仅包含活跃主机在订阅中"}, "rules": {"title": "订阅规则", "description": "为特定客户端模式定义自定义规则", "addRule": "添加规则", "noRules": "尚未配置规则。点击上方按钮添加第一个规则。", "pattern": "模式", "patternPlaceholder": "输入客户端模式（例如：*android*）", "patternDescription": "匹配客户端用户代理的模式", "target": "目标格式", "targetDescription": "匹配此模式时使用的配置格式"}, "formats": {"title": "手动订阅格式", "description": "启用或禁用特定的订阅格式", "links": "链接", "linksDescription": "原始代理链接格式", "linksBase64": "Base64 链接", "linksBase64Description": "Base64 编码的代理链接", "xray": "Xray", "xrayDescription": "Xray 核心配置格式", "singBox": "Sing-box", "singBoxDescription": "Sing-box 配置格式", "clash": "Clash", "clashDescription": "Clash 代理配置格式", "clashMeta": "<PERSON><PERSON>", "clashMetaDescription": "Clash Meta 增强配置格式", "outline": "Outline", "outlineDescription": "Outline VPN 配置格式"}, "configFormats": {"links": "链接", "links_base64": "Base64 链接", "xray": "Xray", "sing_box": "Sing-box", "clash": "Clash", "clash_meta": "<PERSON><PERSON>", "outline": "Outline", "block": "阻止"}}, "telegram": {"title": "Telegram", "description": "配置 Telegram 机器人集成和系统相关设置", "loadError": "加载 Telegram 设置失败，请重试。", "saveSuccess": "Telegram 设置保存成功", "saveFailed": "保存 Telegram 设置失败", "cancelSuccess": "更改已取消，原始 Telegram 设置已恢复", "general": {"title": "常规设置", "description": "基本 Telegram 机器人配置和连接设置", "enable": "启用 Telegram 集成", "enableDescription": "为您的系统启用或禁用 Telegram 机器人功能", "token": "机器人 API 令牌", "tokenPlaceholder": "输入您的 Telegram 机器人令牌", "tokenDescription": "从 Telegram 的 @BotFather 获取的机器人令牌", "webhookUrl": "Webhook URL", "webhookUrlPlaceholder": "https://your-domain.com/webhook", "webhookUrlDescription": "Telegram 发送更新的 URL", "webhookSecret": "Webhook 密钥", "webhookSecretPlaceholder": "输入 webhook 密钥", "webhookSecretDescription": "用于 webhook 安全的秘密令牌", "proxyUrl": "代理 URL", "proxyUrlPlaceholder": "socks5://proxy.example.com:1080", "proxyUrlDescription": "Telegram API 连接的代理 URL（可选）"}, "advanced": {"title": "高级设置", "description": "高级 Telegram 机器人功能和配置", "miniAppLogin": "启用小程序登录", "miniAppLoginDescription": "允许用户通过 Telegram 小程序登录"}}, "discord": {"title": "Discord", "description": "为您的系统配置 Discord 机器人集成和相关设置", "loadError": "加载 Discord 设置失败。请重试。", "saveSuccess": "Discord 设置保存成功", "saveFailed": "保存 Discord 设置失败", "cancelSuccess": "已取消更改并恢复原始 Discord 设置", "general": {"title": "基本设置", "description": "基本 Discord 机器人配置和连接设置", "enable": "启用 Discord 机器人", "enableDescription": "启用或禁用系统的 Discord 机器人功能", "token": "机器人令牌", "tokenPlaceholder": "输入您的 Discord 机器人令牌", "tokenDescription": "从 Discord 开发者门户获取的机器人令牌", "proxyUrl": "代理 URL", "proxyUrlPlaceholder": "socks5://proxy.example.com:1080", "proxyUrlDescription": "Discord API 连接的代理 URL（可选）"}}, "webhook": {"title": "Webhook", "description": "配置系统的Webhook通知和端点设置", "loadError": "加载Webhook设置失败。请重试。", "saveSuccess": "Webhook设置保存成功", "saveFailed": "保存Webhook设置失败", "cancelSuccess": "更改已取消，Webhook设置已还原", "general": {"title": "常规设置", "description": "基本Webhook配置和连接设置", "enable": "启用Webhook", "enableDescription": "为您的系统启用或禁用Webhook通知", "timeout": "超时（秒）", "timeoutDescription": "Webhook调用的请求超时时间（1-300秒）", "recurrent": "重试次数", "recurrentDescription": "失败Webhook的重试次数（1-24）", "proxyUrl": "代理URL", "proxyUrlPlaceholder": "http://proxy.example.com:8080", "proxyUrlDescription": "Webhook请求的代理URL（可选）"}, "webhooks": {"title": "Webhook端点", "description": "配置Webhook URL和身份验证", "add": "添加Webhook", "addFirst": "添加第一个Webhook", "webhook": "Webhook", "empty": "尚未配置Webhook。添加您的第一个Webhook端点。", "name": "名称", "namePlaceholder": "我的Webhook", "url": "URL", "secret": "密钥", "secretPlaceholder": "输入Webhook密钥"}, "triggers": {"daysLeft": {"title": "剩余天数通知", "description": "当用户账户剩余指定天数时发送通知", "empty": "未设置触发器"}, "usagePercent": {"title": "使用百分比通知", "description": "当用户达到指定使用百分比时发送通知", "empty": "未设置触发器"}}}, "cleanup": {"title": "清理", "description": "管理过期账户和重置数据使用量以进行系统维护", "loadError": "加载清理设置失败，请重试。", "saveSuccess": "清理操作成功完成", "saveFailed": "清理操作失败", "cancelSuccess": "清理操作已取消", "expiredUsers": {"title": "删除过期账户", "description": "删除在特定日期范围内过期的账户", "expiredAfter": "过期时间晚于", "expiredBefore": "过期时间早于", "expiredAfterPlaceholder": "选择开始日期", "expiredBeforePlaceholder": "选择结束日期", "dateRange": "日期范围", "deleteExpired": "删除过期账户", "deleting": "删除中...", "deleteSuccess": "成功删除 {{count}} 个过期账户", "deleteFailed": "删除过期账户失败", "confirmDelete": "删除过期账户", "confirmDeleteMessage": "您确定要删除所选日期范围内的所有过期账户吗？此操作无法撤销。", "noDateSelected": "请至少选择一个日期", "selectDateRange": "选择要删除过期账户的日期范围（只能选择过去的日期）"}, "resetUsage": {"title": "重置所有数据使用量", "description": "重置系统中所有账户的数据使用量", "resetAll": "重置所有使用量", "resetting": "重置中...", "resetSuccess": "所有数据使用量重置成功", "resetFailed": "重置数据使用量失败", "confirmReset": "重置所有数据使用量", "confirmResetMessage": "您确定要重置所有账户的数据使用量吗？此操作无法撤销。", "warning": "此操作将重置系统中所有账户的数据使用量"}, "clearUsageData": {"title": "清除使用数据", "description": "从选定的数据库表中永久删除使用数据", "selectTable": "选择表", "selectTablePlaceholder": "选择要清除的表", "noTableSelected": "请选择要清除的表", "dataAfter": "数据晚于", "dataBefore": "数据早于", "dataAfterPlaceholder": "选择开始日期", "dataBeforePlaceholder": "选择结束日期", "selectDateRange": "选择日期范围以按创建时间过滤数据（可选 - 留空以清除所有数据）", "clearData": "清除数据", "clearing": "清除中...", "clearSuccess": "成功清除 {{table}} 表中的数据", "clearFailed": "清除使用数据失败", "confirmClear": "确认清除数据", "confirmClearMessage": "您确定要永久删除 {{table}} 表中的所有数据吗？此操作无法撤销。", "warning": "警告：此操作将永久删除所选表中的所有使用数据。此操作无法撤销。", "tables": {"nodeUserUsages": "节点用户使用情况", "userUsages": "用户使用情况"}}}, "theme": {"title": "主题", "description": "自定义您的应用主题", "light": "明亮", "dark": "深色", "system": "系统", "selectTheme": "选择主题", "themeSaved": "主题保存成功", "themeChanged": "主题更改成功", "visitThemePage": "访问主题页面进行进一步自定义", "radiusSaved": "边框圆角更新成功", "mode": "模式", "color": "颜色", "radius": "边框圆角", "zinc": "锌灰", "rose": "玫瑰", "blue": "蓝色", "green": "绿色", "violet": "紫色", "orange": "橙色", "yellow": "黄色", "default": "默认", "red": "红色", "modeDescription": "选择界面的显示方式", "colorDescription": "选择您喜欢的配色方案", "radiusDescription": "调整界面元素的圆角程度", "lightDescription": "明亮清新", "darkDescription": "护眼模式", "systemDescription": "跟随设备", "preview": "实时预览", "previewDescription": "实时查看您的主题设置效果", "dashboardPreview": "仪表板预览", "dashboardDescription": "监控系统性能和管理用户账户", "currentTheme": "当前主题", "sampleInput": "示例输入", "primaryButton": "主要按钮", "resetToDefaults": "重置为默认", "resetDescription": "将所有主题设置恢复为默认值", "resetting": "重置中...", "reset": "重置", "resetSuccess": "主题已重置为默认设置", "resetFailed": "重置主题设置失败", "radiusNone": "无", "radiusSmall": "小", "radiusMedium": "中", "radiusLarge": "大"}}, "saving": "保存中...", "general": "常规", "core": "核心", "activeUsers": "活跃用户", "apply": "应用", "cancel": "取消", "created": "已创建", "by": "由", "ip": "IP", "admin": "管理员", "sudo": "超级用户", "save": "保存", "test": "测试", "testing": "测试中", "confirm": "确认", "admins": {"title": "管理员", "description": "管理系统管理员", "createAdmin": "创建管理员", "editAdmin": "编辑管理员", "deleteAdmin": "删除管理员", "username": "用户名", "password": "密码", "isSudo": "超级管理员", "createdAt": "创建时间", "actions": "操作", "createSuccess": "管理员「{{name}}」创建成功", "createFailed": "创建管理员「{{name}}」失败", "editSuccess": "管理员「{{name}}」更新成功", "editFailed": "更新管理员「{{name}}」失败", "deleteSuccess": "管理员{{name}}删除成功", "deleteFailed": "删除管理员{{name}}失败", "enterUsername": "请输入用户名", "enterPassword": "请输入密码", "sudo": "超级管理员权限", "edit": "保存更改", "create": "创建管理员", "status": "状态", "role": "角色", "total.users": "用户总数", "used.traffic": "已用流量", "total": "管理员总数", "active": "活跃管理员", "disable": "禁用的管理员", "telegramId": "Telegram ID", "discord": "Discord Webhook", "supportUrl": "支持链接", "subDomain": "订阅域名", "profile": "个人资料标题", "subTemplate": "订阅模板路径", "passwordConfirm": "确认密码", "enterPasswordConfirm": "请输入确认密码", "disableSuccess": "管理员「{{name}}」已成功禁用", "enableSuccess": "管理员「{{name}}」已成功启用", "disableFailed": "禁用管理员「{{name}}」失败", "enableFailed": "启用管理员「{{name}}」失败", "resetUsersUsage": "管理员重置用户使用情况", "resetUsageSuccess": "管理员已成功重置用户「{{name}}」的使用情况", "resetUsageFailed": "管理员重置用户「{{name}}」的使用情况失败", "reset": "重置使用情况", "discordId": "Discord ID", "lifetime.used.traffic": "总计", "used": {"traffic": "使用情况"}, "monitor": {"traffic": "监控管理员使用情况", "no_traffic": "没有使用情况数据"}}, "shortcuts": {"title": "键盘快捷键", "description": "管理您的仪表板快捷键以便快速访问", "add": "添加快捷键", "empty": "未配置快捷键"}, "quickActions": {"title": "快速操作", "description": "选择操作以在系统中创建新项目", "comingSoon": "即将推出"}, "resetUsersUsage.prompt": "你确定要重置管理员用户<b>{{name}}</b>的使用情况吗？", "admin.disable": "管理员已禁用", "admin.enable": "管理员已启用", "deleteAdmin.prompt": "您确定要删除管理员<b>{{name}}</b>吗？", "activeUsers.prompt": "您是否要激活此 {{name}} 下的所有用户？", "disableUsers.prompt": "您是否要禁用此 {{name}} 下的所有用户？", "manageAccounts": "控制、更新和排列用户账户", "manageGroups": "控制、更新和排列用户组", "manageNodes": "监控、控制、添加、编辑和删除节点", "onlineUsers": "在线用户", "totalUsers": "总用户", "edit": "编辑", "editGroup": "编辑群组", "disable": "禁用", "warning": "警告", "remove": "删除", "modify": "修改", "templates.userTemplates": "模板", "editUserTemplateModal.title": "编辑用户模板", "userTemplateModal.title": "创建用户模板", "templates": {"title": "模板", "description": "管理您的模板。", "addTemplate": "创建模板", "editSuccess": "用户模板「{{name}}」已成功更新", "createSuccess": "用户模板「{{name}}」已成功创建", "editFailed": "更新用户模板「{{name}}」失败", "createFailed": "创建用户模板「{{name}}」失败", "name": "名称", "status": "状态", "prefix": "用户名前缀", "suffix": "用户名后缀", "dataLimit": "数据限制", "expire": "过期时间", "onHoldTimeout": "挂起超时", "method": "方法", "flow": "流程", "groups": "分组", "userDataLimitStrategy": "数据限制重置策略", "resetUsage": "重置使用量", "groupsExistingWarning": "您尚未添加任何<a>分组</a>。", "deleteSuccess": "模板 {{name}} 已成功删除", "deleteFailed": "删除模板 {{name}} 失败", "deleteUserTemplateTitle": "删除用户模板", "deleteUserTemplatePrompt": "您确定要删除模板 <b>{{name}}</b> 吗？", "duplicateSuccess": "模板「{{name}}」复制成功", "duplicateFailed": "复制模板「{{name}}」失败", "enableSuccess": "模板「{{name}}」已成功启用", "disableSuccess": "模板「{{name}}」已成功禁用", "enableFailed": "启用管理员「{{name}}」失败", "disableFailed": "禁用管理员「{{name}}」失败"}, "core.configuration": "配置", "core.generalErrorMessage": "配置有误, 请检查", "core.logs": "日志", "core.restartCore": "重启核心", "core.restarting": "重启中...", "core.save": "保存", "core.socket.closed": "已关闭", "core.socket.connected": "已连接", "core.socket.connecting": "连接中...", "core.socket.not_connected": "未连接", "core.successMessage": "核心设置更新成功", "core.title": "核心设置", "core.toggleSuccess": "核心 «{{name}}» 状态切换成功", "core.toggleFailed": "核心 «{{name}}» 状态切换失败", "core.deleteSuccess": "核心 «{{name}}» 删除成功", "core.deleteFailed": "核心 «{{name}}» 删除失败", "core.deleteConfirm": "您确定要删除核心 «{{name}}» 吗？", "createNewUser": "创建新用户", "createUser": "创建用户", "createGroup": "创建群组", "dataUsage": "总流量", "dateFormat": "MM/dd/yyyy", "delete": "删除", "deleteNode.deleteSuccess": "节点 {{name}} 删除成功", "deleteNode.prompt": "您确实要删除 <b>{{name}}</b> 节点吗？", "deleteNode.title": "删除节点", "deleteHost.title": "删除主机", "deleteHost.prompt": "您确定要删除 <b>{{name}}</b> 主机吗？", "deleteHost.deleteSuccess": "主机 {{name}} 已成功删除", "deleteHost.deleteFailed": "无法删除主机 {{name}}", "editHost.title": "编辑主机", "editNode.editSuccess": "节点 {{name}} 已成功更新", "editNode.title": "编辑节点", "deleteUser.deleteSuccess": "{{username}} 删除成功。", "deleteUser.prompt": "您确定你要删除 <b>{{username}}</b>？", "deleteUser.title": "删除用户", "disabled": "禁用", "expire": "过期", "expired": "已在 {{time}} 前过期", "expires": "将在 {{time}} 后过期", "header.donation": "捐赠", "header.hostSettings": "设置", "header.logout": "退出", "header.nodeSettings": "节点设置", "header.nodesUsage": "节点统计", "hostsDialog.addHost": "添加主机", "hostsDialog.advancedOptions": "高级选项", "hostsDialog.useSniAsHost": "使用 SNI 作为主机名", "hostsDialog.alpn": "ALPN", "hostsDialog.apply": "保存", "hostsDialog.currentServer": "当前服务器的 IP 地址", "hostsDialog.currentServerv6": "当前服务器的 IPv6 地址", "hostsDialog.dataLimit": "用户的流量限制", "hostsDialog.dataUsage": "用户当前流量情况", "hostsDialog.desc": "使用这些变量使其可以动态替换", "hostsDialog.expireDate": "用户的有效期", "hostsDialog.fingerprint": "指纹", "hostsDialog.fragment": {"title": "分片", "packets": "数据包", "packetsPlaceholder": "输入数据包数量", "length": "长度", "lengthPlaceholder": "输入长度", "interval": "间隔", "intervalPlaceholder": "输入间隔", "allFieldsRequired": "如果填写一个字段，则必须填写所有字段"}, "hostsDialog.fragment.info": "length,interval,packet (e.g. 10-100,100-200,tlshello)", "hostsDialog.fragment.info.attention": "请注意： 当前特性仅支持 streisand >= 1.6.12 and v2rayNG >= 1.8.16 (自定义配置)", "hostsDialog.fragment.info.examples": "示例：", "hostsDialog.noise": {"title": "噪声", "addNoise": "添加噪声", "removeNoise": "删除噪声", "packet": "数据包", "packetPlaceholder": "输入数据包值（例如：rand:10-20）", "delay": "延迟", "delayPlaceholder": "输入延迟", "rand": "随机"}, "hostsDialog.noise.info": "packet,delay (e.g. rand:10-20,100-200)", "hostsDialog.noise.info.attention": "Attention: currently, this feature only supported in streisand >= 1.6.32 and v2rayNG >= 1.8.39 (custom config)", "hostsDialog.noise.info.examples": "Examples:", "hostsDialog.host": "主机", "hostsDialog.host.info": "默认情况下，如果在 Xray 配置中设置了请求主机，则使用该主机。但是，如果需要，您可以在此处设置自定义请求主机。", "hostsDialog.host.multiHost": "使用 <badge>,</badge> 作为分隔符设置多个地址，使用时，每次随机选择一个地址。", "hostsDialog.host.wildcard": "使用 <badge>*</badge> 生成随机字符串 (在通配符域名中生效)", "hostsDialog.jalaliExpireDate": "用户阳历有效日期", "hostsDialog.loading": "加载中...", "hostsDialog.muxEnable": "启用 MUX", "hostsDialog.path": "路径", "hostsDialog.path.info": "为主机用户设置路径，通常在反向代理中使用。", "hostsDialog.port": "端口", "hostsDialog.port.info": "默认情况下，主机使用入站端口的默认端口。如果此主机是一个服务器，用于从与您的服务器端口不同的端口转发流量，则可以设置自定义端口。例如，服务器可能会将来自端口 8443 的流量转发到您入站服务器的默认端口。", "hostsDialog.proxyMethod": "代理传输方法（例如 ws）", "hostsDialog.proxyOutbound": "代理出站 json", "hostsDialog.proxyOutbound.info": "额外出站（仅在 v2ray 自定义配置中）", "hostsDialog.proxyProtocol": "代理协议（例如 VMess）", "hostsDialog.randomUserAgent": "使用随机 User-Agent", "hostsDialog.remainingData": "用户剩余流量情况", "hostsDialog.remainingDays": "用户的剩余天数", "hostsDialog.remainingTime": "用户剩余时间", "hostsDialog.savedSuccess": "设置保存成功", "hostsDialog.security": "安全层", "hostsDialog.security.info": "如果此主机的中间件服务器使用的安全层与入站默认值不同，则可以在此处设置自定义安全层。", "hostsDialog.sni": "SNI", "hostsDialog.sni.info": "默认情况下，主机使用入站 SNI 的默认值。如果此主机是一个服务器且 SNI 不同，则可以设置自定义 SNI。例如，服务器可能会接收带有不同 SSL 证书的流量，执行 SSL 处理并将其转发到您的入站服务器。", "hostsDialog.sniPlaceholder": "SNI (例如：example.com)", "hostsDialog.sockopt": "<PERSON><PERSON><PERSON><PERSON>", "hostsDialog.statusEmoji": "用户状态作为表情符号 (✅,⌛️,🪫,❌,🔌)", "hostsDialog.statusText": "用户状态", "hostsDialog.title": "使用此设置，您可以为每个入站分配特定的地址。", "hostsDialog.username": "用户的用户名", "inbound": "入站", "itemsPerPage": "每页条数", "login": "登录", "login.fieldRequired": "此项必填", "login.loginYourAccount": "登录您的帐号", "login.welcomeBack": "欢迎回来，请输入您的详细信息", "memoryUsage": "内存状态", "next": "下一页", "monitorServers": "监控您的服务器和用户", "manageServers": "控制您的服务器详细信息", "nodes.addHostForEveryInbound": "为每个入站请求添加此节点作为新主机", "nodes.addNewMarzbanNode": "添加新的 Marzban 节点", "nodes.prompt": "添加并配置节点详细信息", "nodes.addNode": "添加节点", "nodes.addNodeSuccess": "节点 {{name}} 添加成功", "nodes.apply": "编辑节点", "nodes.certificate": "证书", "nodes.certificateCopied": "证书已复制到剪贴板", "nodes.certificateDownloaded": "证书下载成功", "nodes.certificateDescription": "要设置节点，您需要配置此证书以建立主服务器和节点之间的安全连接", "nodes.connection-hint": "要设置Marzban节点，您需要在节点上设置此证书以初始化主服务器和节点之间的安全连接", "nodes.download-certificate": "下载证书", "nodes.editNode": "保存", "nodes.hide-certificate": "隐藏证书", "nodes.nodeAPIPort": "API 端口", "nodes.nodeName": "名称", "nodes.nodePort": "端口", "nodes.reconnect": "重新连接", "nodes.reconnecting": "重连中...", "nodes.show-certificate": "如何证书", "nodes.title": "使用 Marzban-Node，您可以通过在不同的服务器上添加不同的节点来提供多节点负载。", "nodes.usageCoefficient": "使用系数", "on_hold": "在线", "duplicate": "复制", "createAndManageTemplates": "创建和管理模板", "password": "密码", "previous": "上一页", "qrcodeDialog.sublink": "订阅链接", "reset": "重置", "resetAllUsage": "重置所有统计", "resetAllUsage.error": "重置失败，请稍候再试！", "resetAllUsage.prompt": "此操作将清除所有用户统计，您确定要执行此操作吗？ 这不能被撤消！", "resetAllUsage.success": "所有统计重置完成。", "resetAllUsage.title": "重置所有用户的数据使用情况", "resetUserUsage.error": "重置失败，请稍候再试", "resetUserUsage.prompt": "您确定要重置 <b>{{username}}</b> 的流量统计吗？", "resetUserUsage.success": "{{username}} 的流量统计重置完成。", "resetUserUsage.title": "重置用户流量统计", "revoke": "撤销", "revokeUserSub.error": "撤销订阅失败，请稍候再试！", "revokeUserSub.prompt": "您确定要撤销用户 «{{username}}» 的订阅吗？", "revokeUserSub.success": "成功撤销用户 {{username}} 的订阅。", "revokeUserSub.title": "撤销用户订阅", "search": "搜索", "status.active": "活跃", "status.enable": "启用", "status.disabled": "禁用", "status.expired": "过期", "status.limited": "受限", "status.on_hold": "在线", "nodeModal.status.error": "误", "nodeModal.status.disabled": "已禁用", "nodeModal.status.connecting": "正在连接", "nodeModal.status.connected": "已连接", "userDialog.absolute": "选择范围", "userDialog.custom": "自定义", "userDialog.dataLimit": "流量限制", "userDialog.days": "天", "userDialog.editUser": "修改", "userDialog.editUserTitle": "用户编辑", "userDialog.endDate": "结束日期", "userDialog.expiryDate": "过期日期", "userDialog.timeOutDate": "超时日期", "userDialog.generatedByDefault": "默认自动生成", "userDialog.hours": "小时", "userDialog.method": "加密方式", "userDialog.months": "月", "userDialog.note": "备注", "userDialog.onHold": "保持", "userDialog.onHoldExpireDuration": "过期时间", "userDialog.onHoldExpireDurationPlaceholder": "例如：7", "userDialog.optional": "可以为空", "userDialog.periodicUsageReset": "定期重置流量", "userDialog.protocols": "协议", "userDialog.relative": "相对时间", "userDialog.resetStrategyAnnually": "每年", "userDialog.resetStrategyDaily": "每天", "userDialog.resetStrategyMonthly": "每月", "userDialog.resetStrategyNo": "无", "userDialog.resetStrategyWeekly": "每周", "userDialog.resetUsage": "重置流量", "userDialog.revokeSubscription": "撤销订阅", "userDialog.subscriptionInfo": "订阅信息", "userDialog.subscriptionUpdated": "最后更新", "userDialog.lastClient": "最后客户端", "userDialog.subscriptionNotAccessed": "尚未访问", "userDialog.unknownClient": "未知客户端", "userDialog.selectOneProtocol": "请至少选择一个协议", "userDialog.shadowsocksDesc": "快速且安全, 但效率不如其它", "userDialog.startDate": "开始日期", "userDialog.total": "总共：", "userDialog.trojanDesc": "轻量、安全且非常快", "userDialog.usage": "流量详情", "userDialog.userAlreadyExists": "用户已存在", "userDialog.userCreated": "成功创建用户 {{username}}。", "userDialog.userEdited": "已更新用户 {{username}}。", "userDialog.vlessDesc": "轻量、快速且安全", "userDialog.vmessDesc": "快速且安全", "userDialog.warningNoProtocol": "请至少选择一个协议", "userDialog.weeks": "周", "userDialog.proxySettingsAccordion": "代理设置", "userDialog.proxySettings.vmess": "VMess", "userDialog.proxySettings.vless": "VLESS", "userDialog.proxySettings.trojan": "Trojan", "userDialog.proxySettings.shadowsocks": "Shadowsocks", "userDialog.proxySettings.id": "ID", "userDialog.proxySettings.password": "密码", "userDialog.proxySettings.method": "加密方式", "userDialog.proxySettings.flow": "流 (Flow)", "userDialog.proxySettings.desc": "为此用户配置协议专属设置。", "userDialog.expireDate": "过期日期", "userDialog.selectedGroups": "已选择 {{count}} 个分组", "username": "用户名", "users": "用户", "usersTable.copied": "已复制", "usersTable.copyConfigs": "复制配置", "usersTable.copyLink": "复制订阅链接", "usersTable.dataUsage": "流量统计", "usersTable.noUser": "还没有添加任何用户", "usersTable.noUserMatched": "没有找到您搜索的用户", "usersTable.status": "状态", "usersTable.sortByExpire": "按过期时间排序", "usersTable.total": "总共", "statistics.system": "系统", "statistics.totalTraffic": "总流量", "statistics.ramUsage": "RAM 使用情况", "statistics.cpuUsage": "CPU 使用情况", "statistics.cores": "核心", "statistics.serverSelection": "服务器选择", "statistics.selectServerToMonitor": "选择要监控其性能指标的服务器", "statistics.selectServer": "选择服务器", "statistics.realTimeData": "实时数据", "statistics.historicalData": "历史数据", "statistics.viewHistorical": "查看历史", "statistics.viewRealtime": "查看实时", "statistics.historical": "历史", "statistics.realtime": "实时", "statistics.realtimeDescription": "为您的服务器和用户提供实时统计和图表。", "statistics.historicalDescription": "所选时间范围内的历史服务器性能数据", "statistics.selectTimeRange": "选择时间范围", "statistics.selectTimeRangeDescription": "选择用于历史数据分析的时间段", "statistics.selectTimeRangeToView": "请选择一个时间范围来查看历史数据", "hostsDialog": {"addHost": "添加主机", "selectInbound": "选择入站", "fingerprint": "指纹", "selectStatus": "选择状态", "remarkRequired": "备注为必填项", "addressRequired": "地址是必需的", "portRequired": "端口为必填项", "status": {"label": "状态", "active": "活动", "disabled": "禁用", "limited": "受限", "expired": "已过期", "onHold": "等待中"}, "loading": "加载中...", "port": "端口", "sni": "SNI", "host": "主机", "path": "路径", "networkSettings": "网络设置", "transportSettings": "传输设置", "transportType": "传输类型", "selectTransport": "选择传输类型", "serviceName": "服务名称", "enterServiceName": "输入 gRPC 服务名称", "seed": "种子", "enterSeed": "输入 KCP 种子", "enterPath": "输入路径 (示例: /xray)", "enterHost": "输入主机 (示例: example.com)", "header": "头部", "selectHeader": "选择头部类型", "multiMode": "多模式", "securitySettings": "安全设置", "camouflagSettings": "伪装设置", "security": "安全层", "alpn": "ALPN", "allowInsecure": "允许不安全连接", "muxEnable": "启用 MUX", "randomUserAgent": "使用随机 User-Agent", "inboundDefault": "入站默认值", "earlyData": "早期数据", "httpHeaders": "HTTP 标头", "headersName": "标头名称", "headersValue": "标头值", "addHeader": "添加标头", "removeHeader": "删除标头", "createSuccess": "主机「{{name}}」已成功创建", "createFailed": "创建主机「{{name}}」失败", "editSuccess": "主机「{{name}}」已成功更新", "editFailed": "更新主机「{{name}}」失败", "noStatus": "未选择状态", "clearAllStatuses": "清除所有状态", "transportSettingsAccordion": "传输设置", "xhttp": {"mode": "模式", "noGrpcHeader": "无 gRPC 头", "xPaddingBytes": "X-Padding 字节", "scMaxEachPostBytes": "每个帖子最大字节数", "scMinPostsIntervalMs": "帖子最小间隔（毫秒）", "scMaxBufferedPosts": "最大缓冲帖子数", "scStreamUpServerSecs": "流上传服务器（秒）", "xmux": "XMux 设置", "maxConcurrency": "最大并发数", "maxConnections": "最大连接数", "cMaxReuseTimes": "最大重用次数", "cMaxLifetime": "最大生命周期", "hMaxRequestTimes": "最大请求次数", "hKeepAlivePeriod": "保活周期", "downloadSettings": "下载设置", "downloadSettingsInfo": "选择用于下载设置的主机", "selectDownloadSettings": "选择下载设置主机"}, "grpc": {"multiMode": "多模式", "idleTimeout": "空闲超时", "healthCheckTimeout": "健康检查超时", "permitWithoutStream": "允许无流", "initialWindowsSize": "初始窗口大小"}, "kcp": {"header": "头部", "mtu": "MTU", "tti": "TTI", "uplinkCapacity": "上行容量", "downlinkCapacity": "下行容量", "congestion": "拥塞控制", "readBufferSize": "读缓冲区大小", "writeBufferSize": "写缓冲区大小"}, "tcp": {"title": "TCP 设置", "header": "头部类型", "request": {"title": "请求设置", "version": "HTTP 版本", "method": "HTTP 方法", "headers": "请求头"}, "response": {"title": "响应设置", "version": "HTTP 版本", "status": "状态码", "reason": "状态原因", "headers": "响应头"}, "headerName": "头部名称", "headerValue": "头部值", "addHeader": "添加头部", "removeHeader": "删除头部", "requestHeaders": "请求头", "responseHeaders": "响应头"}, "websocket": {"heartbeatPeriod": "心跳周期"}, "muxSettings": "多路复用设置", "enableMux": "启用 Mux", "xraySettings": "Xray 设置", "singBoxSettings": "Sing-box 设置", "clashSettings": "Clash 设置", "addXraySettings": "添加 Xray 设置", "addSingBoxSetting": "添加 Sing-box 设置", "addClashSettings": "添加 Clash 设置", "protocol": "协议", "selectProtocol": "选择协议", "maxConnections": "最大连接数", "maxStreams": "最大流数", "minStreams": "最小流数", "padding": "启用填充", "enabled": "启用", "concurrency": "并发数", "xudpConcurrency": "XUDP 并发数", "xudpProxy443": "XUDP 代理 443", "selectXudpProxy443": "选择 XUDP 代理 443", "statistic": "启用统计", "onlyTcp": "仅 TCP", "brutal": {"enable": "启用 B<PERSON><PERSON>", "title": "Brutal 设置", "upMbps": "上传速度 (Mbps)", "downMbps": "下载速度 (Mbps)"}, "address": "地址", "variables": {"title": "使用这些变量使其动态化", "server_ip": "当前服务器的 IP 地址", "server_ipv6": "当前服务器的 IPv6 地址", "username": "用户名", "data_usage": "当前数据使用量", "data_left": "剩余数据量", "data_limit": "数据使用限制", "days_left": "剩余天数", "expire_date": "到期日期", "jalali_expire_date": "阳历到期日期", "time_left": "剩余时间", "status_text": "用户状态", "status_emoji": "用户状态表情符号 (✅,⌛️,🪫,❌,🔌)", "protocol": "代理协议（如 VMess）", "transport": "代理传输方式（如 ws）"}, "httpVersions": {"1.0": "HTTP/1.0", "1.1": "HTTP/1.1", "2.0": "HTTP/2.0", "3.0": "HTTP/3.0"}, "httpMethods": {"GET": "GET", "POST": "POST", "PUT": "PUT", "DELETE": "DELETE", "HEAD": "HEAD", "OPTIONS": "OPTIONS", "PATCH": "PATCH", "TRACE": "TRACE", "CONNECT": "CONNECT"}, "httpReasons": {"100": "继续", "101": "切换协议", "200": "成功", "201": "已创建", "202": "已接受", "203": "非授权信息", "204": "无内容", "205": "重置内容", "206": "部分内容", "300": "多种选择", "301": "永久移动", "302": "临时移动", "303": "查看其他位置", "304": "未修改", "305": "使用代理", "307": "临时重定向", "308": "永久重定向", "400": "错误请求", "401": "未授权", "402": "需要支付", "403": "禁止", "404": "未找到", "405": "方法禁用", "406": "不接受", "407": "需要代理授权", "408": "请求超时", "409": "冲突", "410": "已删除", "411": "需要有效长度", "412": "未满足前提条件", "413": "请求实体过大", "414": "请求的 URI 过长", "415": "不支持的媒体类型", "416": "请求范围不符合要求", "417": "未满足期望值", "418": "我是茶壶", "421": "错误的请求", "422": "不可处理的实体", "423": "已锁定", "424": "失败的依赖", "425": "太早", "426": "需要升级", "428": "需要先决条件", "429": "太多请求", "431": "请求头字段太大", "451": "因法律原因不可用", "500": "服务器内部错误", "501": "尚未实施", "502": "错误网关", "503": "服务不可用", "504": "网关超时", "505": "HTTP 版本不受支持"}, "selectReason": "选择原因"}, "enable": "启用", "host": {"xudp_proxy_443": "XUDP 代理 443", "reject": "拒绝", "allow": "允许", "enableSuccess": "主机「{{name}}」已成功启用", "enableFailed": "启用主机「{{name}}」失败", "disableSuccess": "主机「{{name}}」已成功禁用", "disableFailed": "禁用主机「{{name}}」失败", "duplicateSuccess": "主机「{{name}}」已成功复制", "duplicateFailed": "复制主机「{{name}}」失败"}, "marzban": "Marzban", "group": {"createSuccess": "群组「{{name}}」已成功创建", "createFailed": "创建群组「{{name}}」失败", "editSuccess": "群组「{{name}}」已成功更新", "editFailed": "更新群组「{{name}}」失败", "deleteSuccess": "群组「{{name}}」已成功删除", "deleteFailed": "删除群组「{{name}}」失败", "deleteConfirm": "您确定要删除群组「{{name}}」吗？", "enableSuccess": "群组「{{name}}」已成功启用", "enableFailed": "启用群组「{{name}}」失败", "disableSuccess": "群组「{{name}}」已成功禁用", "disableFailed": "禁用群组「{{name}}」失败"}, "name": "名称", "nodeModal": {"title": "添加节点", "description": "添加新节点到您的网络", "name": "节点名称", "namePlaceholder": "输入节点名称", "address": "节点地址", "addressPlaceholder": "输入节点地址", "port": "节点端口", "portPlaceholder": "输入节点端口", "usageRatio": "使用系数", "usageRatioPlaceholder": "输入使用系数", "maxLogs": "最大日志数", "maxLogsPlaceholder": "输入最大日志数", "connectionType": "连接类型", "keepAlive": "保持连接", "keepAliveDescription": "设置保持连接间隔", "days": "天", "minutes": "分钟", "seconds": "秒", "certificate": "证书", "certificatePlaceholder": "输入证书", "status": {"connected": "已连接", "error": "错误", "connecting": "连接中...", "disabled": "已禁用"}, "statusCheck": "检查状态", "statusChecking": "检查中...", "reconnect": "重新连接", "reconnecting": "重新连接中...", "sync": "同步", "syncing": "同步中...", "syncSuccess": "节点同步成功", "syncFailed": "节点同步失败", "gatherLogs": "收集日志", "gatherLogsDescription": "启用从此节点收集日志以进行监控和调试", "statusMessages": {"checkUnavailableForNew": "新节点无法进行状态检查。请先创建节点，然后检查其状态。"}, "onlineStats": {"button": "在线统计", "title": "在线用户统计", "ipListTitle": "{{username}} 的 IP 地址", "nodeInfo": "节点：{{nodeName}}", "searchPlaceholder": "输入用户名进行搜索...", "enterUsername": "请输入用户名", "refreshed": "数据刷新成功", "refreshFailed": "数据刷新失败", "backToStats": "返回统计", "ipAddresses": "IP 地址", "errorLoading": "加载用户统计时出错", "userNotOnline": "用户未在线", "searchUser": "搜索用户以查看其在线统计", "autoRefresh": "每 5 秒自动刷新", "connections": "连接", "protocols": "协议", "viewIPs": "查看 IP", "userNotFound": "用户 '{{username}}' 未找到或不在线", "errorLoadingIPs": "加载用户 IP 地址时出错"}, "hideDetails": "隐藏详情", "showDetails": "显示详情", "errorDetails": "错误详情", "connectionError": "连接错误", "retryConnection": "重试连接", "configurationError": "配置错误", "validateConfig": "验证配置", "fillRequiredFields": "请在检查状态之前填写所有必填字段", "apiKey": "API 密钥", "apiKeyPlaceholder": "请输入节点的 API 密钥", "generateUUID": "生成 UUID", "coreConfig": "核心配置", "hours": "小时", "selectCoreConfig": "选择核心配置", "noCoreConfig": "无核心配置"}, "theme": {"title": "主题", "description": "自定义您的应用主题", "light": "明亮", "dark": "深色", "system": "系统", "selectTheme": "选择主题", "themeSaved": "主题保存成功", "themeChanged": "主题更改成功", "visitThemePage": "访问主题页面进行进一步自定义", "radiusSaved": "边框圆角更新成功", "mode": "模式", "color": "颜色", "radius": "边框圆角", "zinc": "锌灰", "rose": "玫瑰", "blue": "蓝色", "green": "绿色", "violet": "紫色", "orange": "橙色", "yellow": "黄色", "default": "默认", "red": "红色", "modeDescription": "选择界面的显示方式", "colorDescription": "选择您喜欢的配色方案", "radiusDescription": "调整界面元素的圆角程度", "lightDescription": "明亮清新", "darkDescription": "护眼模式", "systemDescription": "跟随设备", "preview": "实时预览", "previewDescription": "实时查看您的主题设置效果", "dashboardPreview": "仪表板预览", "dashboardDescription": "监控系统性能和管理用户账户", "currentTheme": "当前主题", "sampleInput": "示例输入", "primaryButton": "主要按钮", "resetToDefaults": "重置为默认", "resetDescription": "将所有主题设置恢复为默认值", "resetting": "重置中...", "reset": "重置", "resetSuccess": "主题已重置为默认设置", "resetFailed": "重置主题设置失败", "radiusNone": "无", "radiusSmall": "小", "radiusMedium": "中", "radiusLarge": "大"}, "coreConfigModal": {"addConfig": "添加核心配置", "createNewConfig": "创建新的核心配置", "editCore": "编辑核心配置", "invalidJson": "无效的 JSON 配置", "createSuccess": "核心配置 \"{name}\" 已成功创建", "createFailed": "核心配置 \"{name}\" 创建失败", "editSuccess": "核心配置 \"{name}\" 已成功更新", "editFailed": "核心配置 \"{name}\" 更新失败", "keyPairGenerated": "密钥对生成成功", "shortIdGenerated": "短 ID 已成功生成", "shortId": "短 ID", "shortIdCopied": "短 ID 已复制到剪贴板", "publicKeyCopied": "公钥已复制到剪贴板", "privateKeyCopied": "私钥已复制到剪贴板", "jsonConfig": "JSON 配置", "editJson": "编辑 JSON 配置", "name": "名称", "namePlaceholder": "输入配置名称", "fallback": "备用项", "selectFallback": "选择备用项", "excludedInbound": "排除的入站", "selectInbound": "选择入站", "inbound": "入站", "generateKeyPair": "生成密钥对", "generateShortId": "生成短 ID", "publicKey": "公钥", "privateKey": "私钥", "clearAllFallbacks": "清除所有回退", "clearAllExcluded": "清除所有排除的入站", "copyPublicKey": "复制公钥", "copyPrivateKey": "复制私钥", "copyShortId": "复制短 ID", "restartNodes": "重启节点", "generateShadowsocksPassword": "生成 Shadowsocks 密码", "generatingShadowsocksPassword": "正在生成密码...", "shadowsocksPassword": "Shadowsocks 密码", "shadowsocksPasswordCopied": "密码已复制到剪贴板", "copyShadowsocksPassword": "复制密码", "selectEncryptionMethod": "选择加密方法", "regeneratePassword": "重新生成密码"}, "settings.cores.title": "核心", "settings.cores.description": "管理您的核心", "settings.cores.addCore": "添加核心", "settings.cores.noCores": "没有可用的核心。添加一个以开始。", "settings.cores.duplicateSuccess": "核心配置 «{{name}}» 复制成功", "settings.cores.duplicateFailed": "核心配置 «{{name}}» 复制失败", "settings.cores.deleteSuccess": "核心删除成功", "settings.cores.deleteFailed": "核心删除失败", "settings.cores.delete": "删除核心配置", "settings.cores.coreNotFound": "未找到核心", "toggle": "切换状态", "close": "关闭", "copy": "复制", "userDialog": {"deleteSuccess": "用户「{{name}}」已成功删除。", "resetUsageSuccess": "用户「{{name}}」的用量已重置。", "revokeSubSuccess": "用户「{{name}}」的订阅已撤销。", "activeNextPlanSuccess": "用户「{{name}}」的下一个套餐已激活。", "activeNextPlanError": "无法为用户「{{name}}」激活下一个计划。", "deleteConfirmTitle": "删除用户", "deleteConfirm": "您确定要删除用户「{{name}}」吗？", "nextPlanTitle": "下一个套餐", "nextPlanTemplateId": "模板ID", "nextPlanDataLimit": "流量限制 (GB)", "nextPlanExpire": "到期（天）", "nextPlanAddRemainingTraffic": "添加剩余流量", "selectTemplate": "选择模板", "selectTemplatePlaceholder": "选择模板", "selectNode": "选择节点", "allNodes": "所有节点", "activeNextPlan": "激活下一个套餐", "editError": "更新用户 «{{name}}» 失败", "createError": "创建用户 «{{name}}» 失败", "selectStatus": "选择状态", "selectedTemplates": "已选择 {{count}} 个模板"}, "sidebar.expand": "展开侧边栏", "sidebar.collapse": "折叠侧边栏", "usersTable.deleteUserTitle": "删除用户", "usersTable.deleteUserPrompt": "您确定要删除用户「{{name}}」吗？此操作无法撤销。", "usersTable.deleteSuccess": "用户「{{name}}」已成功删除。", "usersTable.deleteFailed": "无法删除用户「{{name}}」。{{error}}", "usersTable.delete": "删除", "usersTable.cancel": "取消", "statistics.trafficUsage": "流量使用", "statistics.trafficUsageDescription": "所有服务器的总流量使用情况", "statistics.usageDuringPeriod": "期间使用量", "statistics.users": "用户", "statistics.onlineUsers": "在线用户", "statistics.limitedUsers": "受限用户", "statistics.activeUsers": "活跃用户", "statistics.disabledUsers": "已禁用用户", "statistics.expiredUsers": "已过期用户", "statistics.onHoldUsers": "暂停用户", "statistics.allUsers": "全部用户", "statistics.serverTraffic": "服务器流量", "statistics.nodeTrafficDistribution": "节点流量分布", "statistics.noNodesAvailable": "没有可用节点", "statistics.noDataAvailable": "无数据", "statistics.noDataDescription": "选择时间范围以查看流量使用统计和趋势。", "statistics.noNodesDescription": "当前没有连接的节点来显示流量分布。", "statistics.loadingDescription": "正在获取最新统计数据...", "statistics.waitingForData": "等待数据", "statistics.waitingForDataDescription": "实时统计数据将在数据流开始后显示在这里。", "statistics.noDataInRange": "所选范围内无数据", "statistics.noDataInRangeDescription": "在所选时间段内未找到流量数据。请尝试选择不同的日期范围。", "statistics.selectNodeToView": "选择一个节点以查看详细统计信息", "statistics.userStatisticsDescription": "用户账户状态和活动概览", "errors.connectionFailed": "连接失败。请检查您的网络并重试。", "errors.statisticsLoadFailed": "加载统计数据失败", "usersTable.resetUsageTitle": "重置用户使用情况", "usersTable.resetUsagePrompt": "你确定要重置 «{{name}}» 的使用情况吗？", "usersTable.resetUsageSubmit": "重置使用情况", "usersTable.resetUsageSuccess": "用户「{{name}}」的使用情况已成功重置", "usersTable.resetUsageFailed": "重置用户「{{name}}」的使用情况失败", "time": {"expires": "到期", "expired": "已过期", "year": "年", "years": "年", "month": "月", "months": "月", "day": "天", "days": "天", "hour": "小时", "hours": "小时", "min": "分钟", "mins": "分钟", "ago": "前", "notConnectedYet": "尚未连接"}, "node.xrayVersion": "核心版本", "node.coreVersion": "节点版本", "calendar": {"selectRange": "选择时间范围", "startDate": "开始日期", "endDate": "结束日期"}, "usersTable": {"usageChart": "用量图表", "noUsageData": "此期间没有可用数据。", "tryDifferentRange": "请尝试其他时间范围。", "trendingUp": "上升", "trendingDown": "下降", "usageSummary": "显示所选期间的总用量。"}, "setOwnerModal": {"title": "设置所有者", "currentOwner": "当前所有者:", "none": "无", "selectAdmin": "选择新所有者", "confirm": "设置所有者", "success": "用户 {{username}} 的所有者已成功更改为 {{admin}}。", "error": "无法将用户 {{username}} 的所有者更改为 {{admin}}。", "loadError": "加载管理员列表失败。"}, "online": "在线", "loading": "加载中...", "noResults": "未找到结果", "emptyState": {"noUsers": {"title": "暂无用户", "description": "开始创建您的第一个用户账户", "createFirstUser": "创建第一个用户"}}, "inboundTags": "入站标签", "searchInbounds": "搜索入站...", "noInboundsFound": "未找到入站", "resilientNodeGroups": {"title": "Resilient Node Groups", "description": "Manage resilient node groups for improved reliability", "addGroup": "Add Group", "createGroup": "Create Resilient Node Group", "editGroup": "Edit Resilient Node Group", "name": "Name", "strategy": "Client Strategy", "nodes": "Nodes", "selectNodes": "Select nodes", "createSuccess": "Resilient node group created successfully", "updateSuccess": "Resilient node group updated successfully", "deleteSuccess": "Resilient node group deleted successfully", "operationFailed": "Failed to perform operation on resilient node group", "deleteFailed": "Failed to delete resilient node group", "strategies": {"urlTest": "URL Test", "fallback": "Fallback", "loadBalance": "Load <PERSON>", "clientDefault": "<PERSON><PERSON>", "none": "None"}, "clientDefault": "<PERSON><PERSON>"}, "hiddifyImport": {"title": "Import Users from Hiddify", "selectFile": "Select Hiddify Backup File", "fileSelected": "File selected: {{filename}}", "invalidFile": "Invalid File", "invalidFileDesc": "Please select a valid JSON file.", "unlimitedExpiration": "Set unlimited expiration for all users", "unlimitedExpirationDesc": "If enabled, all imported users will have unlimited expiration (expire = 0), ignoring package_days from Hiddify.", "smartUsernameParsing": "Enable smart username & note parsing", "smartUsernameParsingDesc": "If enabled, names like \"1234 John <PERSON>\" will be split: \"1234\" as username, \"<PERSON>\" as note. Otherwise, the full name will be used as username.", "protocolSelection": "Select Protocols", "protocolSelectionDesc": "Choose which protocols the imported users should have access to.", "noFileSelected": "No File Selected", "noProtocolsSelected": "No Protocols Selected", "importing": "Importing users...", "importUsers": "Import Users", "importComplete": "Import Complete", "importStats": "{{successful}} users imported successfully, {{failed}} failed.", "importSuccess": "Import Successful", "importSuccessDesc": "{{successful}} users imported successfully. {{failed}} users failed to import.", "importWarning": "Import Completed with Warnings", "importWarningDesc": "{{failed}} users failed to import. Check the details below.", "importError": "Import Failed", "importErrorDesc": "An error occurred during import. Please try again."}}
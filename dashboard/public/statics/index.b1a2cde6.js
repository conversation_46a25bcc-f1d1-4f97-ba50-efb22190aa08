import{i as Bt,B as Co,a as _o,b as ko,j as Hr,d as G,U as Qt,z as Io,r as vo,f as Do,Q as zo,e as Uo,$ as Eo,c as At,g as C,h as Ro,C as Ao,k as Lt,u as Tt,l as B,m as r,H as _,n as e,T as c,o as Lo,p as v,s as To,q as g,t as Ke,J as Mo,v as y,w as jr,A as Mt,x as No,y as Po,M as ie,D as ae,E as le,F as ce,G as de,I as ye,K as Ge,L as Br,N as Fo,O as bt,P as he,R as A,S as j,V as $r,W as ne,X as L,Y as se,Z as Y,_ as We,a0 as ke,a1 as N,a2 as St,a3 as pt,a4 as Wo,a5 as Ne,a6 as _e,a7 as Oo,a8 as Vr,a9 as qt,aa as Be,ab as Dt,ac as Ho,ad as He,ae as zt,af as mr,ag as jo,ah as Gr,ai as Yr,aj as Zr,ak as xe,al as Bo,am as $o,an as Vo,ao as Go,ap as Yo,aq as Xt,ar as Qr,as as qr,at as Xr,au as me,av as gr,aw as Zo,ax as Qo,ay as qo,az as $t,aA as Xo,aB as Jo,aC as Ko,aD as en,aE as tn,aF as rn,aG as xt,aH as on,aI as nn,aJ as sn,aK as an,aL as ln,aM as et,aN as Jt,aO as wt,aP as O,aQ as Kt,aR as cn,aS as Qe,aT as qe,aU as Vt,aV as Xe,aW as dn,aX as De,aY as ze,aZ as Ue,a_ as Ee,a$ as Re,b0 as Ae,b1 as fr,b2 as hn,b3 as Ut,b4 as Pe,b5 as un,b6 as Jr,b7 as er,b8 as Kr,b9 as eo,ba as X,bb as pn,bc as Ve,bd as Je,be as tr,bf as mn,bg as gn,bh as fn,bi as to,bj as bn,bk as ro,bl as xn,bm as yn,bn as oo,bo as je,bp as Sn,bq as wn,br as Cn,bs as Pt,bt as br,bu as no,bv as _n,bw as kn,bx as xr,by as In,bz as yr,bA as so,bB as vn,bC as io,bD as Dn,bE as zn,bF as ao,bG as Un,bH as En,bI as lo,bJ as Ft,bK as Rn,bL as An,bM as Ln,bN as Tn,bO as Mn,bP as Nn,bQ as co,bR as Pn,bS as Sr,bT as wr,bU as Ze,bV as Te,bW as Cr,bX as Ce,bY as _r,bZ as Fn,b_ as Wn,b$ as On,c0 as Hn,c1 as jn,c2 as Bn,c3 as $n,c4 as Vn,c5 as Gn,c6 as Yn,c7 as Zn,c8 as Qn,c9 as qn,ca as Xn,cb as Jn,cc as Kn,cd as es}from"./vendor.4e1cd858.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))s(a);new MutationObserver(a=>{for(const i of a)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&s(l)}).observe(document,{childList:!0,subtree:!0});function o(a){const i={};return a.integrity&&(i.integrity=a.integrity),a.referrerpolicy&&(i.referrerPolicy=a.referrerpolicy),a.crossorigin==="use-credentials"?i.credentials="include":a.crossorigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(a){if(a.ep)return;a.ep=!0;const i=o(a);fetch(a.href,i)}})();Bt.use(Co).use(_o).use(ko).init({debug:{}.NODE_ENV==="development",returnNull:!1,fallbackLng:"en",interpolation:{escapeValue:!1},react:{useSuspense:!1},load:"languageOnly",detection:{caches:["localStorage","sessionStorage","cookie"]},backend:{loadPath:Hr(["/","statics/locales/{{lng}}.json"])}},function(t,n){G.locale(Bt.language)});Bt.on("languageChanged",t=>{G.locale(t)});Qt("zh-cn",Io);Qt("ru",vo);Qt("fa",Do);const Gt=new zo,ho=t=>{const n=document.querySelector('meta[name="theme-color"]');n==null||n.setAttribute("content",t=="dark"?"#1A202C":"#3B81F6")},ts=Uo({shadows:{outline:"0 0 0 2px var(--chakra-colors-primary-200)"},fonts:{body:"Inter,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif"},colors:{"light-border":"#d2d2d4",primary:{50:"#9cb7f2",100:"#88a9ef",200:"#749aec",300:"#618ce9",400:"#4d7de7",500:"#396fe4",600:"#3364cd",700:"#2e59b6",800:"#284ea0",900:"#224389"},gray:{750:"#222C3B"}},components:{Alert:{baseStyle:{container:{borderRadius:"6px",fontSize:"sm"}}},Select:{baseStyle:{field:{_dark:{borderColor:"gray.600",borderRadius:"6px"},_light:{borderRadius:"6px"}}}},FormHelperText:{baseStyle:{fontSize:"xs"}},FormLabel:{baseStyle:{fontSize:"sm",fontWeight:"medium",mb:"1",_dark:{color:"gray.300"}}},Input:{baseStyle:{addon:{_dark:{borderColor:"gray.600",_placeholder:{color:"gray.500"}}},field:{_focusVisible:{boxShadow:"none",borderColor:"primary.200",outlineColor:"primary.200"},_dark:{borderColor:"gray.600",_disabled:{color:"gray.400",borderColor:"gray.500"},_placeholder:{color:"gray.500"}}}}},Table:{baseStyle:{table:{borderCollapse:"separate",borderSpacing:0},thead:{borderBottomColor:"light-border"},th:{background:"#F9FAFB",borderColor:"light-border !important",borderBottomColor:"light-border !important",borderTop:"1px solid ",borderTopColor:"light-border !important",_first:{borderLeft:"1px solid",borderColor:"light-border !important"},_last:{borderRight:"1px solid",borderColor:"light-border !important"},_dark:{borderColor:"gray.600 !important",background:"gray.750"}},td:{transition:"all .1s ease-out",borderColor:"light-border",borderBottomColor:"light-border !important",_first:{borderLeft:"1px solid",borderColor:"light-border",_dark:{borderColor:"gray.600"}},_last:{borderRight:"1px solid",borderColor:"light-border",_dark:{borderColor:"gray.600"}},_dark:{borderColor:"gray.600",borderBottomColor:"gray.600 !important"}},tr:{"&.interactive":{cursor:"pointer",_hover:{"& > td":{bg:"gray.200"},_dark:{"& > td":{bg:"gray.750"}}}},_last:{"& > td":{_first:{borderBottomLeftRadius:"8px"},_last:{borderBottomRightRadius:"8px"}}}}}}}});const Et=()=>localStorage.getItem("token"),rs=t=>{localStorage.setItem("token",t)},os=()=>{localStorage.removeItem("token")},ns=Eo.create({baseURL:"/api/"}),ss=(t,n={})=>(Et()&&(n.headers={...(n==null?void 0:n.headers)||{},Authorization:`Bearer ${Et()}`}),ns(t,n)),W=ss,is=At(t=>({isLoading:!0,isPostLoading:!1,version:null,started:!1,logs_websocket:null,config:"",fetchCoreSettings:()=>{t({isLoading:!0}),Promise.all([W("/core").then(({version:n,started:o,logs_websocket:s})=>t({version:n,started:o,logs_websocket:s})),W("/core/config").then(n=>t({config:n}))]).finally(()=>t({isLoading:!1}))},updateConfig:n=>(t({isPostLoading:!0}),W("/core/config",{method:"PUT",body:n}).finally(()=>{t({isPostLoading:!1})})),restartCore:()=>W("/core/restart",{method:"POST"})}));function pe(t,n=2,o=!1){if(!+t)return"0 B";const s=1024,a=n<0?0:n,i=["B","KB","MB","GB","TB","PB","EB","ZB","YB"],l=Math.floor(Math.log(t)/Math.log(s));return o?[parseFloat((t/Math.pow(s,l)).toFixed(a)),i[l]]:`${parseFloat((t/Math.pow(s,l)).toFixed(a))} ${i[l]}`}const kr=t=>{if(t!==null)return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")},as=C(Ro,{baseStyle:{w:5,h:5,position:"relative",zIndex:"2"}}),ls=C(Ao,{baseStyle:{w:5,h:5,position:"relative",zIndex:"2"}}),cs=C(Lt,{baseStyle:{w:5,h:5,position:"relative",zIndex:"2"}}),Wt=({title:t,content:n,icon:o})=>r(Lo,{p:6,borderWidth:"1px",borderColor:"light-border",bg:"#F9FAFB",_dark:{borderColor:"gray.600",bg:"gray.750"},borderStyle:"solid",boxShadow:"none",borderRadius:"12px",width:"full",display:"flex",justifyContent:"space-between",flexDirection:"row",children:[r(_,{alignItems:"center",columnGap:"4",children:[e(v,{p:"2",position:"relative",color:"white",_before:{content:'""',position:"absolute",top:0,left:0,bg:"primary.400",display:"block",w:"full",h:"full",borderRadius:"5px",opacity:".5",z:"1"},_after:{content:'""',position:"absolute",top:"-5px",left:"-5px",bg:"primary.400",display:"block",w:"calc(100% + 10px)",h:"calc(100% + 10px)",borderRadius:"8px",opacity:".4",z:"1"},children:o}),e(c,{color:"gray.600",_dark:{color:"gray.300"},fontWeight:"medium",textTransform:"capitalize",fontSize:"sm",children:t})]}),e(v,{fontSize:"3xl",fontWeight:"semibold",mt:"2",children:n})]}),Yt="statistics-query-key",ds=t=>{const{version:n}=D(),{data:o}=Tt({queryKey:Yt,queryFn:()=>W("/system"),refetchInterval:5e3,onSuccess:({version:a})=>{n!==a&&D.setState({version:a})}}),{t:s}=B();return r(_,{justifyContent:"space-between",gap:0,columnGap:{lg:4,md:0},rowGap:{lg:0,base:4},display:"flex",flexDirection:{lg:"row",base:"column"},...t,children:[e(Wt,{title:s("activeUsers"),content:o&&r(_,{alignItems:"flex-end",children:[e(c,{children:kr(o.users_active)}),r(c,{fontWeight:"normal",fontSize:"lg",as:"span",display:"inline-block",pb:"5px",children:["/ ",kr(o.total_user)]})]}),icon:e(as,{})}),e(Wt,{title:s("dataUsage"),content:o&&pe(o.incoming_bandwidth+o.outgoing_bandwidth),icon:e(ls,{})}),e(Wt,{title:s("memoryUsage"),content:o&&r(_,{alignItems:"flex-end",children:[e(c,{children:pe(o.mem_used,1,!0)[0]}),r(c,{fontWeight:"normal",fontSize:"lg",as:"span",display:"inline-block",pb:"5px",children:[pe(o.mem_used,1,!0)[1]," /"," ",pe(o.mem_total,1)]})]}),icon:e(cs,{})})]})},uo="marzban-num-users-per-page",Ir=10,hs=()=>{const t=localStorage.getItem(uo)||Ir.toString();return parseInt(t)||Ir},us=t=>localStorage.setItem(uo,t),ps=t=>{for(const n in t)t[n]||delete t[n];return D.setState({loading:!0}),W("/users",{query:t}).then(n=>(D.setState({users:n}),n)).finally(()=>{D.setState({loading:!1})})},ms=()=>W("/inbounds").then(t=>{D.setState({inbounds:new Map(Object.entries(t))})}).finally(()=>{D.setState({loading:!1})}),D=At(To((t,n)=>({version:null,editingUser:null,deletingUser:null,isCreatingNewUser:!1,QRcodeLinks:null,subscribeUrl:null,users:{users:[],total:0},loading:!0,isResetingAllUsage:!1,isEditingHosts:!1,isEditingNodes:!1,isShowingNodesUsage:!1,resetUsageUser:null,revokeSubscriptionUser:null,isImportingHiddifyUsers:!1,filters:{username:"",limit:hs(),sort:"-created_at"},inbounds:new Map,isEditingCore:!1,refetchUsers:()=>{ps(n().filters)},resetAllUsage:()=>W("/users/reset",{method:"POST"}).then(()=>{n().onResetAllUsage(!1),n().refetchUsers()}),onResetAllUsage:o=>t({isResetingAllUsage:o}),onImportHiddifyUsers:o=>{t({isImportingHiddifyUsers:o})},onCreateUser:o=>t({isCreatingNewUser:o}),onEditingUser:o=>{t({editingUser:o})},onDeletingUser:o=>{t({deletingUser:o})},onFilterChange:o=>{t({filters:{...n().filters,...o}}),n().refetchUsers()},setQRCode:o=>{t({QRcodeLinks:o})},deleteUser:o=>(t({editingUser:null}),W(`/user/${o.username}`,{method:"DELETE"}).then(()=>{t({deletingUser:null}),n().refetchUsers(),Gt.invalidateQueries(Yt)})),createUser:o=>W("/user",{method:"POST",body:o}).then(()=>{t({editingUser:null}),n().refetchUsers(),Gt.invalidateQueries(Yt)}),editUser:o=>W(`/user/${o.username}`,{method:"PUT",body:o}).then(()=>{n().onEditingUser(null),n().refetchUsers()}),fetchUserUsage:(o,s)=>{for(const a in s)s[a]||delete s[a];return W(`/user/${o.username}/usage`,{method:"GET",query:s})},onEditingHosts:o=>{t({isEditingHosts:o})},onEditingNodes:o=>{t({isEditingNodes:o})},onShowingNodesUsage:o=>{t({isShowingNodesUsage:o})},setSubLink:o=>{t({subscribeUrl:o})},resetDataUsage:o=>W(`/user/${o.username}/reset`,{method:"POST"}).then(()=>{t({resetUsageUser:null}),n().refetchUsers()}),revokeSubscription:o=>W(`/user/${o.username}/revoke_sub`,{method:"POST"}).then(s=>{t({revokeSubscriptionUser:null,editingUser:s}),n().refetchUsers()})}))),ge=({children:t,color:n})=>e(v,{position:"relative",width:"36px",height:"36px",display:"flex",justifyContent:"center",alignItems:"center",_before:{content:'""',display:"block",position:"absolute",top:"0",left:"0",width:"calc(100%)",height:"calc(100%)",bg:`${n}.400`,opacity:".5",borderRadius:"5px",zIndex:"1",_dark:{bg:`${n}.400`}},_after:{content:'""',display:"block",position:"absolute",top:"0",left:"0",width:"calc(100% + 10px)",height:"calc(100% + 10px)",transform:"translate(-5px, -5px)",bg:`${n}.400`,opacity:".4",borderRadius:"8px",zIndex:"1",_dark:{bg:`${n}.400`}},children:e(c,{color:`${n}.500`,_dark:{color:`${n}.900`},position:"relative",zIndex:"2",children:t})});window.ace.define("ace/theme/nord_dark",["require","exports","module","ace/lib/dom"],(t,n,o)=>{n.isDark=!0,n.cssClass="ace-nord-dark",t("../lib/dom").importCssString(n.cssText,n.cssClass)});window.ace.define("ace/theme/dawn",["require","exports","module","ace/lib/dom"],(t,n,o)=>{n.isDark=!1,n.cssClass="ace-dawn",t("../lib/dom").importCssString(n.cssText,n.cssClass)});const gs=g.exports.forwardRef(({json:t,onChange:n,mode:o="code"},s)=>{const{colorMode:a}=Ke(),i={mode:o,onChangeText:n,statusBar:!1,mainMenuBar:!1,theme:a==="dark"?"ace/theme/nord_dark":"ace/theme/dawn"},l=g.exports.useRef(null),p=g.exports.useRef(null);return g.exports.useEffect(()=>(p.current=new Mo(l.current,i),()=>{p.current&&p.current.destroy()}),[]),g.exports.useEffect(()=>{p.current&&p.current.update(t)},[t]),e(v,{ref:s,border:"1px solid",borderColor:"gray.300",_dark:{borderColor:"gray.500"},borderRadius:5,h:"full",children:e(v,{height:"full",ref:l})})}),po=y.object({name:y.string().min(1),address:y.string().min(1),port:y.number().min(1).or(y.string().transform(t=>parseFloat(t))),api_port:y.number().min(1).or(y.string().transform(t=>parseFloat(t))),xray_version:y.string().nullable().optional(),id:y.number().nullable().optional(),status:y.enum(["connected","connecting","error","disabled"]).nullable().optional(),message:y.string().nullable().optional(),add_as_new_host:y.boolean().optional(),usage_coefficient:y.number().or(y.string().transform(t=>parseFloat(t)))}),fs=()=>({name:"",address:"",port:62050,api_port:62051,xray_version:"",usage_coefficient:1}),yt="fetch-nodes-query-key",mo=()=>{const{isEditingNodes:t}=D();return Tt({queryKey:yt,queryFn:Ct.getState().fetchNodes,refetchInterval:t?3e3:void 0,refetchOnWindowFocus:!1})},Ct=At((t,n)=>({nodes:[],addNode(o){return W("/node",{method:"POST",body:o})},fetchNodes(){return W("/nodes")},fetchNodesUsage(o){return W("/nodes/usage",{query:o})},updateNode(o){return W(`/node/${o.id}`,{method:"PUT",body:o})},setDeletingNode(o){t({deletingNode:o})},reconnectNode(o){return W(`/node/${o.id}/reconnect`,{method:"POST"})},deleteNode:()=>{var o;return W(`/node/${(o=n().deletingNode)==null?void 0:o.id}`,{method:"DELETE"})}})),vr=500,bs=C(jr,{baseStyle:{w:5,h:5}}),xs=C(Mt,{baseStyle:{w:4,h:4}}),ys=C(No,{baseStyle:{w:4,h:4}}),Ss=C(Po,{baseStyle:{w:3,h:3}}),ws=t=>({[pt.ReadyState.CONNECTING]:"connecting",[pt.ReadyState.OPEN]:"connected",[pt.ReadyState.CLOSING]:"closed",[pt.ReadyState.CLOSED]:"closed",[pt.ReadyState.UNINSTANTIATED]:"closed"})[t],Cs=t=>{try{let n=new URL("/api/".startsWith("/")?window.location.origin+"/api/":"/api/");return(n.protocol==="https:"?"wss://":"ws://")+Hr([n.host+n.pathname,t?`/node/${t}/logs`:"/core/logs"])+"?interval=1&token="+Et()}catch(n){return console.error("Unable to generate websocket url"),console.error(n),null}};let Oe=[];const _s=()=>{const{colorMode:t}=Ke(),{data:n}=mo(),o=!1,[s,a]=g.exports.useState(""),i=(M,te)=>{M!==s&&(M==="host"?(a(""),T([])):(a(M),T([])))},{isEditingCore:l}=D(),{fetchCoreSettings:p,updateConfig:f,isLoading:w,config:S,isPostLoading:x,version:h,restartCore:m}=is(),I=g.exports.useRef(null),[b,T]=g.exports.useState([]),{t:u}=B(),d=ye(),H=Ge({defaultValues:{config:S||{}}});g.exports.useEffect(()=>{S&&H.setValue("config",S)},[S]),g.exports.useEffect(()=>{l&&p()},[l]);const $=g.exports.useRef(!0),z=g.exports.useCallback(Br(M=>{var fe,ee,Ie;const te=Math.abs((((fe=I.current)==null?void 0:fe.scrollTop)||0)-(((ee=I.current)==null?void 0:ee.scrollHeight)||0)+(((Ie=I.current)==null?void 0:Ie.offsetHeight)||0))<10;I.current&&te?$.current=!0:$.current=!1,M.length<40&&T(M)},300),[]),{readyState:P}=Fo(Cs(s),{onMessage:M=>{Oe.push(M.data),Oe.length>vr&&(Oe=Oe.splice(0,Oe.length-vr)),z([...Oe])},shouldReconnect:()=>!0,reconnectAttempts:10,reconnectInterval:1e3});g.exports.useEffect(()=>{var M;I.current&&$.current&&(I.current.scrollTop=(M=I.current)==null?void 0:M.scrollHeight)},[b]),g.exports.useEffect(()=>()=>{Oe=[]},[]);const Z=ws(P.toString()),{mutate:U,isLoading:E}=bt(m),Q=({config:M})=>{f(M).then(()=>{d({title:u("core.successMessage"),status:"success",isClosable:!0,position:"top",duration:3e3})}).catch(te=>{let fe=u("core.generalErrorMessage");typeof te.response._data.detail=="object"&&(fe=te.response._data.detail[Object.keys(te.response._data.detail)[0]]),typeof te.response._data.detail=="string"&&(fe=te.response._data.detail),d({title:fe,status:"error",isClosable:!0,position:"top",duration:3e3})})},J=g.exports.useRef(null),[R,V]=g.exports.useState(!1),K=()=>{var M;document.fullscreenElement?(document.exitFullscreen(),V(!1)):((M=J.current)==null||M.requestFullscreen(),V(!0))};return r("form",{onSubmit:H.handleSubmit(Q),children:[r(he,{children:[r(A,{children:[r(_,{justifyContent:"space-between",alignItems:"flex-start",children:[r(j,{children:[u("core.configuration")," ",w&&e($r,{isIndeterminate:!0,size:"15px"})]}),e(_,{gap:0,children:e(ne,{label:"Xray Version",placement:"top",children:e(L,{height:"100%",textTransform:"lowercase",children:h&&`v${h}`})})})]}),r(v,{position:"relative",ref:J,minHeight:"300px",children:[e(se,{control:H.control,name:"config",render:({field:M})=>e(gs,{json:S,onChange:M.onChange})}),e(Y,{size:"xs","aria-label":"full screen",variant:"ghost",position:"absolute",top:"2",right:"4",onClick:K,children:R?e(Ss,{}):e(ys,{})})]})]}),r(A,{mt:"4",children:[r(_,{justifyContent:"space-between",style:{paddingBottom:"1rem"},children:[r(_,{children:[(n==null?void 0:n[0])&&r(We,{size:"sm",style:{width:"auto"},disabled:o,bg:"transparent",_dark:{bg:"transparent"},sx:{option:{backgroundColor:t==="dark"?"#222C3B":"white"}},onChange:M=>i(M.currentTarget.value,M.currentTarget.selectedOptions[0].text),children:[e("option",{value:"host",defaultChecked:!0,children:"Master"},"host"),n&&n.map(M=>e("option",{value:String(M.id),children:u(M.name)},M.address))]}),e(j,{className:"w-au",children:u("core.logs")})]}),e(c,{as:j,children:u(`core.socket.${Z}`)})]}),e(v,{border:"1px solid",borderColor:"gray.300",bg:"#F9F9F9",_dark:{borderColor:"gray.500",bg:"#2e3440"},borderRadius:5,minHeight:"200px",maxHeight:"250px",p:2,overflowY:"auto",ref:I,children:b.map((M,te)=>e(c,{fontSize:"xs",opacity:.8,whiteSpace:"pre-line",children:M},te))})]})]}),e(ke,{children:r(_,{w:"full",justifyContent:"space-between",children:[e(_,{children:e(v,{children:e(N,{size:"sm",leftIcon:e(xs,{className:St({"animate-spin":E})}),onClick:()=>U(),children:u(E?"core.restarting":"core.restartCore")})})}),e(_,{children:e(N,{size:"sm",variant:"solid",colorScheme:"primary",px:"5",type:"submit",isDisabled:w||x,isLoading:x,children:u("core.save")})})]})})]})},ks=()=>{const{isEditingCore:t}=D(),n=D.setState.bind(null,{isEditingCore:!1}),{t:o}=B();return r(ie,{isOpen:t,onClose:n,size:"3xl",children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",w:"full",children:[e(ce,{pt:6,children:r(_,{gap:2,children:[e(ge,{color:"primary",children:e(bs,{color:"white"})}),e(c,{fontWeight:"semibold",fontSize:"lg",children:o("core.title")})]})}),e(de,{mt:3}),e(_s,{})]})]})},_t=C(Wo,{baseStyle:{w:5,h:5}}),Is=()=>{const[t,n]=g.exports.useState(!1),{deletingUser:o,onDeletingUser:s,deleteUser:a}=D(),{t:i}=B(),l=ye(),p=()=>{s(null)},f=()=>{o&&(n(!0),a(o).then(()=>{l({title:i("deleteUser.deleteSuccess",{username:o.username}),status:"success",isClosable:!0,position:"top",duration:3e3})}).then(p).finally(n.bind(null,!1)))};return r(ie,{isCentered:!0,isOpen:!!o,onClose:p,size:"sm",children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",children:[e(ce,{pt:6,children:e(ge,{color:"red",children:e(_t,{})})}),e(de,{mt:3}),r(he,{children:[e(c,{fontWeight:"semibold",fontSize:"lg",children:i("deleteUser.title")}),o&&e(c,{mt:1,fontSize:"sm",_dark:{color:"gray.400"},color:"gray.600",children:e(Ne,{components:{b:e("b",{})},children:i("deleteUser.prompt",{username:o.username})})})]}),r(ke,{display:"flex",children:[e(N,{size:"sm",onClick:p,mr:3,w:"full",variant:"outline",children:i("cancel")}),e(N,{size:"sm",w:"full",colorScheme:"red",onClick:f,leftIcon:t?e(_e,{size:"xs"}):void 0,children:i("delete")})]})]})]})},rr={baseStyle:{w:4,h:4}},vs=C(Oo,rr),Ds=C(Vr,rr),or=C(Mt,rr),zs=Br(t=>{D.getState().onFilterChange({...D.getState().filters,offset:0,search:t})},300),Us=({...t})=>{const{loading:n,filters:o,onFilterChange:s,refetchUsers:a,onCreateUser:i}=D(),{t:l}=B(),[p,f]=g.exports.useState(""),w=x=>{f(x.target.value),zs(x.target.value)},S=()=>{f(""),s({...o,offset:0,search:""})};return r(qt,{id:"filters",templateColumns:{lg:"repeat(3, 1fr)",md:"repeat(4, 1fr)",base:"repeat(1, 1fr)"},position:"sticky",top:0,mx:"-6",px:"6",rowGap:4,gap:{lg:4,base:0},bg:"var(--chakra-colors-chakra-body-bg)",py:4,zIndex:"docked",...t,children:[e(Be,{colSpan:{base:1,md:2,lg:1},order:{base:2,md:1},children:r(Dt,{children:[e(Ho,{pointerEvents:"none",children:e(vs,{})}),e(He,{placeholder:l("search"),value:p,borderColor:"light-border",onChange:w}),r(zt,{children:[n&&e(_e,{size:"xs"}),o.search&&o.search.length>0&&e(Y,{onClick:S,"aria-label":"clear",size:"xs",variant:"ghost",children:e(Ds,{})})]})]})}),e(Be,{colSpan:2,order:{base:1,md:2},children:r(_,{justifyContent:"flex-end",alignItems:"center",h:"full",children:[e(Y,{"aria-label":"refresh users",disabled:n,onClick:a,size:"sm",variant:"outline",children:e(or,{className:St({"animate-spin":n})})}),e(N,{colorScheme:"primary",size:"sm",onClick:()=>i(!0),px:5,children:l("createUser")})]})})]})},go="https://github.com/Gozargah/Marzban",Es="https://github.com/Gozargah",Rs="https://github.com/Gozargah/Marzban#donation",fo=t=>{const{version:n}=D();return e(_,{w:"full",py:"0",position:"relative",...t,children:r(c,{display:"inline-block",flexGrow:1,textAlign:"center",color:"gray.500",fontSize:"xs",children:[e(mr,{color:"blue.400",href:go,children:"Marzban"}),n?` (v${n}), `:", ","Made with \u2764\uFE0F in"," ",e(mr,{color:"blue.400",href:Es,children:"Gozargah"})]})})},As=C(jo,{baseStyle:{w:4,h:4}}),bo=({actions:t})=>{const{i18n:n}=B();var o=s=>{n.changeLanguage(s)};return r(Gr,{placement:"bottom-end",children:[e(Yr,{as:Y,size:"sm",variant:"outline",icon:e(As,{}),position:"relative"}),r(Zr,{minW:"100px",zIndex:9999,children:[e(xe,{maxW:"100px",fontSize:"sm",onClick:()=>o("en"),children:"English"}),e(xe,{maxW:"100px",fontSize:"sm",onClick:()=>o("fa"),children:"\u0641\u0627\u0631\u0633\u06CC"}),e(xe,{maxW:"100px",fontSize:"sm",onClick:()=>o("zh-cn"),children:"\u7B80\u4F53\u4E2D\u6587"}),e(xe,{maxW:"100px",fontSize:"sm",onClick:()=>o("ru"),children:"\u0420\u0443\u0441\u0441\u043A\u0438\u0439"})]})]})},Ls=async()=>await W("/admin"),Ts=()=>{const{data:t,isError:n,isLoading:o,isSuccess:s,error:a}=Tt({queryFn:()=>Ls()});return{userData:t||{discord_webook:"",is_sudo:!1,telegram_id:"",username:""},getUserIsPending:o,getUserIsSuccess:s,getUserIsError:n,getUserError:a}},Se={baseStyle:{w:4,h:4}},Ms=C(Bo,Se),Ns=C($o,Se),Ps=C(jr,Se),Fs=C(Vo,Se),Ws=C(Go,Se),Os=C(Yo,Se),Hs=C(Xt,Se),js=C(Qr,Se),Bs=C(Lt,Se),$s=C(qr,Se),Vs=C(Xr,Se),Dr=C(v,{baseStyle:{bg:"yellow.500",w:"2",h:"2",rounded:"full",position:"absolute"}}),xo="marzban-menu-notification",Gs=()=>{const t=localStorage.getItem(xo);if(!t)return!0;try{return t&&Qo(parseInt(t))?qo(new Date,new Date(parseInt(t)))>=7:!0}catch{return!0}},Ys=({actions:t})=>{const{userData:n,getUserIsSuccess:o,getUserIsPending:s}=Ts(),a=()=>!s&&o?n.is_sudo:!1,{onEditingHosts:i,onResetAllUsage:l,onEditingNodes:p,onShowingNodesUsage:f,onImportHiddifyUsers:w}=D(),{t:S}=B(),{colorMode:x,toggleColorMode:h}=Ke(),[m,I]=g.exports.useState(Gs()),b=x==="dark"?"dark_dimmed":x,T=()=>{localStorage.setItem(xo,new Date().getTime().toString()),I(!1)};return r(_,{gap:2,justifyContent:"space-between",__css:{"& .menuList":{direction:"ltr"}},position:"relative",children:[e(c,{as:"h1",fontWeight:"semibold",fontSize:"2xl",children:S("users")}),m&&e(Dr,{top:"0",right:"0",zIndex:9999}),e(v,{overflow:"auto",css:{direction:"rtl"},children:r(_,{alignItems:"center",children:[r(Gr,{children:[e(Yr,{as:Y,size:"sm",variant:"outline",icon:e(me,{children:e(Fs,{})}),position:"relative"}),r(Zr,{minW:"170px",zIndex:99999,className:"menuList",children:[a()&&r(me,{children:[e(xe,{maxW:"170px",fontSize:"sm",icon:e(Hs,{}),onClick:i.bind(null,!0),children:S("header.hostSettings")}),e(xe,{maxW:"170px",fontSize:"sm",icon:e(js,{}),onClick:p.bind(null,!0),children:S("header.nodeSettings")}),e(xe,{maxW:"170px",fontSize:"sm",icon:e(Bs,{}),onClick:f.bind(null,!0),children:S("header.nodesUsage")}),e(xe,{maxW:"170px",fontSize:"sm",icon:e($s,{}),onClick:l.bind(null,!0),children:S("resetAllUsage")}),e(xe,{maxW:"170px",fontSize:"sm",icon:e(Vs,{}),onClick:()=>{w(!0)},children:S("header.hiddifyImport")})]}),e(gr,{to:Rs,target:"_blank",children:r(xe,{maxW:"170px",fontSize:"sm",icon:e(Os,{}),position:"relative",onClick:T,children:[S("header.donation")," ",m&&e(Dr,{top:"3",right:"2"})]})}),e(gr,{to:"/login",children:e(xe,{maxW:"170px",fontSize:"sm",icon:e(Ws,{}),children:S("header.logout")})})]})]}),a()&&e(Y,{size:"sm",variant:"outline","aria-label":"core settings",onClick:()=>{D.setState({isEditingCore:!0})},children:e(Ps,{})}),e(bo,{}),e(Y,{size:"sm",variant:"outline","aria-label":"switch theme",onClick:()=>{ho(x=="dark"?"light":"dark"),h()},children:x==="light"?e(Ms,{}):e(Ns,{})}),e(v,{css:{direction:"ltr"},display:"flex",alignItems:"center",pr:"2",__css:{"&  span":{display:"inline-flex"}},children:e(Zo,{href:go,"data-color-scheme":`no-preference: ${b}; light: ${b}; dark: ${b};`,"data-size":"large","data-show-count":"true","aria-label":"Star Marzban on GitHub",children:"Star"})})]})})]})},Zs=[{title:"Inbound's default",value:"inbound_default"},{title:"TLS",value:"tls"},{title:"None",value:"none"}],Qs=[{title:"",value:""},{title:"h3",value:"h3"},{title:"h2",value:"h2"},{title:"http/1.1",value:"http/1.1"},{title:"h3,h2,http/1.1",value:"h3,h2,http/1.1"},{title:"h3,h2",value:"h3,h2"},{title:"h2,http/1.1",value:"h2,http/1.1"}],qs=[{title:"",value:""},...["chrome","firefox","safari","ios","android","edge","360","qq","random","randomized"].map(t=>({title:t,value:t}))],Xs=[{title:"none",value:""},{title:"xtls-rprx-vision",value:"xtls-rprx-vision"}],Js=["aes-128-gcm","aes-256-gcm","chacha20-ietf-poly1305"],Ks=At(t=>({isLoading:!1,isPostLoading:!1,hosts:{},fetchHosts:()=>{t({isLoading:!0}),W("/hosts").then(n=>t({hosts:n})).finally(()=>t({isLoading:!1}))},setHosts:n=>(t({isPostLoading:!0}),W("/hosts",{method:"PUT",body:n}).finally(()=>{t({isPostLoading:!1})}))}));const ei=C(Vr,{baseStyle:{w:4,h:4}}),Fe=$t.forwardRef(({disabled:t,step:n,label:o,className:s,startAdornment:a,endAdornment:i,type:l="text",placeholder:p,onChange:f,onBlur:w,name:S,value:x,onClick:h,error:m,clearable:I=!1,...b},T)=>{const u=()=>{f&&f({target:{value:"",name:S}})},{size:d="md"}=b,H=l=="number"?on:He,$=l=="number"?Xo:$t.Fragment,z=l=="number"?{keepWithinRange:!0,precision:5,format:P=>isNaN(parseFloat(String(P)))||Number(parseFloat(String(P)).toFixed(5))===0?P:Number(parseFloat(String(P)).toFixed(5)),min:0,step:n,name:S,type:l,placeholder:p,onChange:P=>{f&&f(P)},onBlur:w,value:x,onClick:h,disabled:t,flexGrow:1,size:d}:{};return r(A,{isInvalid:!!m,children:[o&&e(j,{children:o}),r(Dt,{size:d,w:"full",rounded:"md",_focusWithin:{outline:"2px solid",outlineColor:"primary.200"},bg:t?"gray.100":"transparent",_dark:{bg:t?"gray.600":"transparent"},children:[a&&e(Jo,{children:a}),r($,{...z,children:[e(H,{name:S,ref:T,step:n,className:St(s),type:l,placeholder:p,onChange:f,onBlur:w,value:x,onClick:h,disabled:t,flexGrow:1,_focusVisible:{outline:"none",borderTopColor:"transparent",borderRightColor:"transparent",borderBottomColor:"transparent"},_disabled:{cursor:"not-allowed"},...b,roundedLeft:a?"0":"md",roundedRight:i?"0":"md"}),l=="number"&&e(me,{children:r(Ko,{children:[e(en,{}),e(tn,{})]})})]}),i&&e(rn,{borderLeftRadius:0,borderRightRadius:"6px",bg:"transparent",children:i}),I&&x&&x.length&&e(zt,{borderLeftRadius:0,borderRightRadius:"6px",bg:"transparent",onClick:u,cursor:"pointer",children:e(ei,{})})]}),!!m&&e(xt,{children:m})]})}),ti=C(nn,{baseStyle:{w:5,h:5}}),ri=C(sn,{baseStyle:{w:5,h:5}}),oi=C(an,{baseStyle:{w:5,h:5}}),Ot=C(We,{baseStyle:{bg:"white",_dark:{bg:"gray.700"}}}),Me=C(Fe,{baseStyle:{bg:"white",_dark:{bg:"gray.700"}}}),ni=C(Xt,{baseStyle:{w:5,h:5}}),Le=C(ln,{baseStyle:{w:4,h:4,color:"gray.400",cursor:"pointer"}}),si=y.record(y.string().min(1),y.array(y.object({remark:y.string().min(1,"Remark is required"),address:y.string().min(1,"Address is required"),port:y.string().or(y.number()).nullable().transform(t=>typeof t=="number"?t:t!==null&&!isNaN(parseInt(t))?Number(parseInt(t)):null),path:y.string().nullable(),sni:y.string().nullable(),host:y.string().nullable(),mux_enable:y.boolean().default(!1),allowinsecure:y.boolean().nullable().default(!1),is_disabled:y.boolean().default(!0),fragment_setting:y.string().nullable(),noise_setting:y.string().nullable(),random_user_agent:y.boolean().default(!1),security:y.string(),alpn:y.string(),fingerprint:y.string(),use_sni_as_host:y.boolean().default(!1)}))),be=C(xt,{baseStyle:{color:"red.400",display:"block",textAlign:"left",w:"100%"}}),ii=({hostKey:t,isOpen:n,toggleAccordion:o})=>{const{inbounds:s}=D(),a=[...s.values()].flat().filter(u=>u.tag===t)[0],i=Kt(),{fields:l,append:p,remove:f,insert:w,move:S}=cn({control:i.control,name:t}),{errors:x}=i.formState,{t:h}=B(),m=x[t],I=()=>{p({host:"",sni:"",port:null,path:null,address:"",remark:"",mux_enable:!1,allowinsecure:!1,is_disabled:!1,fragment_setting:"",noise_setting:"",random_user_agent:!1,security:"inbound_default",alpn:"",fingerprint:"",use_sni_as_host:!1})},b=u=>{if(u<0||u>=l.length)return;const d=l[u];w(u+1,d)};g.exports.useEffect(()=>{m&&!n&&o()},[m]);const T=(u,d)=>{d==="up"&&u>0?S(u,u-1):d==="down"&&u<l.length-1&&S(u,u+1)};return r(Qe,{border:"1px solid",_dark:{borderColor:"gray.600"},_light:{borderColor:"gray.200"},borderRadius:"4px",p:1,w:"full",children:[r(qe,{px:2,borderRadius:"3px",onClick:o,children:[e(c,{as:"span",fontWeight:"medium",fontSize:"sm",flex:"1",textAlign:"left",color:"gray.700",_dark:{color:"gray.300"},children:t}),e(Vt,{})]}),e(Xe,{px:2,pb:2,children:r(O,{gap:3,children:[l.map((u,d)=>{var H,$,z,P,Z,U,E,Q,J,R,V,K,M,te,fe,ee,Ie,It,rt,vt,ot,nt,st,it,at,k,F,re,ue,we,ve,q,lt,ct,dt,ht,ut,Ye,ir,ar,lr,cr,dr,hr,ur;return e(dn.div,{layout:!0,initial:!1,animate:{opacity:1},exit:{opacity:0},transition:{layout:{type:"spring",stiffness:500,damping:30},opacity:{duration:.1}},id:u.id,whileDrag:{scale:1.05,zIndex:10},style:{width:"100%"},children:r(O,{id:u.id,border:"1px solid",_dark:{borderColor:"gray.600",bg:"#273142"},_light:{borderColor:"gray.200",bg:"#fcfbfb"},p:2,w:"full",borderRadius:"4px",children:[e(_,{w:"100%",alignItems:"flex-start",children:r(A,{position:"relative",zIndex:10,isInvalid:!!(m&&((H=m[d])==null?void 0:H.remark)),children:[r(Dt,{children:[e(Me,{...i.register(t+"."+d+".remark"),size:"sm",borderRadius:"4px",placeholder:"Remark"}),e(zt,{children:r(De,{isLazy:!0,placement:"right",children:[e(ze,{children:e(v,{mt:"-8px",children:e(Le,{})})}),e(Ue,{children:r(Ee,{children:[e(Re,{}),e(Ae,{}),e(fr,{children:r(v,{fontSize:"xs",children:[e(c,{pr:"20px",children:h("hostsDialog.desc")}),r(c,{children:[r(L,{children:["{","SERVER_IP","}"]})," ",h("hostsDialog.currentServer")]}),r(c,{mt:1,children:[r(L,{children:["{","SERVER_IPV6","}"]})," ",h("hostsDialog.currentServerv6")]}),r(c,{mt:1,children:[r(L,{children:["{","USERNAME","}"]})," ",h("hostsDialog.username")]}),r(c,{mt:1,children:[r(L,{children:["{","DATA_USAGE","}"]})," ",h("hostsDialog.dataUsage")]}),r(c,{mt:1,children:[r(L,{children:["{","DATA_LEFT","}"]})," ",h("hostsDialog.remainingData")]}),r(c,{mt:1,children:[r(L,{children:["{","DATA_LIMIT","}"]})," ",h("hostsDialog.dataLimit")]}),r(c,{mt:1,children:[r(L,{children:["{","DAYS_LEFT","}"]})," ",h("hostsDialog.remainingDays")]}),r(c,{mt:1,children:[r(L,{children:["{","EXPIRE_DATE","}"]})," ",h("hostsDialog.expireDate")]}),r(c,{mt:1,children:[r(L,{children:["{","JALALI_EXPIRE_DATE","}"]})," ",h("hostsDialog.jalaliExpireDate")]}),r(c,{mt:1,children:[r(L,{children:["{","TIME_LEFT","}"]})," ",h("hostsDialog.remainingTime")]}),r(c,{mt:1,children:[r(L,{children:["{","STATUS_TEXT","}"]})," ",h("hostsDialog.statusText")]}),r(c,{mt:1,children:[r(L,{children:["{","STATUS_EMOJI","}"]})," ",h("hostsDialog.statusEmoji")]}),r(c,{mt:1,children:[r(L,{children:["{","PROTOCOL","}"]})," ",h("hostsDialog.proxyProtocol")]}),r(c,{mt:1,children:[r(L,{children:["{","TRANSPORT","}"]})," ",h("hostsDialog.proxyMethod")]})]})})]})})]})})]}),m&&(($=m[d])==null?void 0:$.remark)&&e(be,{children:(P=(z=m[d])==null?void 0:z.remark)==null?void 0:P.message})]})}),r(A,{isInvalid:!!(m&&((Z=m[d])==null?void 0:Z.address)),children:[r(Dt,{children:[e(Me,{size:"sm",borderRadius:"4px",placeholder:"Address (e.g. example.com)",...i.register(t+"."+d+".address")}),e(zt,{children:r(De,{isLazy:!0,placement:"right",children:[e(ze,{children:e(v,{mt:"-8px",children:e(Le,{})})}),e(Ue,{children:r(Ee,{children:[e(Re,{}),e(Ae,{}),e(fr,{children:r(v,{fontSize:"xs",children:[e(c,{pr:"20px",children:h("hostsDialog.desc")}),r(c,{children:[r(L,{children:["{","SERVER_IP","}"]})," ",h("hostsDialog.currentServer")]}),r(c,{mt:1,children:[r(L,{children:["{","SERVER_IPV6","}"]})," ",h("hostsDialog.currentServerv6")]}),r(c,{mt:1,children:[r(L,{children:["{","USERNAME","}"]})," ",h("hostsDialog.username")]}),r(c,{mt:1,children:[r(L,{children:["{","DATA_USAGE","}"]})," ",h("hostsDialog.dataUsage")]}),r(c,{mt:1,children:[r(L,{children:["{","DATA_LEFT","}"]})," ",h("hostsDialog.remainingData")]}),r(c,{mt:1,children:[r(L,{children:["{","DATA_LIMIT","}"]})," ",h("hostsDialog.dataLimit")]}),r(c,{mt:1,children:[r(L,{children:["{","DAYS_LEFT","}"]})," ",h("hostsDialog.remainingDays")]}),r(c,{mt:1,children:[r(L,{children:["{","EXPIRE_DATE","}"]})," ",h("hostsDialog.expireDate")]}),r(c,{mt:1,children:[r(L,{children:["{","JALALI_EXPIRE_DATE","}"]})," ",h("hostsDialog.jalaliExpireDate")]}),r(c,{mt:1,children:[r(L,{children:["{","TIME_LEFT","}"]})," ",h("hostsDialog.remainingTime")]}),r(c,{mt:1,children:[r(L,{children:["{","STATUS_TEXT","}"]})," ",h("hostsDialog.statusText")]}),r(c,{mt:1,children:[r(L,{children:["{","STATUS_EMOJI","}"]})," ",h("hostsDialog.statusEmoji")]}),r(c,{mt:1,children:[r(L,{children:["{","PROTOCOL","}"]})," ",h("hostsDialog.proxyProtocol")]}),r(c,{mt:1,children:[r(L,{children:["{","TRANSPORT","}"]})," ",h("hostsDialog.proxyMethod")]})]})})]})})]})})]}),m&&((U=m[d])==null?void 0:U.address)&&e(be,{children:(Q=(E=m[d])==null?void 0:E.address)==null?void 0:Q.message})]}),e(wt,{w:"full",allowToggle:!0,children:r(Qe,{border:"0",children:[r("div",{style:{display:"flex",alignItems:"center"},children:[r(qe,{display:"flex",px:0,py:1,borderRadius:3,_hover:{bg:"transparent"},children:[r(c,{flex:"3",align:"start",fontSize:"xs",color:"gray.600",_dark:{color:"gray.500"},pl:1,children:[h("hostsDialog.advancedOptions"),e(Vt,{fontSize:"sm",ml:1})]}),r(hn,{flex:"1",px:"0",display:"contents",children:[e(se,{control:i.control,name:`${t}.${d}.is_disabled`,render:({field:oe})=>e(Ut,{mx:"1.5",colorScheme:"primary",...oe,value:void 0,isChecked:!oe.value,onChange:pr=>{console.log(pr.target.checked),oe.onChange(!pr.target.checked)}})}),e(ne,{label:"Delete",placement:"top",children:e(Y,{"aria-label":"Delete",size:"sm",colorScheme:"red",variant:"ghost",onClick:f.bind(null,d),children:e(_t,{})})})]})]}),e(ne,{label:"Duplicate",placement:"top",children:e(Y,{"aria-label":"Duplicate",size:"sm",colorScheme:"white",variant:"ghost",onClick:()=>b(d),children:e(ti,{})})}),d<l.length-1&&e(ne,{label:"Move Down",placement:"top",children:e(Y,{"aria-label":"DownIcon",size:"sm",colorScheme:"white",variant:"ghost",onClick:()=>T(d,"down"),children:e(oi,{})})}),d>0&&e(ne,{label:"Move Up",placement:"top",children:e(Y,{"aria-label":"UpIcon",size:"sm",colorScheme:"white",variant:"ghost",onClick:()=>T(d,"up"),children:e(ri,{})})})]}),e(Xe,{w:"full",p:1,children:r(O,{w:"full",borderRadius:"4px",children:[r(A,{isInvalid:!!(m&&((J=m[d])==null?void 0:J.port)),children:[r(j,{display:"flex",pb:1,alignItems:"center",justifyContent:"space-between",gap:1,m:"0",children:[e("span",{children:h("hostsDialog.port")}),r(De,{isLazy:!0,placement:"right",children:[e(ze,{children:e(Le,{})}),e(Ue,{children:r(Ee,{p:2,children:[e(Re,{}),e(Ae,{}),e(c,{fontSize:"xs",pr:5,children:h("hostsDialog.port.info")})]})})]})]}),e(Me,{size:"sm",borderRadius:"4px",placeholder:String(a.port||"8080"),type:"number",...i.register(t+"."+d+".port")})]}),r(A,{isInvalid:!!(m&&((R=m[d])==null?void 0:R.sni)),children:[r(j,{display:"flex",pb:1,alignItems:"center",gap:1,justifyContent:"space-between",m:"0",children:[e("span",{children:h("hostsDialog.sni")}),r(De,{isLazy:!0,placement:"right",children:[e(ze,{children:e(Le,{})}),e(Ue,{children:r(Ee,{p:2,children:[e(Re,{}),e(Ae,{}),e(c,{fontSize:"xs",pr:5,children:h("hostsDialog.sni.info")}),e(c,{fontSize:"xs",mt:"2",children:e(Ne,{i18nKey:"hostsDialog.host.wildcard",components:{badge:e(L,{})}})}),e(c,{fontSize:"xs",children:e(Ne,{i18nKey:"hostsDialog.host.multiHost",components:{badge:e(L,{})}})})]})})]})]}),e(Me,{size:"sm",borderRadius:"4px",placeholder:"SNI (e.g. example.com)",...i.register(t+"."+d+".sni")}),m&&((V=m[d])==null?void 0:V.sni)&&e(be,{children:(M=(K=m[d])==null?void 0:K.sni)==null?void 0:M.message})]}),r(A,{isInvalid:!!(m&&((te=m[d])==null?void 0:te.host)),children:[r(j,{display:"flex",pb:1,alignItems:"center",gap:1,justifyContent:"space-between",m:"0",children:[e("span",{children:h("hostsDialog.host")}),r(De,{isLazy:!0,placement:"right",children:[e(ze,{children:e(Le,{})}),e(Ue,{children:r(Ee,{p:2,children:[e(Re,{}),e(Ae,{}),e(c,{fontSize:"xs",pr:5,children:h("hostsDialog.host.info")}),e(c,{fontSize:"xs",mt:"2",children:e(Ne,{i18nKey:"hostsDialog.host.wildcard",components:{badge:e(L,{})}})}),e(c,{fontSize:"xs",children:e(Ne,{i18nKey:"hostsDialog.host.multiHost",components:{badge:e(L,{})}})})]})})]})]}),e(Me,{size:"sm",borderRadius:"4px",placeholder:"Host (e.g. example.com)",...i.register(t+"."+d+".host")}),m&&((fe=m[d])==null?void 0:fe.host)&&e(be,{children:(Ie=(ee=m[d])==null?void 0:ee.host)==null?void 0:Ie.message})]}),r(A,{isInvalid:!!(m&&((It=m[d])==null?void 0:It.path)),children:[r(j,{display:"flex",pb:1,alignItems:"center",gap:1,justifyContent:"space-between",m:"0",children:[e("span",{children:h("hostsDialog.path")}),r(De,{isLazy:!0,placement:"right",children:[e(ze,{children:e(Le,{})}),e(Ue,{children:r(Ee,{p:2,children:[e(Re,{}),e(Ae,{}),e(c,{fontSize:"xs",pr:5,children:h("hostsDialog.path.info")})]})})]})]}),e(Me,{size:"sm",borderRadius:"4px",placeholder:"path (e.g. /vless)",...i.register(t+"."+d+".path")}),m&&((rt=m[d])==null?void 0:rt.path)&&e(be,{children:(ot=(vt=m[d])==null?void 0:vt.path)==null?void 0:ot.message})]}),r(A,{height:"66px",children:[r(j,{display:"flex",pb:1,alignItems:"center",gap:1,justifyContent:"space-between",m:"0",children:[e("span",{children:h("hostsDialog.security")}),r(De,{isLazy:!0,placement:"right",children:[e(ze,{children:e(Le,{})}),e(Ue,{children:r(Ee,{p:2,children:[e(Re,{}),e(Ae,{}),e(c,{fontSize:"xs",pr:5,children:h("hostsDialog.security.info")})]})})]})]}),e(Ot,{size:"sm",...i.register(t+"."+d+".security"),children:Zs.map(oe=>e("option",{value:oe.value,children:oe.title},oe.value))})]}),r(A,{height:"66px",children:[e(j,{display:"flex",pb:1,alignItems:"center",gap:1,justifyContent:"space-between",m:"0",children:e("span",{children:h("hostsDialog.alpn")})}),e(Ot,{size:"sm",...i.register(t+"."+d+".alpn"),children:Qs.map(oe=>e("option",{value:oe.value,children:oe.title},oe.value))})]}),r(A,{height:"66px",children:[e(j,{display:"flex",pb:1,alignItems:"center",gap:1,justifyContent:"space-between",m:"0",children:e("span",{children:h("hostsDialog.fingerprint")})}),e(Ot,{size:"sm",...i.register(t+"."+d+".fingerprint"),children:qs.map(oe=>e("option",{value:oe.value,children:oe.title},oe.value))})]}),r(A,{isInvalid:!!(m&&((nt=m[d])==null?void 0:nt.fragment_setting)),children:[r(j,{display:"flex",pb:1,alignItems:"center",gap:1,justifyContent:"space-between",m:"0",children:[e("span",{children:h("hostsDialog.fragment")}),r(De,{isLazy:!0,placement:"right",children:[e(ze,{children:e(Le,{})}),e(Ue,{children:r(Ee,{p:2,children:[e(Re,{}),e(Ae,{}),e(c,{fontSize:"xs",pr:5,children:h("hostsDialog.fragment.info")}),e(c,{fontSize:"xs",pr:5,pt:2,pb:1,children:h("hostsDialog.fragment.info.examples")}),e(c,{fontSize:"xs",pr:5,children:"100-200,10-20,tlshello"}),e(c,{fontSize:"xs",pr:5,children:"100-200,10-20,1-3"}),e(c,{fontSize:"xs",pr:5,pt:"3",children:h("hostsDialog.fragment.info.attention")})]})})]})]}),e(Me,{size:"sm",borderRadius:"4px",placeholder:"Fragment settings by pattern",...i.register(t+"."+d+".fragment_setting")}),m&&((st=m[d])==null?void 0:st.fragment_setting)&&e(be,{children:(at=(it=m[d])==null?void 0:it.fragment_setting)==null?void 0:at.message})]}),r(A,{isInvalid:!!(m&&((k=m[d])==null?void 0:k.noise_setting)),children:[r(j,{display:"flex",pb:1,alignItems:"center",gap:1,justifyContent:"space-between",m:"0",children:[e("span",{children:h("hostsDialog.noise")}),r(De,{isLazy:!0,placement:"right",children:[e(ze,{children:e(Le,{})}),e(Ue,{children:r(Ee,{p:2,children:[e(Re,{}),e(Ae,{}),e(c,{fontSize:"xs",pr:5,children:h("hostsDialog.noise.info")}),e(c,{fontSize:"xs",pr:5,pt:2,pb:1,children:h("hostsDialog.noise.info.examples")}),e(c,{fontSize:"xs",pr:5,children:"rand:10-20,10-20"}),e(c,{fontSize:"xs",pr:5,children:"rand:10-20,10-20&base64:7nQBAAABAAAAAAAABnQtcmluZwZtc2VkZ2UDbmV0AAABAAE=,10-25"}),e(c,{fontSize:"xs",pr:5,pt:"3",children:h("hostsDialog.noise.info.attention")})]})})]})]}),e(Me,{size:"sm",borderRadius:"4px",placeholder:"Noise settings by pattern",...i.register(t+"."+d+".noise_setting")}),m&&((F=m[d])==null?void 0:F.noise_setting)&&e(be,{children:(ue=(re=m[d])==null?void 0:re.noise_setting)==null?void 0:ue.message})]}),r(A,{isInvalid:!!(m&&((we=m[d])==null?void 0:we.use_sni_as_host)),children:[e(Pe,{...i.register(t+"."+d+".use_sni_as_host"),children:e(j,{children:h("hostsDialog.useSniAsHost")})}),m&&((ve=m[d])==null?void 0:ve.use_sni_as_host)&&e(be,{children:(lt=(q=m[d])==null?void 0:q.use_sni_as_host)==null?void 0:lt.message})]}),e(A,{isInvalid:!!(m&&((ct=m[d])==null?void 0:ct.allowinsecure)),children:r(Pe,{...i.register(t+"."+d+".allowinsecure"),name:t+"."+d+".allowinsecure",children:[e(j,{children:h("hostsDialog.allowinsecure")}),m&&((dt=m[d])==null?void 0:dt.allowinsecure)&&e(be,{children:(ut=(ht=m[d])==null?void 0:ht.allowinsecure)==null?void 0:ut.message})]})}),r(A,{isInvalid:!!(m&&((Ye=m[d])==null?void 0:Ye.mux_enable)),children:[e(Pe,{...i.register(t+"."+d+".mux_enable"),children:e(j,{children:h("hostsDialog.muxEnable")})}),m&&((ir=m[d])==null?void 0:ir.mux_enable)&&e(be,{children:(lr=(ar=m[d])==null?void 0:ar.mux_enable)==null?void 0:lr.message})]}),r(A,{isInvalid:!!(m&&((cr=m[d])==null?void 0:cr.random_user_agent)),children:[e(Pe,{...i.register(t+"."+d+".random_user_agent"),children:e(j,{children:h("hostsDialog.randomUserAgent")})}),m&&((dr=m[d])==null?void 0:dr.random_user_agent)&&e(be,{children:(ur=(hr=m[d])==null?void 0:hr.random_user_agent)==null?void 0:ur.message})]})]},d)})]})})]},u.id)},u.id)}),e(N,{variant:"outline",w:"full",size:"sm",color:"",fontWeight:"normal",onClick:I,children:h("hostsDialog.addHost")})]})})]})},ai=()=>{const{isEditingHosts:t,onEditingHosts:n,refetchUsers:o,inbounds:s}=D(),{isLoading:a,hosts:i,fetchHosts:l,isPostLoading:p,setHosts:f}=Ks(),w=ye(),{t:S}=B(),[x,h]=g.exports.useState({});g.exports.useEffect(()=>{t&&l()},[t]);const m=Ge({resolver:et(si)});g.exports.useEffect(()=>{i&&t&&m.reset(i)},[i]);const I=()=>{h({}),n(!1)},b=u=>{f(u).then(()=>{w({title:S("hostsDialog.savedSuccess"),status:"success",isClosable:!0,position:"top",duration:3e3}),o()}).catch(d=>{var H,$,z,P,Z;(((H=d==null?void 0:d.response)==null?void 0:H.status)===409||(($=d==null?void 0:d.response)==null?void 0:$.status)===400)&&w({title:(P=(z=d.response)==null?void 0:z._data)==null?void 0:P.detail,status:"error",isClosable:!0,position:"top",duration:3e3}),((Z=d==null?void 0:d.response)==null?void 0:Z.status)===422&&Object.keys(d.response._data.detail).forEach(U=>{w({title:d.response._data.detail[U]+" ("+U+")",status:"error",isClosable:!0,position:"top",duration:3e3})})})},T=u=>{x[String(u)]?delete x[String(u)]:x[String(u)]={},h({...x})};return r(ie,{isOpen:t,onClose:I,children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",w:"fit-content",maxW:"3xl",children:[e(ce,{pt:6,children:e(ge,{color:"primary",children:e(ni,{color:"white"})})}),e(de,{mt:3}),e(he,{w:"440px",pb:3,pt:3,children:e(Jt,{...m,children:r("form",{onSubmit:m.handleSubmit(b),children:[e(c,{mb:3,opacity:.8,fontSize:"sm",children:S("hostsDialog.title")}),a&&S("hostsDialog.loading"),!a&&i&&(Object.keys(i).length>0?e(wt,{w:"full",allowToggle:!0,allowMultiple:!0,index:Object.keys(x).map(u=>parseInt(u)),children:e(O,{w:"full",children:Object.keys(i).map((u,d)=>e(ii,{toggleAccordion:()=>T(d),isOpen:x[String(d)],hostKey:u},u))})}):"No inbound found. Please check your Xray config file."),e(_,{justifyContent:"flex-end",py:2,children:e(N,{variant:"solid",mt:"2",type:"submit",colorScheme:"primary",size:"sm",px:5,isLoading:p,disabled:p,children:S("hostsDialog.apply")})})]})})})]})]})},li=C(un,{baseStyle:{strokeWidth:"2px",w:5,h:5}}),ci=({inbound:t,...n})=>{const{getCheckboxProps:o,getInputProps:s,getLabelProps:a,htmlProps:i}=Kr(n),l=s();return r(v,{as:"label",children:[e("input",{...l}),r(v,{w:"fll",position:"relative",...i,cursor:"pointer",borderRadius:"sm",border:"1px solid",borderColor:"gray.200",_dark:{borderColor:"gray.600"},display:"flex",alignItems:"center",justifyContent:"space-between",overflow:"hidden",_checked:{bg:"gray.50",outline:"2px",boxShadow:"outline",outlineColor:"primary.500",borderColor:"transparent",fontWeight:"medium",_dark:{bg:"gray.750",borderColor:"transparent"},"& p":{opacity:1}},__css:{"& p":{opacity:.8}},textTransform:"capitalize",px:3,py:2,fontWeight:"medium",...o(),children:[e(Pe,{size:"sm",w:"full",maxW:"full",color:"gray.700",_dark:{color:"gray.300"},textTransform:"uppercase",colorScheme:"primary",className:"inbound-item",isChecked:l.checked,pointerEvents:"none",flexGrow:1,children:e(_,{justify:"space-between",w:"full",maxW:"calc(100% - 20px)",spacing:0,gap:2,overflow:"hidden",children:r(c,{isTruncated:!0,...a(),fontSize:"xs",children:[t.tag," ",r(c,{as:"span",children:["(",t.network,")"]})]})})}),t.tls&&t.tls!="none"&&e(L,{fontSize:"xs",opacity:".8",size:"xs",children:t.tls})]})]})},di=({disabled:t,title:n,description:o,toggleAccordion:s,isSelected:a,...i})=>{const l=Kt(),{inbounds:p}=D(),{getCheckboxProps:f,getInputProps:w,getLabelProps:S,htmlProps:x}=Kr(i),h=w(),[m]=eo({name:[`inbounds.${n}`],control:l.control}),{getCheckboxProps:I}=Jr({value:m,onChange:d=>{if(l.setValue(`inbounds.${n}`,d),d.length===0){const H=l.getValues("selected_proxies");l.setValue("selected_proxies",H.filter($=>$!==n)),s()}}}),b=m&&a&&(D.getState().inbounds.get(n)||[]).length!==m.length,T=(D.getState().inbounds.get(n)||[]).length>0,u=!a&&!T;return r(Qe,{isDisabled:!T,borderRadius:"md",borderStyle:"solid",border:"1px",borderColor:"gray.200",bg:u?"gray.100":"transparent",_dark:{borderColor:"gray.600",bg:u?"#364154":"transparent"},_checked:{bg:"gray.50",outline:"2px",boxShadow:"outline",outlineColor:"primary.500",borderColor:"transparent"},...f(),children:[r(v,{as:u?"span":"label",position:"relative",children:[b&&e(v,{position:"absolute",w:"2",h:"2",bg:"yellow.500",top:"-1",right:"-1",rounded:"full",zIndex:999}),e("input",{...h}),r(v,{w:"fll",position:"relative",...x,borderRadius:"md",cursor:u?"not-allowed":"pointer",_checked:{fontWeight:"medium",_dark:{bg:"gray.750",borderColor:"transparent"},"& > svg":{opacity:1,"&.checked":{display:"block"},"&.unchecked":{display:"none"}},"& p":{opacity:1}},__css:{"& > svg":{opacity:.3,"&.checked":{display:"none"},"&.unchecked":{display:"block"}},"& p":{opacity:.8}},textTransform:"capitalize",px:3,py:2,fontWeight:"medium",...f(),children:[e(qe,{display:h.checked&&T?"block":"none",as:"span",className:"checked",color:"primary.200",position:"absolute",right:"3",top:"3",w:"auto",p:0,onClick:s,children:e(Y,{size:"sm","aria-label":"inbound settings",children:e(li,{})})}),e(c,{fontSize:"sm",color:u?"gray.400":"gray.700",_dark:{color:u?"gray.500":"gray.300"},...S(),children:n}),e(c,{fontWeight:"medium",color:u?"gray.400":"gray.600",_dark:{color:u?"gray.500":"gray.400"},fontSize:"xs",children:o})]})]}),e(Xe,{px:2,pb:3,roundedBottom:"5px",pt:3,_dark:{bg:h.checked&&"gray.750"},children:r(O,{w:"full",rowGap:2,borderStyle:"solid",borderWidth:"1px",borderRadius:"md",pl:3,pr:3,pt:1.5,_dark:{bg:"gray.700"},children:[r(O,{alignItems:"flex-start",w:"full",children:[e(c,{fontSize:"sm",children:X("inbound")}),e(er,{gap:2,alignItems:"flex-start",w:"full",columns:1,spacing:1,children:(p.get(n)||[]).map(d=>e(ci,{...I({value:d.tag}),inbound:d},d.tag))})]}),n==="vmess"&&a&&e(O,{alignItems:"flex-start",w:"full",children:r(A,{height:"66px",children:[e(c,{fontSize:"sm",pb:1,children:"ID"}),e(He,{fontSize:"xs",size:"sm",borderRadius:"6px",pl:2,pr:2,placeholder:X("userDialog.generatedByDefault"),...l.register("proxies.vmess.id")})]})}),n==="vless"&&a&&r(O,{alignItems:"flex-start",w:"full",children:[r(A,{height:"66px",children:[e(c,{fontSize:"sm",pb:1,children:"ID"}),e(He,{fontSize:"xs",size:"sm",borderRadius:"6px",pl:2,pr:2,placeholder:X("userDialog.generatedByDefault"),...l.register("proxies.vless.id")})]}),r(A,{height:"66px",children:[e(c,{fontSize:"sm",pb:1,children:"Flow"}),e(We,{fontSize:"xs",size:"sm",borderRadius:"6px",...l.register("proxies.vless.flow"),children:Xs.map(d=>e("option",{value:d.value,children:d.title},d.title))})]})]}),n==="trojan"&&a&&e(O,{alignItems:"flex-start",w:"full",children:r(A,{height:"66px",children:[e(c,{fontSize:"sm",pb:1,children:X("password")}),e(He,{fontSize:"xs",size:"sm",borderRadius:"6px",pl:2,pr:2,placeholder:X("userDialog.generatedByDefault"),...l.register("proxies.trojan.password")})]})}),n==="shadowsocks"&&a&&r(O,{alignItems:"flex-start",w:"full",children:[r(A,{height:"66px",children:[e(c,{fontSize:"sm",pb:1,children:X("password")}),e(He,{fontSize:"xs",size:"sm",borderRadius:"6px",pl:2,pr:2,placeholder:X("userDialog.generatedByDefault"),...l.register("proxies.shadowsocks.password")})]}),r(A,{height:"66px",children:[e(c,{fontSize:"sm",pb:1,children:X("userDialog.method")}),e(We,{fontSize:"xs",size:"sm",borderRadius:"6px",...l.register("proxies.shadowsocks.method"),children:Js.map(d=>e("option",{value:d,children:d},d))})]})]})]})})]})},yo=g.exports.forwardRef(({name:t,list:n,onChange:o,disabled:s,...a},i)=>{const l=Kt(),[p,f]=g.exports.useState([]),w=x=>{p.includes(x)?p.splice(p.indexOf(x),1):p.push(x),f([...p])},{getCheckboxProps:S}=Jr({value:a.value,onChange:x=>{var m;const h=x.filter(I=>!a.value.includes(I));h[0]&&l.setValue(`inbounds.${h[0]}`,(m=D.getState().inbounds.get(h[0]))==null?void 0:m.map(I=>I.tag)),f(p.filter(I=>x.find(b=>b===n[I].title))),o({target:{value:x,name:t}})}});return e(wt,{allowToggle:!0,index:p,children:e(er,{ref:i,gap:2,alignItems:"flex-start",columns:1,spacing:1,children:n.map((x,h)=>e(di,{toggleAccordion:w.bind(null,h),disabled:s,title:x.title,description:x.description,isSelected:!!a.value.find(m=>m===x.title),...S({value:x.title})},x.title))})})}),hi=C(Xr,{baseStyle:{w:5,h:5}}),ui=y.object({file:y.any().refine(t=>t instanceof File,"Please select a file"),set_unlimited_expire:y.boolean(),enable_smart_username_parsing:y.boolean(),selected_protocols:y.array(y.string()).min(1,"Please select at least one protocol"),inbounds:y.record(y.array(y.string())).optional(),proxies:y.record(y.any()).optional()}),zr=()=>({file:null,set_unlimited_expire:!1,enable_smart_username_parsing:!0,selected_protocols:[],inbounds:{},proxies:{}}),pi=()=>{var P,Z;const{isImportingHiddifyUsers:t,onImportHiddifyUsers:n}=D(),{t:o}=B(),s=ye(),a=g.exports.useRef(null),[i,l]=g.exports.useState(!1),[p,f]=g.exports.useState(null),[w,S]=g.exports.useState(!1),[x,h]=g.exports.useState(!1),[m,I]=g.exports.useState(0),b=Ge({resolver:et(ui),defaultValues:zr()});b.watch("selected_protocols"),g.exports.useEffect(()=>{t&&d()},[t]);const T=()=>{n(!1),f(null),h(!1),b.reset(zr()),a.current&&(a.current.value="")},u=U=>{var Q;const E=(Q=U.target.files)==null?void 0:Q[0];E&&E.type==="application/json"?(b.setValue("file",E),f(null)):(s({title:o("hiddifyImport.invalidFile"),description:o("hiddifyImport.invalidFileDesc"),status:"error",duration:3e3,isClosable:!0}),a.current&&(a.current.value=""))},d=async()=>{try{const E=(await W("/users?limit=1000")).users.filter(Q=>Q.custom_uuid);return I(E.length),E.length}catch(U){return console.error("Error checking imported users:",U),0}},H=async()=>{S(!0);try{const E=(await W("/users?limit=1000")).users.filter(J=>J.custom_uuid),Q=E.map(J=>W(`/user/${J.username}`,{method:"DELETE"}));await Promise.all(Q),s({title:o("hiddifyImport.deleteSuccess"),description:o("hiddifyImport.deleteSuccessDesc",{count:E.length}),status:"success",duration:5e3,isClosable:!0}),D.getState().refetchUsers(),h(!1),I(0)}catch(U){console.error("Error deleting imported users:",U),s({title:o("hiddifyImport.deleteError"),description:o("hiddifyImport.deleteErrorDesc"),status:"error",duration:5e3,isClosable:!0})}finally{S(!1)}},$=async()=>{await d()>0?h(!0):s({title:o("hiddifyImport.noImportedUsers"),description:o("hiddifyImport.noImportedUsersDesc"),status:"info",duration:3e3,isClosable:!0})},z=async()=>{if(!b.getValues("file")){s({title:o("hiddifyImport.noFileSelected"),status:"warning",duration:3e3,isClosable:!0});return}if(b.getValues("selected_protocols").length===0){s({title:o("hiddifyImport.noProtocolsSelected"),status:"warning",duration:3e3,isClosable:!0});return}l(!0),f(null);try{const U=new FormData;U.append("file",b.getValues("file")),U.append("set_unlimited_expire",b.getValues("set_unlimited_expire").toString()),U.append("enable_smart_username_parsing",b.getValues("enable_smart_username_parsing").toString()),U.append("selected_protocols",JSON.stringify(b.getValues("selected_protocols"))),U.append("inbounds",JSON.stringify(b.getValues("inbounds"))),U.append("proxies",JSON.stringify(b.getValues("proxies")));const E=await W("/users/import/hiddify",{method:"POST",body:U});f(E),E.successful_imports>0&&(s({title:o("hiddifyImport.importSuccess"),description:o("hiddifyImport.importSuccessDesc",{successful:E.successful_imports,failed:E.failed_imports}),status:"success",duration:5e3,isClosable:!0}),D.getState().refetchUsers()),(E.failed_imports>0||E.errors.length>0)&&s({title:o("hiddifyImport.importWarning"),description:o("hiddifyImport.importWarningDesc",{failed:E.failed_imports}),status:"warning",duration:5e3,isClosable:!0})}catch(U){console.error("Hiddify import error:",U),s({title:o("hiddifyImport.importError"),description:o("hiddifyImport.importErrorDesc"),status:"error",duration:5e3,isClosable:!0})}finally{l(!1)}};return r(ie,{isOpen:t,onClose:T,size:"2xl",children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),e(Jt,{...b,children:r(le,{mx:"3",maxH:"90vh",overflowY:"auto",children:[e(ce,{pt:6,children:r(_,{gap:2,children:[e(ge,{color:"primary",children:e(hi,{color:"white"})}),e(c,{fontWeight:"semibold",fontSize:"lg",children:o("hiddifyImport.title")})]})}),e(de,{mt:3,disabled:i||w}),e(he,{pb:4,children:r(qt,{templateColumns:{base:"repeat(1, 1fr)",md:"repeat(2, 1fr)"},gap:6,children:[e(Be,{children:r(O,{spacing:4,align:"stretch",children:[r(A,{children:[e(j,{children:o("hiddifyImport.selectFile")}),r(v,{position:"relative",children:[e(He,{ref:a,type:"file",accept:".json",onChange:u,disabled:i,size:"md",borderRadius:"6px",height:"40px",sx:{"&::file-selector-button":{bg:"primary.500",color:"white",border:"none",borderRadius:"md",px:4,py:2,mr:3,cursor:"pointer",fontSize:"sm",_hover:{bg:"primary.600"}}}}),b.watch("file")&&e(c,{fontSize:"sm",color:"green.500",mt:2,children:o("hiddifyImport.fileSelected",{filename:b.watch("file").name})})]})]}),r(A,{children:[e(se,{control:b.control,name:"set_unlimited_expire",render:({field:{onChange:U,value:E}})=>e(Pe,{isChecked:E,onChange:U,disabled:i,size:"sm",children:e(c,{fontSize:"sm",children:o("hiddifyImport.unlimitedExpiration")})})}),e(c,{fontSize:"xs",color:"gray.500",mt:1,children:o("hiddifyImport.unlimitedExpirationDesc")})]}),r(A,{children:[e(se,{control:b.control,name:"enable_smart_username_parsing",render:({field:{onChange:U,value:E}})=>e(Pe,{isChecked:E,onChange:U,disabled:i,size:"sm",children:e(c,{fontSize:"sm",children:o("hiddifyImport.smartUsernameParsing")})})}),e(c,{fontSize:"xs",color:"gray.500",mt:1,children:o("hiddifyImport.smartUsernameParsingDesc")})]}),i&&r(v,{w:"full",children:[e(c,{fontSize:"sm",mb:2,children:o("hiddifyImport.importing")}),e(pn,{size:"sm",isIndeterminate:!0,colorScheme:"primary"})]}),p&&r(Ve,{status:p.successful_imports>0?"success":"warning",children:[e(Je,{}),r(O,{align:"start",spacing:1,flex:1,children:[e(c,{fontSize:"sm",fontWeight:"medium",children:o("hiddifyImport.importComplete")}),e(c,{fontSize:"xs",children:o("hiddifyImport.importStats",{successful:p.successful_imports,failed:p.failed_imports})}),p.errors.length>0&&e(v,{maxH:"100px",overflowY:"auto",w:"full",children:p.errors.map((U,E)=>r(c,{fontSize:"xs",color:"red.500",children:["\u2022 ",U]},E))})]})]})]})}),e(Be,{children:r(A,{isInvalid:!!((P=b.formState.errors.selected_protocols)!=null&&P.message),children:[e(j,{children:o("hiddifyImport.protocolSelection")}),e(v,{position:"relative",zIndex:1,children:e(se,{control:b.control,name:"selected_protocols",render:({field:U})=>e(yo,{list:[{title:"vmess",description:o("userDialog.vmessDesc")},{title:"vless",description:o("userDialog.vlessDesc")},{title:"trojan",description:o("userDialog.trojanDesc")},{title:"shadowsocks",description:o("userDialog.shadowsocksDesc")}],disabled:i,...U})})}),e(xt,{children:(Z=b.formState.errors.selected_protocols)==null?void 0:Z.message})]})})]})}),e(ke,{pt:4,pb:6,children:r(_,{spacing:3,w:"full",flexDirection:{base:"column",sm:"row"},children:[e(N,{variant:"outline",onClick:T,disabled:i||w,size:"sm",flex:1,children:o(p?"close":"cancel")}),e(N,{variant:"outline",colorScheme:"red",onClick:$,disabled:i||w,leftIcon:w?e(_e,{size:"xs"}):void 0,size:"sm",flex:1,children:o("hiddifyImport.deleteImported")}),e(N,{colorScheme:"primary",onClick:z,disabled:!b.watch("file")||b.watch("selected_protocols").length===0||i||w,leftIcon:i?e(_e,{size:"xs"}):void 0,size:"sm",flex:1,children:o("hiddifyImport.importUsers")})]})})]})}),r(ie,{isOpen:x,onClose:()=>h(!1),size:"md",children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",children:[e(ce,{children:e(c,{fontWeight:"semibold",fontSize:"lg",color:"red.500",children:o("hiddifyImport.deleteConfirmTitle")})}),e(de,{disabled:w}),e(he,{children:r(O,{spacing:3,align:"stretch",children:[r(Ve,{status:"warning",children:[e(Je,{}),e(c,{fontSize:"sm",children:o("hiddifyImport.deleteConfirmMessage",{count:m})})]}),e(c,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:o("hiddifyImport.deleteConfirmDesc")})]})}),e(ke,{children:r(_,{spacing:3,w:"full",children:[e(N,{variant:"outline",onClick:()=>h(!1),disabled:w,flex:1,size:"sm",children:o("cancel")}),e(N,{colorScheme:"red",onClick:H,disabled:w,leftIcon:w?e(_e,{size:"xs"}):void 0,flex:1,size:"sm",children:o("hiddifyImport.confirmDelete")})]})})]})]})]})},nr=(t,n,o)=>{if(t.response&&t.response._data){if(typeof t.response._data.detail=="string")return n({title:t.response._data.detail,status:"error",isClosable:!0,position:"top",duration:3e3});if(typeof t.response._data.detail=="object"&&o){Object.keys(t.response._data.detail).forEach(s=>o.setError(s,{message:t.response._data.detail[s]}));return}}return n({title:"Something went wrong!",status:"error",isClosable:!0,position:"top",duration:3e3})},sr=(t,n)=>n({title:t,status:"success",isClosable:!0,position:"top",duration:3e3}),mi=({deleteCallback:t})=>{const{deleteNode:n,deletingNode:o,setDeletingNode:s}=Ct(),{t:a}=B(),i=ye(),l=tr(),p=()=>{s(null)},{isLoading:f,mutate:w}=bt(n,{onSuccess:()=>{sr(a("deleteNode.deleteSuccess",{name:o&&o.name}),i),s(null),l.invalidateQueries(yt),t&&t()},onError:S=>{nr(S,i)}});return r(ie,{isCentered:!0,isOpen:!!o,onClose:p,size:"sm",children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",children:[e(ce,{pt:6,children:e(ge,{color:"red",children:e(_t,{})})}),e(de,{mt:3}),r(he,{children:[e(c,{fontWeight:"semibold",fontSize:"lg",children:a("deleteNode.title")}),o&&e(c,{mt:1,fontSize:"sm",_dark:{color:"gray.400"},color:"gray.600",children:e(Ne,{components:{b:e("b",{})},children:a("deleteNode.prompt",{name:o.name})})})]}),r(ke,{display:"flex",children:[e(N,{size:"sm",onClick:p,mr:3,w:"full",variant:"outline",children:a("cancel")}),e(N,{size:"sm",w:"full",colorScheme:"red",onClick:()=>w(),leftIcon:f?e(_e,{size:"xs"}):void 0,children:a("delete")})]})]})]})},kt={baseStyle:{strokeWidth:"2px",w:4,h:4}},Ur=C(mn,kt),gi=C(gn,kt),Er=C(fn,kt),Rr=C(to,kt),fi=C(to,kt),Zt=[{title:"No",value:"no_reset"},{title:"Daily",value:"day"},{title:"Weekly",value:"week"},{title:"Monthly",value:"month"},{title:"Annually",value:"year"}],$e={active:{statusColor:"green",bandWidthColor:"primary",icon:Ur},connected:{statusColor:"green",bandWidthColor:"primary",icon:Ur},disabled:{statusColor:"gray",bandWidthColor:"gray",icon:gi},expired:{statusColor:"orange",bandWidthColor:"orange",icon:Rr},on_hold:{statusColor:"purple",bandWidthColor:"purple",icon:fi},connecting:{statusColor:"orange",bandWidthColor:"orange",icon:Rr},limited:{statusColor:"red",bandWidthColor:"red",icon:Er},error:{statusColor:"red",bandWidthColor:"red",icon:Er}},Nt=t=>{let n={status:"",time:""};if(t){G(t*1e3).utc().isAfter(G().utc())?n.status="expires":n.status="expired";const o=[],s=G.duration(G(t*1e3).utc().diff(G()));s.years()!=0&&o.push(Math.abs(s.years())+" year"+(Math.abs(s.years())!=1?"s":"")),s.months()!=0&&o.push(Math.abs(s.months())+" month"+(Math.abs(s.months())!=1?"s":"")),s.days()!=0&&o.push(Math.abs(s.days())+" day"+(Math.abs(s.days())!=1?"s":"")),o.length===0&&(s.hours()!=0&&o.push(Math.abs(s.hours())+" hour"+(Math.abs(s.hours())!=1?"s":"")),s.minutes()!=0&&o.push(Math.abs(s.minutes())+" min"+(Math.abs(s.minutes())!=1?"s":""))),n.time=o.join(", ")}return n},bi=({expiryDate:t,status:n,compact:o=!1,showDetail:s=!0,extraText:a})=>{const{t:i}=B(),l=Nt(t),p=$e[n].icon;return r(me,{children:[r(L,{colorScheme:$e[n].statusColor,rounded:"full",display:"inline-flex",px:3,py:1,columnGap:o?1:2,alignItems:"center",children:[e(p,{w:o?3:4}),s&&r(c,{textTransform:"capitalize",fontSize:o?".7rem":".875rem",lineHeight:o?"1rem":"1.25rem",fontWeight:"medium",letterSpacing:"tighter",children:[n&&i(`nodeModal.status.${n}`),a&&`: ${a}`]})]}),s&&t&&e(c,{display:"inline-block",fontSize:"xs",fontWeight:"medium",ml:"2",color:"gray.600",_dark:{color:"gray.400"},children:i(l.status,{time:l.time})})]})},mt=C(Fe,{baseStyle:{bg:"white",_dark:{bg:"gray.700"}}}),xi=C(Qr,{baseStyle:{w:5,h:5}}),yi=C(bn,{baseStyle:{w:5,h:5,strokeWidth:2}}),Si=({toggleAccordion:t,node:n})=>{const{updateNode:o,reconnectNode:s,setDeletingNode:a}=Ct(),{t:i}=B(),l=tr(),p=ye(),f=Ge({defaultValues:n,resolver:et(po)}),w=a.bind(null,n),{isLoading:S,mutate:x}=bt(o,{onSuccess:()=>{sr("Node updated successfully",p),l.invalidateQueries(yt)},onError:b=>{nr(b,p,f)}}),{isLoading:h,mutate:m}=bt(s.bind(null,n),{onSuccess:()=>{l.invalidateQueries(yt)}}),I=h?"connecting":n.status?n.status:"error";return r(Qe,{border:"1px solid",_dark:{borderColor:"gray.600"},_light:{borderColor:"gray.200"},borderRadius:"4px",p:1,w:"full",children:[r(qe,{px:2,borderRadius:"3px",onClick:t,children:[r(_,{w:"full",justifyContent:"space-between",pr:2,children:[e(c,{as:"span",fontWeight:"medium",fontSize:"sm",flex:"1",textAlign:"left",color:"gray.700",_dark:{color:"gray.300"},children:n.name}),r(_,{children:[n.xray_version&&e(L,{colorScheme:"blue",rounded:"full",display:"inline-flex",px:3,py:1,children:r(c,{textTransform:"capitalize",fontSize:"0.7rem",fontWeight:"medium",letterSpacing:"tighter",children:["Xray ",n.xray_version]})}),n.status&&e(bi,{status:I,compact:!0})]})]}),e(Vt,{})]}),r(Xe,{px:2,pb:2,children:[e(O,{pb:3,alignItems:"flex-start",children:I==="error"&&e(Ve,{status:"error",size:"xs",children:r(v,{children:[r(_,{w:"full",children:[e(Je,{w:4}),e(c,{marginInlineEnd:0,children:n.message})]}),e(_,{justifyContent:"flex-end",w:"full",children:e(N,{size:"sm","aria-label":"reconnect node",leftIcon:e(or,{}),onClick:()=>m(),disabled:h,children:i(h?"nodes.reconnecting":"nodes.reconnect")})})]})})}),e(So,{form:f,mutate:x,isLoading:S,submitBtnText:i("nodes.editNode"),btnLeftAdornment:e(ne,{label:i("delete"),placement:"top",children:e(Y,{colorScheme:"red",variant:"ghost",size:"sm","aria-label":"delete node",onClick:w,children:e(_t,{})})})})]})]})},wi=({toggleAccordion:t,resetAccordions:n})=>{const o=ye(),{t:s}=B(),a=tr(),{addNode:i}=Ct(),l=Ge({resolver:et(po),defaultValues:{...fs(),add_as_new_host:!1}}),{isLoading:p,mutate:f}=bt(i,{onSuccess:()=>{sr(s("nodes.addNodeSuccess",{name:l.getValues("name")}),o),a.invalidateQueries(yt),l.reset(),n()},onError:w=>{nr(w,o,l)}});return r(Qe,{border:"1px solid",_dark:{borderColor:"gray.600"},_light:{borderColor:"gray.200"},borderRadius:"4px",p:1,w:"full",children:[e(qe,{px:2,borderRadius:"3px",onClick:t,children:r(c,{as:"span",fontWeight:"medium",fontSize:"sm",flex:"1",textAlign:"left",color:"gray.700",_dark:{color:"gray.300"},display:"flex",gap:1,children:[e(yi,{display:"inline-block"})," ",e("span",{children:s("nodes.addNewMarzbanNode")})]})}),e(Xe,{px:2,py:4,children:e(So,{form:l,mutate:f,isLoading:p,submitBtnText:s("nodes.addNode"),btnProps:{variant:"solid"},addAsHost:!0})})]})},So=({form:t,mutate:n,isLoading:o,submitBtnText:s,btnProps:a={},btnLeftAdornment:i,addAsHost:l=!1})=>{var m,I,b,T,u,d,H,$,z,P,Z,U,E,Q,J;const{t:p}=B(),[f,w]=g.exports.useState(!1),{data:S,isLoading:x}=Tt({queryKey:"node-settings",queryFn:()=>W("/node/settings")});function h(R){if(document.body.createTextRange){const V=document.body.createTextRange();V.moveToElementText(R),V.select()}else if(window.getSelection){const V=window.getSelection(),K=document.createRange();K.selectNodeContents(R),V.removeAllRanges(),V.addRange(K)}else console.warn("Could not select text in node: Unsupported browser.")}return e("form",{onSubmit:t.handleSubmit(R=>n(R)),children:r(O,{children:[S&&S.certificate&&e(Ve,{status:"info",alignItems:"start",children:r(ro,{display:"flex",flexDirection:"column",overflow:"hidden",children:[e("span",{children:p("nodes.connection-hint")}),r(_,{justify:"end",py:2,children:[e(N,{as:"a",colorScheme:"primary",size:"xs",download:"ssl_client_cert.pem",href:URL.createObjectURL(new Blob([S.certificate],{type:"text/plain"})),children:p("nodes.download-certificate")}),e(ne,{placement:"top",label:p("nodes.show-certificate"),children:e(Y,{"aria-label":p("nodes.show-certificate"),onClick:w.bind(null,!f),colorScheme:"whiteAlpha",color:"primary",size:"xs",children:f?e(yn,{width:"15px"}):e(xn,{width:"15px"})})})]}),e(oo,{in:f,animateOpacity:!0,children:e(c,{bg:"rgba(255,255,255,.5)",_dark:{bg:"rgba(255,255,255,.2)"},rounded:"md",p:"2",lineHeight:"1.2",fontSize:"10px",fontFamily:"Courier",whiteSpace:"pre",overflow:"auto",onClick:R=>{h(R.target)},children:S.certificate})})]})}),r(_,{w:"full",children:[e(A,{children:e(mt,{label:p("nodes.nodeName"),size:"sm",placeholder:"Marzban-S2",...t.register("name"),error:(b=(I=(m=t.formState)==null?void 0:m.errors)==null?void 0:I.name)==null?void 0:b.message})}),e(_,{px:1,children:e(se,{name:"status",control:t.control,render:({field:R})=>e(ne,{placement:"top",label:`${p("usersTable.status")}: `+(R.value!=="disabled"?p("active"):p("disabled")),textTransform:"capitalize",children:e(v,{mt:"6",children:e(Ut,{colorScheme:"primary",isChecked:R.value!=="disabled",onChange:V=>{V.target.checked?R.onChange("connecting"):R.onChange("disabled")}})})},R.value)})})]}),e(_,{alignItems:"flex-start",w:"100%",children:e(v,{w:"100%",children:e(mt,{label:p("nodes.nodeAddress"),size:"sm",placeholder:"***********",...t.register("address"),error:(d=(u=(T=t.formState)==null?void 0:T.errors)==null?void 0:u.address)==null?void 0:d.message})})}),r(_,{alignItems:"flex-start",w:"100%",children:[e(v,{children:e(mt,{label:p("nodes.nodePort"),size:"sm",placeholder:"62050",...t.register("port"),error:(z=($=(H=t.formState)==null?void 0:H.errors)==null?void 0:$.port)==null?void 0:z.message})}),e(v,{children:e(mt,{label:p("nodes.nodeAPIPort"),size:"sm",placeholder:"62051",...t.register("api_port"),error:(U=(Z=(P=t.formState)==null?void 0:P.errors)==null?void 0:Z.api_port)==null?void 0:U.message})}),e(v,{children:e(mt,{label:p("nodes.usageCoefficient"),size:"sm",placeholder:"1",...t.register("usage_coefficient"),error:(J=(Q=(E=t.formState)==null?void 0:E.errors)==null?void 0:Q.usage_coefficient)==null?void 0:J.message})})]}),l&&e(A,{py:1,children:e(Pe,{...t.register("add_as_new_host"),children:e(j,{m:0,children:p("nodes.addHostForEveryInbound")})})}),r(_,{w:"full",children:[i,e(N,{flexGrow:1,type:"submit",colorScheme:"primary",size:"sm",px:5,w:"full",isLoading:o,...a,children:s})]})]})})},Ci=()=>{const{isEditingNodes:t,onEditingNodes:n}=D(),{t:o}=B(),[s,a]=g.exports.useState({}),{data:i,isLoading:l}=mo(),p=()=>{a({}),n(!1)},f=w=>{s[String(w)]?delete s[String(w)]:s[String(w)]={},a({...s})};return r(me,{children:[r(ie,{isOpen:t,onClose:p,children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",w:"fit-content",maxW:"3xl",children:[e(ce,{pt:6,children:e(ge,{color:"primary",children:e(xi,{color:"white"})})}),e(de,{mt:3}),r(he,{w:"440px",pb:6,pt:3,children:[e(c,{mb:3,opacity:.8,fontSize:"sm",children:o("nodes.title")}),l&&"loading...",e(wt,{w:"full",allowToggle:!0,index:Object.keys(s).map(w=>parseInt(w)),children:r(O,{w:"full",children:[!l&&i&&i.map((w,S)=>e(Si,{toggleAccordion:()=>f(S),node:w},w.name)),e(wi,{toggleAccordion:()=>f((i||[]).length),resetAccordions:()=>a({})})]})})]})]})]}),e(mi,{deleteCallback:()=>a({})})]})};function _i(t){const n=360/t,o=90,s=47,a=[];for(let i=0;i<t;i++){const l=i*n%360+140,p=ki(l,o,s);a.push(p)}return a}function ki(t,n,o){t/=360,n/=100,o/=100;let s,a,i;if(n===0)s=a=i=o;else{const p=(S,x,h)=>(h<0&&(h+=1),h>1&&(h-=1),h<.16666666666666666?S+(x-S)*6*h:h<.5?x:h<.6666666666666666?S+(x-S)*(.6666666666666666-h)*6:S),f=o<.5?o*(1+n):o+n-o*n,w=2*o-f;s=Math.round(p(w,f,t+1/3)*255),a=Math.round(p(w,f,t)*255),i=Math.round(p(w,f,t-1/3)*255)}const l=p=>{const f=p.toString(16);return f.length===1?"0"+f:f};return`#${l(s)}${l(a)}${l(i)}`}const Ar=({border:t,...n})=>{const{getInputProps:o,getRadioProps:s}=vn(n),a=je({base:"xs",md:"sm"});return r(v,{as:"label",children:[e("input",{...o()}),e(v,{...s(),minW:"48px",w:"full",h:"full",textAlign:"center",cursor:"pointer",fontSize:a,borderWidth:t?"1px":"0px",borderRadius:"md",_checked:{bg:"primary.500",color:"white",borderColor:"primary.500"},_focus:{boxShadow:"outline"},px:3,py:1,children:n.children})]})},wo=({onChange:t,defaultValue:n,...o})=>{const{t:s,i18n:a}=B();Ke();const i=je({base:["7h","1d","3d","1w"],md:["7h","1d","3d","1w","1m","3m"]}),l={h:"hour",d:"day",w:"week",m:"month",y:"year"},p=je({base:[{title:"hours",options:["1h","3h","6h","12h"]},{title:"days",options:["1d","2d","3d","4d"]},{title:"weeks",options:["1w","2w","3w","4w"]},{title:"months",options:["1m","2m","3m","6m"]}],md:[{title:"hours",options:["1h","2h","3h","6h","8h","12h"]},{title:"days",options:["1d","2d","3d","4d","5d","6d"]},{title:"weeks",options:["1w","2w","3w","4w"]},{title:"months",options:["1m","2m","3m","6m","8m"]}]}),{getRootProps:f,getRadioProps:w,setValue:S}=Sn({name:"filter",defaultValue:n,onChange:R=>{if(R==="custom")return;m(),i.indexOf(R)>=0?(T(s("userDialog.custom")),d(!1)):(T(s("userDialog.custom")+` (${R})`),d(!0));const V=Number(R.substring(0,R.length-1)),K=l[R[R.length-1]];t(R,{start:G().utc().subtract(V,K).format("YYYY-MM-DDTHH:00:00")})}}),{isOpen:x,onOpen:h,onClose:m}=wn(),I=g.exports.useRef(null);Cn({ref:I,handler:m});const[b,T]=g.exports.useState(s("userDialog.custom")),[u,d]=g.exports.useState(!1),[H,$]=g.exports.useState(0),z=je({base:1,md:2}),P=je({base:"xs",md:"sm"}),[Z,U]=g.exports.useState(null),[E,Q]=g.exports.useState(null),J=R=>{const[V,K]=R;E&&!K?(U(null),Q(null)):(U(V),Q(K),V&&K&&(m(),t("custom",{start:G(V).format("YYYY-MM-DDT00:00:00"),end:G(K).format("YYYY-MM-DDT23:59:59")})))};return r(O,{...o,children:[H==0&&r(er,{...f(),gap:0,display:"flex",borderWidth:"1px",borderRadius:"md",minW:{base:"320px",md:"400px"},children:[i.map(R=>e(Ar,{...w({value:R}),children:R},R)),e(v,{onClick:()=>{U(null),Q(null),h()},cursor:"pointer",borderRadius:"md",w:"full",fontSize:P,px:3,py:1,bg:u?"primary.500":"unset",color:u?"white":"unset",borderColor:u?"primary.500":"unset",children:r(_,{children:[e(c,{children:b}),e(Pt,{as:br,boxSize:"18px"})]})})]}),H==1&&r(_,{onClick:h,cursor:"pointer",fontSize:P,borderRadius:"md",px:3,py:1,minW:{base:"320px",md:"400px"},borderWidth:"1px",children:[e(c,{w:"full",color:Z?"unset":"gray.500",children:Z?G(Z).format("YYYY-MM-DD (00:00)"):s("userDialog.startDate")}),e(Pt,{as:no,boxSize:"18px"}),e(c,{w:"full",color:E?"unset":"gray.500",children:E?G(E).format("YYYY-MM-DD (23:59)"):s("userDialog.endDate")}),e(Pt,{as:br,boxSize:"18px"})]}),e(O,{ref:I,marginTop:"40px !important",borderRadius:"md",borderWidth:"1px",position:"absolute",zIndex:"1",backgroundColor:"white",_dark:{backgroundColor:"gray.700"},display:x?"unset":"none",children:r(_n,{onChange:R=>$(R),children:[r(kn,{children:[e(xr,{fontSize:P,children:s("userDialog.relative")}),e(xr,{fontSize:P,children:s("userDialog.absolute")})]}),r(In,{children:[e(yr,{children:p.map(R=>e(O,{alignItems:"start",pl:2,pr:2,children:r(_,{justifyItems:"flex-start",mb:4,children:[e(c,{fontSize:P,minW:"60px",children:s("userDialog."+R.title)}),R.options.map(V=>e(Ar,{border:!0,...w({value:V}),children:V},V+".custom"))]})},R.title))}),e(yr,{className:"datepicker-panel",children:e(O,{children:e(so,{locale:a.language.toLocaleLowerCase(),selected:Z,onChange:J,startDate:Z,endDate:E,selectsRange:!0,maxDate:new Date,monthsShown:z,peekNextMonth:!1,inline:!0})})})]})]})})]})};function Rt(t,n,o=[],s=[]){const a=pe(o.reduce((i,l)=>i+=l,0));return{series:o,options:{labels:s,chart:{width:"100%",height:"100%",type:"donut",animations:{enabled:!1}},title:{text:`${n}${a}`,align:"center",style:{fontWeight:"var(--chakra-fontWeights-medium)",color:t==="dark"?"var(--chakra-colors-gray-300)":void 0}},legend:{position:"bottom",labels:{colors:t==="dark"?"#CBD5E0":void 0,useSeriesColors:!1}},stroke:{width:1,colors:void 0},dataLabels:{formatter:(i,{seriesIndex:l,w:p})=>pe(p.config.series[l],1)},tooltip:{custom:({series:i,seriesIndex:l,dataPointIndex:p,w:f})=>{const w=pe(i[l],1),S=Math.max(i.reduce((h,m)=>h+=m),1),x=Math.round(i[l]/S*1e3)/10+"%";return`
            <div style="
                    background-color: ${f.globals.colors[l]};
                    padding-left:12px;
                    padding-right:12px;
                    padding-top:6px;
                    padding-bottom:6px;
                    font-size:0.725rem;
                  "
            >
              ${f.config.labels[l]}: <b>${x}, ${w}</b>
            </div>
          `}},colors:_i(o.length)}}}const Ii=C(Lt,{baseStyle:{w:5,h:5}}),vi=()=>{const{isShowingNodesUsage:t,onShowingNodesUsage:n}=D(),{fetchNodesUsage:o}=Ct(),{t:s}=B(),[a,i]=g.exports.useState(!1),{colorMode:l}=Ke(),p=s("userDialog.total"),[f,w]=g.exports.useState(Rt(l,p)),[S,x]=g.exports.useState("1m"),h=b=>{o(b).then(T=>{const u=[],d=[];for(const H in T.usages){const $=T.usages[H];d.push($.uplink+$.downlink),u.push($.node_name)}w(Rt(l,p,d,u))})};g.exports.useEffect(()=>{t&&h({start:G().utc().subtract(30,"day").format("YYYY-MM-DDTHH:00:00")})},[t]);const m=()=>{n(!1),x("1m")},I=a;return r(ie,{isOpen:t,onClose:m,size:"2xl",children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",w:"full",children:[e(ce,{pt:6,children:r(_,{gap:2,children:[e(ge,{color:"primary",children:e(Ii,{color:"white"})}),e(c,{fontWeight:"semibold",fontSize:"lg",children:s("header.nodesUsage")})]})}),e(de,{mt:3,disabled:I}),e(he,{children:r(O,{gap:4,children:[e(wo,{defaultValue:S,onChange:(b,T)=>{x(b),h(T)}}),e(v,{justifySelf:"center",w:"full",maxW:"300px",mt:"4",children:e(g.exports.Suspense,{fallback:e($r,{isIndeterminate:!0}),children:e(io,{options:f.options,series:f.series,type:"donut",height:"500px"})})})]})}),e(ke,{mt:"3"})]})]})},Lr=C(Dn),Di=C(no,{baseStyle:{w:6,h:6,color:"gray.600",_dark:{color:"white"}}}),zi=C(zn,{baseStyle:{w:6,h:6,color:"gray.600",_dark:{color:"white"}}}),Ui=C(ao,{baseStyle:{w:5,h:5}}),Ei=()=>{const{QRcodeLinks:t,setQRCode:n,setSubLink:o,subscribeUrl:s}=D(),a=t!==null,[i,l]=g.exports.useState(0),{t:p}=B(),f=()=>{n(null),o(null)},w=String(s).startsWith("/")?window.location.origin+s:String(s);return r(ie,{isOpen:a,onClose:f,children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",w:"fit-content",maxW:"3xl",children:[e(ce,{pt:6,children:e(ge,{color:"primary",children:e(Ui,{color:"white"})})}),e(de,{mt:3}),t&&r(he,{gap:{base:"20px",lg:"50px"},pr:{lg:"60px"},px:{base:"50px"},display:"flex",justifyContent:"center",flexDirection:{base:"column",lg:"row"},children:[s&&r(O,{children:[e(Lr,{mx:"auto",size:300,p:"2",level:"L",includeMargin:!1,value:w,bg:"white"}),e(c,{display:"block",textAlign:"center",pb:3,mt:1,children:p("qrcodeDialog.sublink")})]}),r(v,{w:"300px",children:[e(Un,{centerPadding:"0px",centerMode:!0,slidesToShow:1,slidesToScroll:1,dots:!1,afterChange:l,onInit:()=>l(0),nextArrow:e(Y,{size:"sm",position:"absolute",display:"flex !important",_before:{content:'""'},"aria-label":"next",mr:"-4",children:e(Di,{})}),prevArrow:e(Y,{size:"sm",position:"absolute",display:"flex !important",_before:{content:'""'},"aria-label":"prev",ml:"-4",children:e(zi,{})}),children:t.map((S,x)=>e(_,{children:e(Lr,{mx:"auto",size:300,p:"2",level:"L",includeMargin:!1,value:S,bg:"white"})},x))}),r(c,{display:"block",textAlign:"center",pb:3,mt:1,children:[i+1," / ",t.length]})]})]})]})]})},Ri=C(qr,{baseStyle:{w:5,h:5}}),Ai=()=>{const[t,n]=g.exports.useState(!1),{isResetingAllUsage:o,onResetAllUsage:s,resetAllUsage:a}=D(),{t:i}=B(),l=ye(),p=()=>{s(!1)},f=()=>{n(!0),a().then(()=>{l({title:i("resetAllUsage.success"),status:"success",isClosable:!0,position:"top",duration:3e3})}).catch(()=>{l({title:i("resetAllUsage.error"),status:"error",isClosable:!0,position:"top",duration:3e3})}).finally(()=>{n(!1)})};return r(ie,{isCentered:!0,isOpen:o,onClose:p,size:"sm",children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",children:[e(ce,{pt:6,children:e(ge,{color:"red",children:e(Ri,{})})}),e(de,{mt:3}),r(he,{children:[e(c,{fontWeight:"semibold",fontSize:"lg",children:i("resetAllUsage.title")}),o&&e(c,{mt:1,fontSize:"sm",_dark:{color:"gray.400"},color:"gray.600",children:i("resetAllUsage.prompt")})]}),r(ke,{display:"flex",children:[e(N,{size:"sm",onClick:p,mr:3,w:"full",variant:"outline",children:i("cancel")}),e(N,{size:"sm",w:"full",colorScheme:"red",onClick:f,leftIcon:t?e(_e,{size:"xs"}):void 0,children:i("reset")})]})]})]})},Li=C(Mt,{baseStyle:{w:5,h:5}}),Ti=()=>{const[t,n]=g.exports.useState(!1),{resetUsageUser:o,resetDataUsage:s}=D(),{t:a}=B(),i=ye(),l=()=>{D.setState({resetUsageUser:null})},p=()=>{o&&(n(!0),s(o).then(()=>{i({title:a("resetUserUsage.success",{username:o.username}),status:"success",isClosable:!0,position:"top",duration:3e3})}).catch(()=>{i({title:a("resetUserUsage.error"),status:"error",isClosable:!0,position:"top",duration:3e3})}).finally(()=>{n(!1)}))};return r(ie,{isCentered:!0,isOpen:!!o,onClose:l,size:"sm",children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",children:[e(ce,{pt:6,children:e(ge,{color:"blue",children:e(Li,{})})}),e(de,{mt:3}),r(he,{children:[e(c,{fontWeight:"semibold",fontSize:"lg",children:a("resetUserUsage.title")}),o&&e(c,{mt:1,fontSize:"sm",_dark:{color:"gray.400"},color:"gray.600",children:e(Ne,{components:{b:e("b",{})},children:a("resetUserUsage.prompt",{username:o.username})})})]}),r(ke,{display:"flex",children:[e(N,{size:"sm",onClick:l,mr:3,w:"full",variant:"outline",children:a("cancel")}),e(N,{size:"sm",w:"full",colorScheme:"blue",onClick:p,leftIcon:t?e(_e,{size:"xs"}):void 0,children:a("reset")})]})]})]})},Mi=C(Mt,{baseStyle:{w:5,h:5}}),Ni=()=>{const[t,n]=g.exports.useState(!1),{revokeSubscriptionUser:o,revokeSubscription:s}=D(),{t:a}=B(),i=ye(),l=()=>{D.setState({revokeSubscriptionUser:null})},p=()=>{o&&(n(!0),s(o).then(()=>{i({title:a("revokeUserSub.success",{username:o.username}),status:"success",isClosable:!0,position:"top",duration:3e3})}).catch(()=>{i({title:a("revokeUserSub.error"),status:"error",isClosable:!0,position:"top",duration:3e3})}).finally(()=>{n(!1)}))};return r(ie,{isCentered:!0,isOpen:!!o,onClose:l,size:"sm",children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),r(le,{mx:"3",children:[e(ce,{pt:6,children:e(ge,{color:"blue",children:e(Mi,{})})}),e(de,{mt:3}),r(he,{children:[e(c,{fontWeight:"semibold",fontSize:"lg",children:a("revokeUserSub.title")}),o&&e(c,{mt:1,fontSize:"sm",_dark:{color:"gray.400"},color:"gray.600",children:e(Ne,{components:{b:e("b",{})},children:a("revokeUserSub.prompt",{username:o.username})})})]}),r(ke,{display:"flex",children:[e(N,{size:"sm",onClick:l,mr:3,w:"full",variant:"outline",children:a("cancel")}),e(N,{size:"sm",w:"full",colorScheme:"blue",onClick:p,leftIcon:t?e(_e,{size:"xs"}):void 0,children:a("revoke")})]})]})]})},Pi=C(En,{baseStyle:{w:5,h:5}}),Fi=C(lo,{baseStyle:{w:5,h:5}}),Wi=C(Lt,{baseStyle:{w:5,h:5}}),Oi=t=>({...t,data_limit:t.data_limit?Number((t.data_limit/1073741824).toFixed(5)):t.data_limit,on_hold_expire_duration:t.on_hold_expire_duration?Number(t.on_hold_expire_duration/(24*60*60)):t.on_hold_expire_duration,selected_proxies:Object.keys(t.proxies)}),Ht=()=>{const t=Object.fromEntries(D.getState().inbounds),n={};for(const o in t)n[o]=t[o].map(s=>s.tag);return{selected_proxies:Object.keys(t),data_limit:null,expire:null,username:"",data_limit_reset_strategy:"no_reset",status:"active",on_hold_expire_duration:null,note:"",inbounds:n,proxies:{vless:{id:"",flow:""},vmess:{id:""},trojan:{password:""},shadowsocks:{password:"",method:"chacha20-ietf-poly1305"}}}},Hi=(t,n)=>{const o=t.reduce((s,a)=>({...s,[a]:{}}),{});return n&&t.forEach(s=>{n[s]&&(o[s]=n[s])}),o},gt={username:y.string().min(1,{message:"Required"}),selected_proxies:y.array(y.string()).refine(t=>t.length>0,{message:"userDialog.selectOneProtocol"}),note:y.string().nullable(),proxies:y.record(y.string(),y.record(y.string(),y.any())).transform(t=>{const n=(o,s)=>{o&&o[s]===""&&delete o[s]};return n(t.vmess,"id"),n(t.vless,"id"),n(t.trojan,"password"),n(t.shadowsocks,"password"),n(t.shadowsocks,"method"),t}),data_limit:y.string().min(0).or(y.number()).nullable().transform(t=>t?Number((parseFloat(String(t))*1073741824).toFixed(5)):0),expire:y.number().nullable(),data_limit_reset_strategy:y.string(),inbounds:y.record(y.string(),y.array(y.string())).transform(t=>(Object.keys(t).forEach(n=>{var o;Array.isArray(t[n])&&!((o=t[n])!=null&&o.length)&&delete t[n]}),t))},ji=y.discriminatedUnion("status",[y.object({status:y.literal("active"),...gt}),y.object({status:y.literal("disabled"),...gt}),y.object({status:y.literal("limited"),...gt}),y.object({status:y.literal("expired"),...gt}),y.object({status:y.literal("on_hold"),on_hold_expire_duration:y.coerce.number().min(.1,"Required").transform(t=>t*(24*60*60)),...gt})]),Bi=()=>{var ot,nt,st,it,at;const{editingUser:t,isCreatingNewUser:n,onCreateUser:o,editUser:s,fetchUserUsage:a,onEditingUser:i,createUser:l,onDeletingUser:p}=D(),f=!!t,w=n||f,[S,x]=g.exports.useState(!1),[h,m]=g.exports.useState(""),I=ye(),{t:b,i18n:T}=B(),{colorMode:u}=Ke(),[d,H]=g.exports.useState(!1),$=()=>{H(k=>!k)},z=Ge({defaultValues:Ht(),resolver:et(ji)});g.exports.useEffect(()=>D.subscribe(k=>k.inbounds,()=>{z.reset(Ht())}),[]);const[P,Z]=eo({control:z.control,name:["data_limit","status"]}),U=b("userDialog.total"),[E,Q]=g.exports.useState(Rt(u,U)),[J,R]=g.exports.useState("1m"),V=k=>{a(t,k).then(F=>{const re=[],ue=[];for(const we in F.usages)ue.push(F.usages[we].used_traffic),re.push(F.usages[we].node_name);Q(Rt(u,U,ue,re))})};g.exports.useEffect(()=>{t&&(z.reset(Oi(t)),V({start:G().utc().subtract(30,"day").format("YYYY-MM-DDTHH:00:00")}))},[t]);const K=k=>{x(!0);const F={edited:s,created:l},re=f?"edited":"created";m(null);const{selected_proxies:ue,...we}=k;let ve={...we,data_limit:k.data_limit,proxies:Hi(ue,k.proxies),data_limit_reset_strategy:k.data_limit&&k.data_limit>0?k.data_limit_reset_strategy:"no_reset",status:k.status==="active"||k.status==="disabled"||k.status==="on_hold"?k.status:"active"};F[re](ve).then(()=>{I({title:b(f?"userDialog.userEdited":"userDialog.userCreated",{username:k.username}),status:"success",isClosable:!0,position:"top",duration:3e3}),M()}).catch(q=>{var lt,ct,dt,ht,ut;(((lt=q==null?void 0:q.response)==null?void 0:lt.status)===409||((ct=q==null?void 0:q.response)==null?void 0:ct.status)===400)&&m((ht=(dt=q==null?void 0:q.response)==null?void 0:dt._data)==null?void 0:ht.detail),((ut=q==null?void 0:q.response)==null?void 0:ut.status)===422&&Object.keys(q.response._data.detail).forEach(Ye=>{m(q==null?void 0:q.response._data.detail[Ye]),z.setError(Ye,{type:"custom",message:q.response._data.detail[Ye]})})}).finally(()=>{x(!1)})},M=()=>{z.reset(Ht()),o(!1),i(null),m(null),H(!1),R("1m")},te=()=>{D.setState({resetUsageUser:t})},fe=()=>{D.setState({revokeSubscriptionUser:t})},ee=S,Ie=Z==="on_hold",[It,rt]=g.exports.useState(!1),vt=()=>{rt(!0);let k="";const F="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",re=F.length;let ue=0;for(;ue<6;)k+=F.charAt(Math.floor(Math.random()*re)),ue+=1;return k};return r(ie,{isOpen:w,onClose:M,size:"2xl",children:[e(ae,{bg:"blackAlpha.300",backdropFilter:"blur(10px)"}),e(Jt,{...z,children:e(le,{mx:"3",children:r("form",{onSubmit:z.handleSubmit(K),children:[e(ce,{pt:6,children:r(_,{gap:2,children:[e(ge,{color:"primary",children:f?e(Fi,{color:"white"}):e(Pi,{color:"white"})}),e(c,{fontWeight:"semibold",fontSize:"lg",children:b(f?"userDialog.editUserTitle":"createNewUser")})]})}),e(de,{mt:3,disabled:ee}),r(he,{children:[r(qt,{templateColumns:{base:"repeat(1, 1fr)",md:"repeat(2, 1fr)"},gap:3,children:[e(Be,{children:r(O,{justifyContent:"space-between",children:[r(Ft,{flexDirection:"column",gridAutoRows:"min-content",w:"full",children:[r(Ft,{flexDirection:"row",w:"full",gap:2,children:[r(A,{mb:"10px",children:[e(j,{children:r(Ft,{gap:2,alignItems:"center",children:[b("username"),!f&&e(or,{cursor:"pointer",className:St({"animate-spin":It}),onClick:()=>{const k=vt();z.setValue("username",k),setTimeout(()=>{rt(!1)},350)}})]})}),r(_,{children:[e(Fe,{size:"sm",type:"text",borderRadius:"6px",error:(ot=z.formState.errors.username)==null?void 0:ot.message,disabled:ee||f,...z.register("username")}),f&&e(_,{px:1,children:e(se,{name:"status",control:z.control,render:({field:k})=>e(ne,{placement:"top",label:"status: "+b(`status.${k.value}`),textTransform:"capitalize",children:e(v,{children:e(Ut,{colorScheme:"primary",isChecked:k.value==="active",onChange:F=>{F.target.checked?k.onChange("active"):k.onChange("disabled")}})})})})})]})]}),!f&&r(A,{flex:"1",children:[e(j,{whiteSpace:"nowrap",children:b("userDialog.onHold")}),e(se,{name:"status",control:z.control,render:({field:k})=>{const F=k.value;return e(me,{children:F?e(Ut,{colorScheme:"primary",isChecked:F==="on_hold",onChange:re=>{re.target.checked?k.onChange("on_hold"):k.onChange("active")}}):""})}})]})]}),r(A,{mb:"10px",children:[e(j,{children:b("userDialog.dataLimit")}),e(se,{control:z.control,name:"data_limit",render:({field:k})=>{var F;return e(Fe,{endAdornment:"GB",type:"number",size:"sm",borderRadius:"6px",onChange:k.onChange,disabled:ee,error:(F=z.formState.errors.data_limit)==null?void 0:F.message,value:k.value?String(k.value):""})}})]}),e(oo,{in:!!(P&&P>0),animateOpacity:!0,style:{width:"100%"},children:r(A,{height:"66px",children:[e(j,{children:b("userDialog.periodicUsageReset")}),e(se,{control:z.control,name:"data_limit_reset_strategy",render:({field:k})=>e(We,{size:"sm",...k,disabled:ee,bg:ee?"gray.100":"transparent",_dark:{bg:ee?"gray.600":"transparent"},sx:{option:{backgroundColor:u==="dark"?"#222C3B":"white"}},children:Zt.map(F=>e("option",{value:F.value,children:b("userDialog.resetStrategy"+F.title)},F.value))})})]})}),r(A,{mb:"10px",children:[e(j,{children:b(Ie?"userDialog.onHoldExpireDuration":"userDialog.expiryDate")}),Ie&&e(se,{control:z.control,name:"on_hold_expire_duration",render:({field:k})=>{var F;return e(Fe,{endAdornment:"Days",type:"number",size:"sm",borderRadius:"6px",onChange:re=>{z.setValue("expire",null),k.onChange({target:{value:re}})},disabled:ee,error:(F=z.formState.errors.on_hold_expire_duration)==null?void 0:F.message,value:k.value?String(k.value):""})}}),!Ie&&e(se,{name:"expire",control:z.control,render:({field:k})=>{var we;function F(ve){return G(G(ve*1e3).utc()).toDate()}const{status:re,time:ue}=Nt(k.value);return r(me,{children:[e(so,{locale:T.language.toLocaleLowerCase(),dateFormat:b("dateFormat"),minDate:new Date,selected:k.value?F(k.value):void 0,onChange:ve=>{z.setValue("on_hold_expire_duration",null),k.onChange({target:{value:ve?G(G(ve).set("hour",23).set("minute",59).set("second",59)).utc().valueOf()/1e3:0,name:"expire"}})},customInput:e(Fe,{size:"sm",type:"text",borderRadius:"6px",clearable:!0,disabled:ee,error:(we=z.formState.errors.expire)==null?void 0:we.message})}),k.value?e(Rn,{children:b(re,{time:ue})}):""]})}})]}),r(A,{mb:"10px",isInvalid:!!z.formState.errors.note,children:[e(j,{children:b("userDialog.note")}),e(An,{...z.register("note")}),e(xt,{children:(st=(nt=z.formState.errors)==null?void 0:nt.note)==null?void 0:st.message})]})]}),h&&r(Ve,{status:"error",display:{base:"none",md:"flex"},children:[e(Je,{}),h]})]})}),e(Be,{children:r(A,{isInvalid:!!((it=z.formState.errors.selected_proxies)!=null&&it.message),children:[e(j,{children:b("userDialog.protocols")}),e(se,{control:z.control,name:"selected_proxies",render:({field:k})=>e(yo,{list:[{title:"vmess",description:b("userDialog.vmessDesc")},{title:"vless",description:b("userDialog.vlessDesc")},{title:"trojan",description:b("userDialog.trojanDesc")},{title:"shadowsocks",description:b("userDialog.shadowsocksDesc")}],disabled:ee,...k})}),e(xt,{children:b((at=z.formState.errors.selected_proxies)==null?void 0:at.message)})]})}),f&&d&&e(Be,{pt:6,colSpan:{base:1,md:2},children:r(O,{gap:4,children:[e(wo,{defaultValue:J,onChange:(k,F)=>{R(k),V(F)}}),e(v,{width:{base:"100%",md:"70%"},justifySelf:"center",children:e(io,{options:E.options,series:E.series,type:"donut"})})]})})]}),h&&r(Ve,{mt:"3",status:"error",display:{base:"flex",md:"none"},children:[e(Je,{}),h]})]}),e(ke,{mt:"3",children:r(_,{justifyContent:"space-between",w:"full",gap:3,flexDirection:{base:"column",sm:"row"},children:[e(_,{justifyContent:"flex-start",w:{base:"full",sm:"unset"},children:f&&r(me,{children:[e(ne,{label:b("delete"),placement:"top",children:e(Y,{"aria-label":"Delete",size:"sm",onClick:()=>{p(t),M()},children:e(_t,{})})}),e(ne,{label:b("userDialog.usage"),placement:"top",children:e(Y,{"aria-label":"usage",size:"sm",onClick:$,children:e(Wi,{})})}),e(N,{onClick:te,size:"sm",children:b("userDialog.resetUsage")}),e(N,{onClick:fe,size:"sm",children:b("userDialog.revokeSubscription")})]})}),e(_,{w:"full",maxW:{md:"50%",base:"full"},justify:"end",children:e(N,{type:"submit",size:"sm",px:"8",colorScheme:"primary",leftIcon:S?e(_e,{size:"xs"}):void 0,disabled:ee,children:b(f?"userDialog.editUser":"createUser")})})]})})]})})})]})},$i=t=>g.exports.createElement("svg",{xmlns:"http://www.w3.org/2000/svg","data-name":"Layer 1",width:782.04441,height:701.88002,viewBox:"0 0 782.04441 701.88002",xmlnsXlink:"http://www.w3.org/1999/xlink",...t},g.exports.createElement("path",{d:"M609.48783,100.59015l-25.44631,6.56209L270.53735,187.9987,245.091,194.56079A48.17927,48.17927,0,0,0,210.508,253.17865L320.849,681.05606a48.17924,48.17924,0,0,0,58.61776,34.58317l.06572-.01695,364.26536-93.93675.06572-.01695a48.17923,48.17923,0,0,0,34.58309-58.6178l-110.341-427.87741A48.17928,48.17928,0,0,0,609.48783,100.59015Z",transform:"translate(-208.9778 -99.05999)",fill:"#f2f2f2"}),g.exports.createElement("path",{d:"M612.94784,114.00532l-30.13945,7.77236L278.68955,200.20385l-30.139,7.77223a34.30949,34.30949,0,0,0-24.6275,41.74308l110.341,427.87741a34.30946,34.30946,0,0,0,41.7431,24.62736l.06572-.01695,364.26536-93.93674.06619-.01707a34.30935,34.30935,0,0,0,24.627-41.7429l-110.341-427.87741A34.30938,34.30938,0,0,0,612.94784,114.00532Z",transform:"translate(-208.9778 -99.05999)",fill:"#fff"}),g.exports.createElement("path",{d:"M590.19,252.56327,405.917,300.08359a8.01411,8.01411,0,0,1-4.00241-15.52046l184.273-47.52033A8.01412,8.01412,0,0,1,590.19,252.56327Z",transform:"translate(-208.9778 -99.05999)",fill:"#f2f2f2"}),g.exports.createElement("path",{d:"M628.955,270.49906,412.671,326.27437a8.01411,8.01411,0,1,1-4.00241-15.52046l216.284-55.77531a8.01411,8.01411,0,0,1,4.00242,15.52046Z",transform:"translate(-208.9778 -99.05999)",fill:"#f2f2f2"}),g.exports.createElement("path",{d:"M620.45825,369.93676l-184.273,47.52032a8.01411,8.01411,0,1,1-4.00242-15.52046l184.273-47.52032a8.01411,8.01411,0,1,1,4.00241,15.52046Z",transform:"translate(-208.9778 -99.05999)",fill:"#f2f2f2"}),g.exports.createElement("path",{d:"M659.22329,387.87255l-216.284,55.77531a8.01411,8.01411,0,1,1-4.00242-15.52046l216.284-55.77531a8.01411,8.01411,0,0,1,4.00242,15.52046Z",transform:"translate(-208.9778 -99.05999)",fill:"#f2f2f2"}),g.exports.createElement("path",{d:"M650.72653,487.31025l-184.273,47.52033a8.01412,8.01412,0,0,1-4.00242-15.52047l184.273-47.52032a8.01411,8.01411,0,0,1,4.00242,15.52046Z",transform:"translate(-208.9778 -99.05999)",fill:"#f2f2f2"}),g.exports.createElement("path",{d:"M689.49156,505.246l-216.284,55.77532a8.01412,8.01412,0,1,1-4.00241-15.52047l216.284-55.77531a8.01411,8.01411,0,0,1,4.00242,15.52046Z",transform:"translate(-208.9778 -99.05999)",fill:"#f2f2f2"}),g.exports.createElement("path",{d:"M374.45884,348.80871l-65.21246,16.817a3.847,3.847,0,0,1-4.68062-2.76146L289.5963,304.81607a3.847,3.847,0,0,1,2.76145-4.68061l65.21247-16.817a3.847,3.847,0,0,1,4.68061,2.76145l14.96947,58.04817A3.847,3.847,0,0,1,374.45884,348.80871Z",transform:"translate(-208.9778 -99.05999)",fill:"#e6e6e6"}),g.exports.createElement("path",{d:"M404.72712,466.1822l-65.21247,16.817a3.847,3.847,0,0,1-4.68062-2.76146l-14.96946-58.04816A3.847,3.847,0,0,1,322.626,417.509l65.21246-16.817a3.847,3.847,0,0,1,4.68062,2.76145l14.96946,58.04817A3.847,3.847,0,0,1,404.72712,466.1822Z",transform:"translate(-208.9778 -99.05999)",fill:"#e6e6e6"}),g.exports.createElement("path",{d:"M434.99539,583.55569l-65.21246,16.817a3.847,3.847,0,0,1-4.68062-2.76145l-14.96946-58.04817a3.847,3.847,0,0,1,2.76145-4.68062l65.21247-16.817a3.847,3.847,0,0,1,4.68061,2.76146l14.96947,58.04816A3.847,3.847,0,0,1,434.99539,583.55569Z",transform:"translate(-208.9778 -99.05999)",fill:"#e6e6e6"}),g.exports.createElement("path",{d:"M863.63647,209.0517H487.31811a48.17928,48.17928,0,0,0-48.125,48.12512V699.05261a48.17924,48.17924,0,0,0,48.125,48.12507H863.63647a48.17924,48.17924,0,0,0,48.125-48.12507V257.17682A48.17928,48.17928,0,0,0,863.63647,209.0517Z",transform:"translate(-208.9778 -99.05999)",fill:"#e6e6e6"}),g.exports.createElement("path",{d:"M863.637,222.90589H487.31811a34.30948,34.30948,0,0,0-34.271,34.27093V699.05261a34.30947,34.30947,0,0,0,34.271,34.27088H863.637a34.30936,34.30936,0,0,0,34.27051-34.27088V257.17682A34.30937,34.30937,0,0,0,863.637,222.90589Z",transform:"translate(-208.9778 -99.05999)",fill:"#fff"}),g.exports.createElement("circle",{cx:694.19401,cy:614.02963,r:87.85039,fill:"#3182CE"}),g.exports.createElement("path",{d:"M945.18722,701.63087H914.63056V671.07421a11.45875,11.45875,0,0,0-22.9175,0v30.55666H861.1564a11.45875,11.45875,0,0,0,0,22.9175h30.55666V755.105a11.45875,11.45875,0,1,0,22.9175,0V724.54837h30.55666a11.45875,11.45875,0,0,0,0-22.9175Z",transform:"translate(-208.9778 -99.05999)",fill:"#fff"}),g.exports.createElement("path",{d:"M807.00068,465.71551H616.699a8.01412,8.01412,0,1,1,0-16.02823H807.00068a8.01412,8.01412,0,0,1,0,16.02823Z",transform:"translate(-208.9778 -99.05999)",fill:"#e6e6e6"}),g.exports.createElement("path",{d:"M840.05889,492.76314H616.699a8.01412,8.01412,0,1,1,0-16.02823H840.05889a8.01411,8.01411,0,1,1,0,16.02823Z",transform:"translate(-208.9778 -99.05999)",fill:"#e6e6e6"}),g.exports.createElement("path",{d:"M807.00068,586.929H616.699a8.01412,8.01412,0,1,1,0-16.02823H807.00068a8.01411,8.01411,0,0,1,0,16.02823Z",transform:"translate(-208.9778 -99.05999)",fill:"#e6e6e6"}),g.exports.createElement("path",{d:"M840.05889,613.97661H616.699a8.01412,8.01412,0,1,1,0-16.02823H840.05889a8.01412,8.01412,0,1,1,0,16.02823Z",transform:"translate(-208.9778 -99.05999)",fill:"#e6e6e6"}),g.exports.createElement("path",{d:"M574.07028,505.04162H506.72434a3.847,3.847,0,0,1-3.84278-3.84278V441.25158a3.847,3.847,0,0,1,3.84278-3.84278h67.34594a3.847,3.847,0,0,1,3.84278,3.84278v59.94726A3.847,3.847,0,0,1,574.07028,505.04162Z",transform:"translate(-208.9778 -99.05999)",fill:"#e6e6e6"}),g.exports.createElement("path",{d:"M574.07028,626.25509H506.72434a3.847,3.847,0,0,1-3.84278-3.84278V562.46505a3.847,3.847,0,0,1,3.84278-3.84278h67.34594a3.847,3.847,0,0,1,3.84278,3.84278v59.94726A3.847,3.847,0,0,1,574.07028,626.25509Z",transform:"translate(-208.9778 -99.05999)",fill:"#e6e6e6"}),g.exports.createElement("path",{d:"M807.21185,330.781H666.91017a8.01411,8.01411,0,0,1,0-16.02823H807.21185a8.01411,8.01411,0,0,1,0,16.02823Z",transform:"translate(-208.9778 -99.05999)",fill:"#ccc"}),g.exports.createElement("path",{d:"M840.27007,357.82862H666.91017a8.01411,8.01411,0,1,1,0-16.02822h173.3599a8.01411,8.01411,0,0,1,0,16.02822Z",transform:"translate(-208.9778 -99.05999)",fill:"#ccc"}),g.exports.createElement("path",{d:"M635.85911,390.6071H506.51316a3.847,3.847,0,0,1-3.84277-3.84277V285.81706a3.847,3.847,0,0,1,3.84277-3.84277H635.85911a3.847,3.847,0,0,1,3.84277,3.84277V386.76433A3.847,3.847,0,0,1,635.85911,390.6071Z",transform:"translate(-208.9778 -99.05999)",fill:"#ccc"})),Vi=t=>{if(!t)return null;const n=new Date(`${t}Z`);return Math.floor(n.getTime()/1e3)},Tr=({lastOnline:t})=>{const n=Math.floor(Date.now()/1e3),o=Vi(t);return!t||o===null?e(v,{border:"1px solid",borderColor:"gray.400",_dark:{borderColor:"gray.600"},className:"circle"}):n-o<=60?e(v,{bg:"green.300",_dark:{bg:"green.500"},className:"circle pulse green"}):e(v,{bg:"gray.400",_dark:{bg:"gray.600"},className:"circle"})},Gi=t=>{if(!t)return null;const n=new Date(t+"Z");return Math.floor(n.getTime()/1e3)},Mr=({lastOnline:t})=>{const n=Math.floor(Date.now()/1e3),o=Gi(t),s=o?n-o:null,a=o?Nt(o):{status:"",time:"Not Connected Yet"};return e(c,{display:"inline-block",fontSize:"xs",fontWeight:"medium",ml:"2",color:"gray.600",_dark:{color:"gray.400"},children:s&&s<=60?"Online":s?`${a.time} ago`:a.time})},Yi=C(Ln,{baseStyle:{w:4,h:4}}),Zi=C(Tn,{baseStyle:{w:4,h:4}}),Nr=5;function Qi(t,n,o){if(o<Nr)throw new Error(`Must allow at least ${Nr} page items`);if(o%2===0)throw new Error("Must allow odd number of page items");if(t<o)return[...new Array(t).keys()];const s=Math.max(0,Math.min(t-o,n-Math.floor(o/2))),a=new Array(o);for(let i=0;i<o;i+=1)a[i]=i+s;return a[0]>0&&(a[0]=0,a[1]="prev-more"),a[a.length-1]<t-1&&(a[a.length-1]=t-1,a[a.length-2]="next-more"),a}const qi=()=>{const{filters:t,onFilterChange:n,users:{total:o}}=D(),{limit:s,offset:a}=t,i=(a||0)/(s||1),l=Math.ceil(o/(s||1)),p=Qi(l,i,7),f=x=>{n({...t,offset:x*s})},w=x=>{n({...t,limit:parseInt(x.target.value)}),us(x.target.value)},{t:S}=B();return r(_,{justifyContent:"space-between",mt:4,w:"full",display:"flex",columnGap:{lg:4,md:0},rowGap:{md:0,base:4},flexDirection:{md:"row",base:"column"},children:[e(v,{order:{base:2,md:1},children:r(_,{children:[r(We,{minW:"60px",value:s,onChange:w,size:"sm",rounded:"md",children:[e("option",{children:"10"}),e("option",{children:"20"}),e("option",{children:"30"})]}),e(c,{whiteSpace:"nowrap",fontSize:"sm",children:S("itemsPerPage")})]})}),r(Mn,{size:"sm",isAttached:!0,variant:"outline",order:{base:1,md:2},children:[e(N,{leftIcon:e(Yi,{}),onClick:f.bind(null,i-1),isDisabled:i===0||l===0,children:S("previous")}),p.map(x=>typeof x=="string"?e(N,{children:"..."},x):e(N,{variant:x===i?"solid":"outline",onClick:f.bind(null,x),children:x+1},x)),e(N,{rightIcon:e(Zi,{}),onClick:f.bind(null,i+1),isDisabled:i+1===l||l===0,children:S("next")})]})]})},jt=({expiryDate:t,status:n,compact:o=!1,showDetail:s=!0,extraText:a})=>{const{t:i}=B(),l=Nt(t),p=$e[n].icon;return r(me,{children:[r(L,{colorScheme:$e[n].statusColor,rounded:"full",display:"inline-flex",px:3,py:1,columnGap:o?1:2,alignItems:"center",children:[e(p,{w:o?3:4}),s&&r(c,{textTransform:"capitalize",fontSize:o?".7rem":".875rem",lineHeight:o?"1rem":"1.25rem",fontWeight:"medium",letterSpacing:"tighter",children:[n&&i(`status.${n}`),a&&`: ${a}`]})]}),s&&t&&e(c,{display:"inline-block",fontSize:"xs",fontWeight:"medium",ml:"2",color:"gray.600",_dark:{color:"gray.400"},children:i(l.status,{time:l.time})})]})},Xi=C($i),tt={baseStyle:{w:{base:4,md:5},h:{base:4,md:5}}},Ji=C(Nn,tt),Ki=C(co,tt),Pr=C(Pn,tt),ea=C(Xt,tt),ta=C(ao,tt),ra=C(lo,tt),oa=C(co,{baseStyle:{width:"15px",height:"15px"}}),na=t=>{for(var n=0;n<Zt.length;n++){const o=Zt[n];if(o.value==t)return o.title}return"No"},sa=t=>{const{used:n,total:o,dataLimitResetStrategy:s,totalUsedTraffic:a}=t,i=o===0||o===null;return e(_,{justifyContent:"space-between",fontSize:"xs",fontWeight:"medium",color:"gray.600",_dark:{color:"gray.400"},children:r(c,{children:[pe(n)," /"," ",i?e(c,{as:"span",fontFamily:"system-ui",children:"\u221E"}):pe(o)]})})},Fr=t=>{const{used:n,total:o,dataLimitResetStrategy:s,totalUsedTraffic:a,...i}=t,l=o===0||o===null,p=!l&&n/o*100>=100;return r(me,{children:[e(Fn,{orientation:"horizontal",value:l?100:Math.min(n/o*100,100),colorScheme:p?"red":"primary",...i,children:e(Wn,{h:"6px",borderRadius:"full",children:e(On,{borderRadius:"full"})})}),r(_,{justifyContent:"space-between",fontSize:"xs",fontWeight:"medium",color:"gray.600",_dark:{color:"gray.400"},children:[r(c,{children:[pe(n)," /"," ",l?e(c,{as:"span",fontFamily:"system-ui",children:"\u221E"}):pe(o)+(s&&s!=="no_reset"?" "+X("userDialog.resetStrategy"+na(s)):"")]}),r(c,{children:[X("usersTable.total"),": ",pe(a)]})]})]})},ft=({sort:t,column:n})=>t.includes(n)?e(oa,{transform:t.startsWith("-")?void 0:"rotate(180deg)"}):null,ia=t=>{const{filters:n,users:{users:o},users:s,onEditingUser:a,onFilterChange:i}=D(),{t:l}=B(),[p,f]=g.exports.useState(void 0),w=je({base:120,lg:72})||72,[S,x]=g.exports.useState(`${w}px`),h=je({base:!1,md:!0});g.exports.useEffect(()=>{const u=()=>{const d=document.querySelectorAll("#filters")[0];x(`${d.offsetHeight}px`)};window.addEventListener("scroll",u)},[]);const m=o.length!==s.total,I=u=>{let d=n.sort;d.includes(u)?d.startsWith("-")?d="-created_at":d="-"+u:d=u,i({sort:d})},b=u=>{i({status:u.target.value.length>0?u.target.value:void 0})},T=u=>{f(u===p?void 0:u)};return r(v,{id:"users-table",overflowX:{base:"unset",md:"unset"},children:[e(wt,{allowMultiple:!0,display:{base:"block",md:"none"},index:p,children:r(Sr,{orientation:"vertical",zIndex:"docked",...t,children:[e(wr,{zIndex:"docked",position:"relative",children:r(Ze,{children:[e(Te,{position:"sticky",top:S,minW:"120px",pl:4,pr:4,cursor:"pointer",onClick:I.bind(null,"username"),children:r(_,{children:[e("span",{children:l("users")}),e(ft,{sort:n.sort,column:"username"})]})}),e(Te,{position:"sticky",top:S,minW:"50px",pl:0,pr:0,w:"140px",cursor:"pointer",children:r(_,{spacing:0,position:"relative",children:[r(c,{position:"absolute",_dark:{bg:"gray.750"},_light:{bg:"#F9FAFB"},userSelect:"none",pointerEvents:"none",zIndex:1,w:"100%",children:[l("usersTable.status"),n.status?": "+n.status:""]}),r(We,{value:n.sort,fontSize:"xs",fontWeight:"extrabold",textTransform:"uppercase",cursor:"pointer",p:0,border:0,h:"auto",w:"auto",icon:e(me,{}),_focusVisible:{border:"0 !important"},onChange:b,children:[e("option",{}),e("option",{children:"active"}),e("option",{children:"on_hold"}),e("option",{children:"disabled"}),e("option",{children:"limited"}),e("option",{children:"expired"})]})]})}),e(Te,{position:"sticky",top:S,minW:"100px",cursor:"pointer",pr:0,onClick:I.bind(null,"used_traffic"),children:r(_,{children:[e("span",{children:l("usersTable.dataUsage")}),e(ft,{sort:n.sort,column:"used_traffic"})]})}),e(Te,{position:"sticky",top:S,minW:"32px",w:"32px",p:0,cursor:"pointer"})]})}),e(Cr,{children:!h&&(o==null?void 0:o.map((u,d)=>r(g.exports.Fragment,{children:[r(Ze,{onClick:T.bind(null,d),cursor:"pointer",children:[e(Ce,{borderBottom:0,minW:"100px",pl:4,pr:4,maxW:"calc(100vw - 50px - 32px - 100px - 48px)",children:r("div",{className:"flex-status",children:[e(Tr,{lastOnline:u.online_at}),e(c,{isTruncated:!0,children:u.username})]})}),e(Ce,{borderBottom:0,minW:"50px",pl:0,pr:0,children:e(jt,{compact:!0,showDetail:!1,expiryDate:u.expire,status:u.status})}),e(Ce,{borderBottom:0,minW:"100px",pr:0,children:e(sa,{totalUsedTraffic:u.lifetime_used_traffic,dataLimitResetStrategy:u.data_limit_reset_strategy,used:u.used_traffic,total:u.data_limit,colorScheme:$e[u.status].bandWidthColor})}),e(Ce,{p:0,borderBottom:0,w:"32px",minW:"32px",children:e(Ki,{color:"gray.600",_dark:{color:"gray.400"},transition:"transform .2s ease-out",transform:p===d?"rotate(180deg)":"0deg"})})]}),e(Ze,{className:"collapsible",onClick:T.bind(null,d),children:e(Ce,{p:0,colSpan:4,children:r(Qe,{border:0,children:[e(qe,{display:"none"}),e(Xe,{border:0,cursor:"pointer",px:6,py:3,children:r(O,{justifyContent:"space-between",spacing:"4",children:[r(O,{alignItems:"flex-start",w:"full",spacing:-1,children:[e(c,{textTransform:"capitalize",fontSize:"xs",fontWeight:"bold",color:"gray.600",_dark:{color:"gray.400"},children:l("usersTable.dataUsage")}),e(v,{width:"full",minW:"230px",children:e(Fr,{totalUsedTraffic:u.lifetime_used_traffic,dataLimitResetStrategy:u.data_limit_reset_strategy,used:u.used_traffic,total:u.data_limit,colorScheme:$e[u.status].bandWidthColor})})]}),r(_,{w:"full",justifyContent:"space-between",children:[r(v,{width:"full",children:[e(jt,{compact:!0,expiryDate:u.expire,status:u.status}),e(Mr,{lastOnline:u.online_at})]}),r(_,{children:[e(Wr,{user:u}),e(ne,{label:l("userDialog.editUser"),placement:"top",children:e(Y,{p:"0 !important","aria-label":"Edit user",bg:"transparent",_dark:{_hover:{bg:"gray.700"}},size:{base:"sm",md:"md"},onClick:H=>{H.stopPropagation(),a(u)},children:e(ra,{})})})]})]})]})})]})})})]},u.username)))})]})}),r(Sr,{orientation:"vertical",display:{base:"none",md:"table"},...t,children:[e(wr,{zIndex:"docked",position:"relative",children:r(Ze,{children:[e(Te,{position:"sticky",top:{base:"unset",md:S},minW:"140px",cursor:"pointer",onClick:I.bind(null,"username"),children:r(_,{children:[e("span",{children:l("username")}),e(ft,{sort:n.sort,column:"username"})]})}),e(Te,{position:"sticky",top:{base:"unset",md:S},width:"400px",minW:"150px",cursor:"pointer",children:r(_,{position:"relative",gap:"5px",children:[r(c,{_dark:{bg:"gray.750"},_light:{bg:"#F9FAFB"},userSelect:"none",pointerEvents:"none",zIndex:1,children:[l("usersTable.status"),n.status?": "+n.status:""]}),e(c,{children:"/"}),e(ft,{sort:n.sort,column:"expire"}),e(_,{onClick:I.bind(null,"expire"),children:e(c,{children:"Sort by expire"})}),r(We,{fontSize:"xs",fontWeight:"extrabold",textTransform:"uppercase",cursor:"pointer",position:"absolute",p:0,left:"-40px",border:0,h:"auto",w:"auto",icon:e(me,{}),_focusVisible:{border:"0 !important"},value:n.sort,onChange:b,children:[e("option",{}),e("option",{children:"active"}),e("option",{children:"on_hold"}),e("option",{children:"disabled"}),e("option",{children:"limited"}),e("option",{children:"expired"})]})]})}),e(Te,{position:"sticky",top:{base:"unset",md:S},width:"350px",minW:"230px",cursor:"pointer",onClick:I.bind(null,"used_traffic"),children:r(_,{children:[e("span",{children:l("usersTable.dataUsage")}),e(ft,{sort:n.sort,column:"used_traffic"})]})}),e(Te,{position:"sticky",top:{base:"unset",md:S},width:"200px",minW:"180px"})]})}),r(Cr,{children:[h&&(o==null?void 0:o.map((u,d)=>r(Ze,{className:St("interactive",{"last-row":d===o.length-1}),onClick:()=>a(u),children:[e(Ce,{minW:"140px",children:r("div",{className:"flex-status",children:[e(Tr,{lastOnline:u.online_at}),u.username,e(Mr,{lastOnline:u.online_at})]})}),e(Ce,{width:"400px",minW:"150px",children:e(jt,{expiryDate:u.expire,status:u.status})}),e(Ce,{width:"350px",minW:"230px",children:e(Fr,{totalUsedTraffic:u.lifetime_used_traffic,dataLimitResetStrategy:u.data_limit_reset_strategy,used:u.used_traffic,total:u.data_limit,colorScheme:$e[u.status].bandWidthColor})}),e(Ce,{width:"200px",minW:"180px",children:e(Wr,{user:u})})]},u.username))),o.length==0&&e(Ze,{children:e(Ce,{colSpan:4,children:e(aa,{isFiltered:m})})})]})]}),e(qi,{})]})},Wr=({user:t})=>{const{setQRCode:n,setSubLink:o}=D(),s=t.links.join(`\r
`),[a,i]=g.exports.useState([-1,!1]);return g.exports.useEffect(()=>{a[1]&&setTimeout(()=>{i([-1,!1])},1e3)},[a]),r(_,{justifyContent:"flex-end",onClick:l=>{l.preventDefault(),l.stopPropagation()},children:[e(_r,{text:t.subscription_url.startsWith("/")?window.location.origin+t.subscription_url:t.subscription_url,onCopy:()=>{i([0,!0])},children:e("div",{children:e(ne,{label:a[0]==0&&a[1]?X("usersTable.copied"):X("usersTable.copyLink"),placement:"top",children:e(Y,{p:"0 !important","aria-label":"copy subscription link",bg:"transparent",_dark:{_hover:{bg:"gray.700"}},size:{base:"sm",md:"md"},children:a[0]==0&&a[1]?e(Pr,{}):e(ea,{})})})})}),e(_r,{text:s,onCopy:()=>{i([1,!0])},children:e("div",{children:e(ne,{label:a[0]==1&&a[1]?X("usersTable.copied"):X("usersTable.copyConfigs"),placement:"top",children:e(Y,{p:"0 !important","aria-label":"copy configs",bg:"transparent",_dark:{_hover:{bg:"gray.700"}},size:{base:"sm",md:"md"},children:a[0]==1&&a[1]?e(Pr,{}):e(Ji,{})})})})}),e(ne,{label:"QR Code",placement:"top",children:e(Y,{p:"0 !important","aria-label":"qr code",bg:"transparent",_dark:{_hover:{bg:"gray.700"}},size:{base:"sm",md:"md"},onClick:()=>{n(t.links),o(t.subscription_url)},children:e(ta,{})})})]})},aa=({isFiltered:t})=>{const{onCreateUser:n}=D();return r(v,{padding:"5",py:"8",display:"flex",alignItems:"center",flexDirection:"column",gap:4,w:"full",children:[e(Xi,{maxHeight:"200px",maxWidth:"200px",_dark:{'path[fill="#fff"]':{fill:"gray.800"},'path[fill="#f2f2f2"], path[fill="#e6e6e6"], path[fill="#ccc"]':{fill:"gray.700"},'circle[fill="#3182CE"]':{fill:"primary.300"}},_light:{'path[fill="#f2f2f2"], path[fill="#e6e6e6"], path[fill="#ccc"]':{fill:"gray.300"},'circle[fill="#3182CE"]':{fill:"primary.500"}}}),e(c,{fontWeight:"medium",color:"gray.600",_dark:{color:"gray.400"},children:t?X("usersTable.noUserMatched"):X("usersTable.noUser")}),!t&&e(N,{size:"sm",colorScheme:"primary",onClick:()=>n(!0),children:X("createUser")})]})},la=()=>(g.exports.useEffect(()=>{D.getState().refetchUsers(),ms()},[]),r(O,{justifyContent:"space-between",minH:"100vh",p:"6",rowGap:4,children:[r(v,{w:"full",children:[e(Ys,{}),e(ds,{mt:"4"}),e(Us,{}),e(ia,{}),e(Bi,{}),e(Is,{}),e(Ei,{}),e(ai,{}),e(Ti,{}),e(Ni,{}),e(Ci,{}),e(vi,{}),e(Ai,{}),e(ks,{}),e(pi,{})]}),e(fo,{})]})),ca=t=>g.exports.createElement("svg",{viewBox:"0 0 747 747",fill:"none",xmlns:"http://www.w3.org/2000/svg",...t},g.exports.createElement("path",{d:"M746.671 209.652V77.6572C746.671 72.9384 743.827 68.6832 739.468 66.8811C735.108 65.0738 730.093 66.0739 726.754 69.4072L579.354 216.807C577.453 202.386 570.203 189.203 559.037 179.87C547.875 170.542 533.615 165.744 519.083 166.432C504.551 167.12 490.807 173.24 480.573 183.583L414.375 249.776C412.417 235.391 405.141 222.25 393.985 212.959C382.823 203.662 368.594 198.88 354.083 199.552C339.578 200.224 325.849 206.302 315.594 216.588L274.343 257.828L19.9167 3.41515C16.5781 0.0818157 11.5625 -0.918184 7.20306 0.889149C2.84373 2.69648 0 6.94648 0 11.6651V143.649C0 146.743 1.22917 149.712 3.41667 151.899L191.843 340.339L3.41667 528.753C1.2292 530.94 0 533.909 0 537.003V668.997C0 673.716 2.84373 677.971 7.20306 679.773C11.5624 681.581 16.578 680.58 19.9167 677.247L167.317 529.847C169.285 544.233 176.561 557.358 187.718 566.65C198.869 575.947 213.098 580.728 227.603 580.066C242.103 579.404 255.838 573.342 266.098 563.071L332.295 496.874V496.879C334.259 511.264 341.535 524.395 352.691 533.686C363.853 542.977 378.082 547.759 392.587 547.092C407.093 546.421 420.822 540.348 431.077 530.066L472.327 488.826L726.754 743.239C728.941 745.427 731.91 746.656 735.004 746.656C736.535 746.661 738.056 746.359 739.473 745.77C743.832 743.963 746.671 739.708 746.671 734.989V602.994C746.671 599.901 745.442 596.932 743.254 594.744L554.827 406.318L743.254 217.904H743.259C745.447 215.717 746.676 212.748 746.676 209.654L746.671 209.652ZM23.3373 39.8118L257.844 274.318L208.328 323.818L23.3413 138.818L23.3373 39.8118ZM723.337 706.825L488.831 472.318L538.347 422.818L723.333 607.818L723.337 706.825ZM23.3373 541.825L332.097 233.078C338.764 226.719 347.624 223.172 356.843 223.172C366.056 223.172 374.916 226.719 381.588 233.078C388.151 239.641 391.838 248.548 391.838 257.828C391.838 267.114 388.151 276.016 381.588 282.577L23.3347 640.831L23.3373 541.825ZM249.604 546.575C240.755 555.419 227.864 558.872 215.781 555.638C203.703 552.398 194.265 542.961 191.027 530.883C187.792 518.8 191.245 505.91 200.089 497.06L497.076 200.074C505.92 191.23 518.811 187.772 530.893 191.011C542.971 194.246 552.409 203.678 555.648 215.76C558.888 227.839 555.434 240.73 546.591 249.572L249.604 546.575ZM723.337 204.815L414.577 513.562C407.911 519.926 399.051 523.473 389.832 523.473C380.618 523.473 371.753 519.926 365.087 513.562C358.529 506.999 354.842 498.098 354.842 488.816C354.842 479.535 358.529 470.634 365.087 464.071L723.34 105.818L723.337 204.815Z",fill:"currentColor"})),da=y.object({username:y.string().min(1,"login.fieldRequired"),password:y.string().min(1,"login.fieldRequired")}),ha=C(ca,{baseStyle:{strokeWidth:"10px",w:12,h:12}}),ua=C(Hn,{baseStyle:{w:5,h:5,strokeWidth:"2px"}}),Or=()=>{var x,h;const[t,n]=g.exports.useState(""),[o,s]=g.exports.useState(!1),a=jn(),{t:i}=B();let l=Bn();const{register:p,formState:{errors:f},handleSubmit:w}=Ge({resolver:et(da)});g.exports.useEffect(()=>{os(),l.pathname!=="/login"&&a("/login",{replace:!0})},[]);const S=m=>{n("");const I=new FormData;I.append("username",m.username),I.append("password",m.password),I.append("grant_type","password"),s(!0),W("/admin/token",{method:"post",body:I}).then(({access_token:b})=>{rs(b),a("/")}).catch(b=>{n(b.response._data.detail)}).finally(s.bind(null,!1))};return r(O,{justifyContent:"space-between",minH:"100vh",p:"6",w:"full",children:[r(v,{w:"full",children:[e(_,{justifyContent:"end",w:"full",children:e(bo,{})}),e(_,{w:"full",justifyContent:"center",alignItems:"center",children:r(v,{w:"full",maxW:"340px",mt:"6",children:[r(O,{alignItems:"center",w:"full",children:[e(ha,{}),e(c,{fontSize:"2xl",fontWeight:"semibold",children:i("login.loginYourAccount")}),e(c,{color:"gray.600",_dark:{color:"gray.400"},children:i("login.welcomeBack")})]}),e(v,{w:"full",maxW:"300px",m:"auto",pt:"4",children:e("form",{onSubmit:w(S),children:r(O,{mt:4,rowGap:2,children:[e(A,{children:e(Fe,{w:"full",placeholder:i("username"),...p("username"),error:i((x=f==null?void 0:f.username)==null?void 0:x.message)})}),e(A,{children:e(Fe,{w:"full",type:"password",placeholder:i("password"),...p("password"),error:i((h=f==null?void 0:f.password)==null?void 0:h.message)})}),t&&r(Ve,{status:"error",rounded:"md",children:[e(Je,{}),e(ro,{children:t})]}),r(N,{isLoading:o,type:"submit",w:"full",colorScheme:"primary",children:[e(ua,{marginRight:1}),i("login")]})]})})})]})})]}),e(fo,{})]})},pa=()=>W("/admin",{headers:{Authorization:`Bearer ${Et()}`}}),ma=$n([{path:"/",element:e(la,{}),errorElement:e(Or,{}),loader:pa},{path:"/login/",element:e(Or,{})}]);function ga(){return e("main",{className:"p-8",children:e(Vn,{router:ma})})}G.extend(Gn);G.extend(Yn);G.extend(Zn);G.extend(Qn);G.extend(qn);ho(Xn.get()||"light");Jn.createRoot(document.getElementById("root")).render(e($t.StrictMode,{children:e(Kn,{theme:ts,children:e(es,{client:Gt,children:e(ga,{})})})}));

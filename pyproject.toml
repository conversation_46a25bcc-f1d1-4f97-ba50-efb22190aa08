[project]
name = "marzban"
version = "1.0.0-beta-1"
description = "Unified GUI Censorship Resistant Solution Powered by Xray"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "APScheduler>=3.11.0",
    "Deprecated==1.2.13",
    "Jinja2>=3.1.6",
    "PyJWT>=2.10.1",
    "PyYAML>=6.0.2",
    "alembic>=1.15.2",
    "anyio==4.2.0",
    "bcrypt==4.0.1",
    "click>=8.1.8",
    "commentjson==0.9.0",
    "cryptography==43.0.1",
    "fastapi>=0.115.12",
    "jdatetime>=5.2.0",
    "passlib==1.7.4",
    "psutil==5.9.4",
    "pyOpenSSL==24.2.1",
    "pydantic>=2.11.3",
    "python-dateutil>=2.8.2",
    "python-decouple>=3.6",
    "python-dotenv>=0.21.1",
    "python-multipart>=0.0.7",
    "qrcode>=8.1",
    "rich>=13.7.1",
    "uvicorn==0.27.0.post1",
    "websocket-client==1.7.0",
    "websockets==12.0",
    "sse-starlette>=2.1.3",
    "gozargah-node-bridge>=0.0.40",
    "httpx>=0.28.1",
    "packaging>=24.2",
    "sqlalchemy[asyncio]>=2.0.40",
    "aiosqlite>=0.21.0",
    "asyncpg>=0.30.0",
    "asyncmy>=0.2.10",
    "aiocache>=0.12.3",
    "textual[syntax]>=2.1.2",
    "aiogram>=3.19.0",
    "aiohttp-socks>=0.10.1",
]

[tool.ruff]
line-length = 120
exclude = ["xray_api", "app/db/migrations"]
fix = true

[tool.ruff.lint]
fixable = ["ALL"]

[tool.ruff.lint.isort]
combine-as-imports = true

[tool.ruff.format]
quote-style = "double"
line-ending = "auto"
docstring-code-format = false
docstring-code-line-length = "dynamic"

[dependency-groups]
dev = [
    "faker>=37.1.0",
    "pytest>=8.3.5",
    "pytest-asyncio>=0.26.0",
    "pytest-watch>=4.2.0",
    "ruff>=0.11.6",
    "sqlalchemy-utils>=0.41.2",
    "textual-dev>=1.7.0",
    "watchfiles>=1.0.4",
]
